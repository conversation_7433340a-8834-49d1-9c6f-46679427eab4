#include "utils.h"

QByteArray Utils::generateHmacSha256(const QByteArray &key, const QByteArray &data)
{
    // 使用HMAC SHA256算法生成签名
    QByteArray hashedData;
    int blockSize = 64; // HMAC-SHA256 block size is 64 bytes
    
    QByteArray keyPadded = key;
    if (keyPadded.length() > blockSize) {
        keyPadded = QCryptographicHash::hash(keyPadded, QCryptographicHash::Sha256);
    }
    
    if (keyPadded.length() < blockSize) {
        keyPadded.append(QByteArray(blockSize - keyPadded.length(), 0));
    }
    
    QByteArray innerPadding(blockSize, char(0x36));
    QByteArray outerPadding(blockSize, char(0x5c));
    
    for (int i = 0; i < blockSize; i++) {
        innerPadding[i] = innerPadding[i] ^ keyPadded[i];
        outerPadding[i] = outerPadding[i] ^ keyPadded[i];
    }
    
    QByteArray innerHash = QCryptographicHash::hash(innerPadding + data, QCryptographicHash::Sha256);
    QByteArray outerHash = QCryptographicHash::hash(outerPadding + innerHash, QCryptographicHash::Sha256);
    
    return outerHash.toHex().toLower();
}

QString Utils::getTimestamp()
{
    // 获取当前时间戳（毫秒）
    qint64 timestamp = QDateTime::currentMSecsSinceEpoch();
    
    // 减去1500毫秒的偏移量，确保时间戳在币安服务器时间的允许范围内
    // 币安API要求时间戳必须在服务器时间的±1000毫秒范围内
    timestamp -= 1500;
    
    return QString::number(timestamp);
}

void Utils::saveApiKeys(const QString &apiKey, const QString &secretKey, bool remember)
{
    QSettings settings("BinanceTrader", "ApiKeys");
    
    if (remember) {
        // 保存API密钥到配置文件
        settings.setValue("apiKey", apiKey);
        settings.setValue("secretKey", secretKey);
        settings.setValue("remember", true);
    } else {
        // 清除保存的API密钥
        settings.remove("apiKey");
        settings.remove("secretKey");
        settings.setValue("remember", false);
    }
}

bool Utils::loadApiKeys(QString &apiKey, QString &secretKey)
{
    QSettings settings("BinanceTrader", "ApiKeys");
    
    bool remember = settings.value("remember", false).toBool();
    if (remember) {
        apiKey = settings.value("apiKey", "").toString();
        secretKey = settings.value("secretKey", "").toString();
        return !apiKey.isEmpty() && !secretKey.isEmpty();
    }
    
    return false;
}

void Utils::clearApiKeys()
{
    QSettings settings("BinanceTrader", "ApiKeys");
    settings.remove("apiKey");
    settings.remove("secretKey");
    settings.setValue("remember", false);
}

bool Utils::hasApiKeys()
{
    QSettings settings("BinanceTrader", "ApiKeys");
    bool remember = settings.value("remember", false).toBool();
    if (remember) {
        QString apiKey = settings.value("apiKey", "").toString();
        QString secretKey = settings.value("secretKey", "").toString();
        return !apiKey.isEmpty() && !secretKey.isEmpty();
    }
    
    return false;
} 