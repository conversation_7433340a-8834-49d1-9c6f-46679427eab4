# 币安量化交易系统

基于币安API的量化交易软件，支持USDT/USDC交易对的套利交易。

## 功能

- 登录界面：输入API Key和Secret Key进行登录
- 账户信息显示：显示USDT和USDC余额
- 套利功能：基于USDT/USDC交易对的套利交易

## 安装和配置

### 解决SSL问题

如果遇到SSL错误（如 `qt.network.ssl: QSslSocket::connectToHostEncrypted: TLS initialization failed`），请按照以下步骤解决：

#### 方法1：使用setup_openssl.ps1脚本（推荐）

1. 以管理员身份运行PowerShell
2. 执行项目根目录下的setup_openssl.ps1脚本：
   ```
   .\setup_openssl.ps1
   ```
3. 脚本会自动下载并安装OpenSSL，然后复制必要的DLL文件到正确的位置

#### 方法2：手动安装OpenSSL

1. 从 https://slproweb.com/products/Win32OpenSSL.html 下载并安装OpenSSL（选择Win64 OpenSSL v3.1.4 Light版本）
2. 安装完成后，从安装目录（通常是C:\Program Files\OpenSSL-Win64\bin）复制以下文件：
   - libcrypto-3-x64.dll
   - libssl-3-x64.dll
3. 将这些文件复制到以下位置：
   - 项目根目录下的ssl文件夹（如果不存在，请创建）
   - 项目的构建目录（通常是build\Debug或build\Release）

#### 方法3：使用Qt提供的OpenSSL

如果您使用的是Qt安装程序安装的Qt，可以在Qt安装目录下找到OpenSSL DLL文件：

1. 找到Qt安装目录下的OpenSSL DLL文件，通常位于：
   - [Qt安装目录]\[版本]\[编译器]\bin
   - 例如：C:\Qt\6.5.0\msvc2019_64\bin
2. 复制以下文件：
   - libcrypto-3-x64.dll（或类似名称）
   - libssl-3-x64.dll（或类似名称）
3. 将这些文件复制到应用程序的可执行文件所在目录

## 使用说明

1. 启动应用程序
2. 在登录界面输入API Key和Secret Key
3. 点击"登录"按钮
4. 登录成功后，可以在主界面查看账户余额和进行套利交易

## 开发说明

- 使用Qt 6.5.0或更高版本
- 使用CMake构建系统
- 使用C++17标准 