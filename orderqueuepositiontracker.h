#ifndef ORDERQUEUEPOSITIONTRACKER_H
#define ORDERQUEUEPOSITIONTRACKER_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include <QString>

// 订单队列位置跟踪类
// 用于跟踪当前买单在买一价订单队列中的相对位置
// 以及卖单在卖一价订单队列中的相对位置
class OrderQueuePositionTracker : public QObject
{
    Q_OBJECT

public:
    // 订单状态枚举
    enum OrderStatus
    {
        Initial,             // 初始状态
        BuyPending,          // 买入挂单
        Arbitraging,         // 套利中
        StopLossFirstDrop    // 第一次止损状态
    };

    // 获取单例实例
    static OrderQueuePositionTracker *getInstance();
    // 释放单例实例
    static void releaseInstance();

    // 重置跟踪状态
    void reset();

    // 获取当前买单相对位置百分比 (0-1)
    double getCurrentPositionPercent() const;

    // 获取买单累计成交金额(USDT)
    double getFilledAmount() const;

    // 获取买单初始队列总金额(USDT)
    double getInitialTotalAmount() const;

    // 获取买单当前队列总金额(USDT)
    double getCurrentTotalAmount() const;

    // 获取我的买单金额(USDT)
    double getMyOrderAmount() const;

    // 获取当前卖单相对位置百分比 (0-1)
    double getSellPositionPercent() const;

    // 获取卖单累计成交金额(USDT)
    double getSellFilledAmount() const;

    // 获取卖单初始队列总金额(USDT)
    double getSellInitialTotalAmount() const;

    // 获取卖单当前队列总金额(USDT)
    double getSellCurrentTotalAmount() const;

    // 获取我的卖单金额(USDT)
    double getMySellOrderAmount() const;

    // 获取当前订单状态
    OrderStatus getOrderStatus() const;

    // 设置当前订单状态
    void setOrderStatus(OrderStatus status);

    // 设置买入订单ID
    void setBuyOrderId(const QString &orderId);

    // 设置卖出订单ID
    void setSellOrderId(const QString &orderId);

    // 获取买入订单ID
    QString getBuyOrderId() const;

    // 获取卖出订单ID
    QString getSellOrderId() const;

    // 设置买入订单时间
    void setBuyOrderTime(const QString &time);

    // 设置卖出订单时间
    void setSellOrderTime(const QString &time);

    // 获取买入订单时间
    QString getBuyOrderTime() const;

    // 获取卖出订单时间
    QString getSellOrderTime() const;

signals:
    // 买单队列位置更新信号
    void positionUpdated(double positionPercent, double filledAmount);

    // 卖单队列位置更新信号
    void sellPositionUpdated(double positionPercent, double filledAmount);

    // 订单状态变化信号
    void orderStatusChanged(OrderStatus status);

public slots:
    // 处理订单簿深度数据更新
    void handleOrderBookDepthUpdated(const QJsonObject &depthData);

    // 处理最新交易数据更新
    void handleLatestTradeUpdated(const QJsonObject &tradeInfo);

    // 设置我的买单信息
    void setMyBuyOrder(const QString &symbol, double price, double quantity);

    // 设置我的卖单信息
    void setMySellOrder(const QString &symbol, double price, double quantity);
    double formatPriceToFixed4Decimals(double price) const;

private:
    // 私有构造和析构函数
    explicit OrderQueuePositionTracker(QObject *parent = nullptr);
    ~OrderQueuePositionTracker();

    // 禁止拷贝构造和赋值操作
    OrderQueuePositionTracker(const OrderQueuePositionTracker &) = delete;
    OrderQueuePositionTracker &operator=(const OrderQueuePositionTracker &) = delete;

    // 从订单簿数据中提取买一价订单总量
    double extractBidVolumeAtPrice(const QJsonArray &bids, double price) const;

    // 从订单簿数据中提取卖一价订单总量
    double extractAskVolumeAtPrice(const QJsonArray &asks, double price) const;

    // 处理买单价格的成交交易
    void processBuyPriceTradeExecution(double price, double quantity, bool isBuyerMaker);

    // 处理卖单价格的成交交易
    void processSellPriceTradeExecution(double price, double quantity, bool isBuyerMaker);

    // 单例实例
    static OrderQueuePositionTracker *m_instance;

    // 交易对
    QString m_symbol;

    // 买入价格
    double m_buyPrice;

    // 买入数量
    double m_buyQuantity;

    // 买入总金额
    double m_buyAmount;

    // 买单初始队列总金额
    double m_initialTotalAmount;

    // 买单当前队列总金额
    double m_currentTotalAmount;

    // 买单累计成交金额
    double m_filledAmount;

    // 买单当前相对位置百分比 (0-1)
    double m_positionPercent;

    // 买单是否已初始化跟踪
    bool m_isInitialized;

    // 卖出价格
    double m_sellPrice;

    // 卖出数量
    double m_sellQuantity;

    // 卖出总金额
    double m_sellAmount;

    // 卖单初始队列总金额
    double m_sellInitialTotalAmount;

    // 卖单当前队列总金额
    double m_sellCurrentTotalAmount;

    // 卖单累计成交金额
    double m_sellFilledAmount;

    // 卖单当前相对位置百分比 (0-1)
    double m_sellPositionPercent;

    // 卖单是否已初始化跟踪
    bool m_sellIsInitialized;

    // 买入订单ID
    QString m_buyOrderId;

    // 卖出订单ID
    QString m_sellOrderId;

    // 买入订单时间
    QString m_buyOrderTime;

    // 卖出订单时间
    QString m_sellOrderTime;

    // 当前订单状态
    OrderStatus m_orderStatus;
};

#endif // ORDERQUEUEPOSITIONTRACKER_H
