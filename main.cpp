#include "mainwindow.h"
#include "loginwindow.h"

#include <QApplication>
#include <QSslSocket>
#include <QDebug>
#include <QDir>
#include <QMetaType>
#include <QFile>
#include <QDateTime>
#include <QTextStream>
#include <QMutex>
#include <QStringConverter>
#include <QEvent>
#include <QAbstractEventDispatcher>
#include <QThread>
#include <QThreadPool>
#include <QNetworkAccessManager>
#include <QWebSocket>
#include <QNetworkReply>
#include <QMessageBox> // 为了处理QMessageBox的事件
#include <QSqlDatabase>
#include <QCoreApplication>

// 定义全局互斥锁，防止多线程同时写入文件
QMutex g_logMutex;

// 声明全局变量，用于跟踪应用程序的线程
QList<QThread *> g_appThreads;

// 自定义消息处理函数
void customMessageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    // 获取当前时间
    QString currentDateTime = QDateTime::currentDateTime().toString("yyyy年MM月dd日hh:mm:ss");

    // 根据消息类型构建不同的前缀
    QString logMsg;
    switch (type)
    {
    case QtDebugMsg:
        logMsg = QString("%1       %2").arg(currentDateTime).arg(msg);
        break;
    case QtInfoMsg:
        logMsg = QString("%1       [信息] %2").arg(currentDateTime).arg(msg);
        break;
    case QtWarningMsg:
        logMsg = QString("%1       [警告] %2").arg(currentDateTime).arg(msg);
        break;
    case QtCriticalMsg:
        logMsg = QString("%1       [错误] %2").arg(currentDateTime).arg(msg);
        break;
    case QtFatalMsg:
        logMsg = QString("%1       [致命] %2").arg(currentDateTime).arg(msg);
        break;
    }

    // 加锁，确保多线程环境下的安全写入
    QMutexLocker locker(&g_logMutex);

    // 打开文件进行追加写入
    QFile logFile("debug.txt");

    if (logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text))
    {
        QTextStream stream(&logFile);
        // Qt 6中不再使用setCodec，而是使用setEncoding方法
        stream.setEncoding(QStringConverter::Utf8);
        stream << logMsg << Qt::endl;
        logFile.close();
    }

    // 同时在控制台输出（可选）
    fprintf(stderr, "%s\n", qPrintable(logMsg));
}

// 全局事件过滤器，用于监听应用程序退出事件
class AppEventFilter : public QObject
{
public:
    explicit AppEventFilter(QObject *parent = nullptr) : QObject(parent) {}

    bool eventFilter(QObject *watched, QEvent *event) override
    {
        // 添加调试信息，帮助识别收到的事件
        if (event->type() == QEvent::Close)
        {
            QString objectType = watched->metaObject()->className();
            qDebug() << "收到Close事件，对象类型:" << objectType;

            // 明确忽略QMessageBox和对话框的关闭事件
            if (qobject_cast<QMessageBox *>(watched) || objectType.contains("Dialog"))
            {
                qDebug() << "忽略消息框或对话框的关闭事件";
                return false; // 继续传递事件，不要拦截
            }
        }

        // 只监听主窗口或登录窗口的关闭事件，而不是所有关闭事件
        if (event->type() == QEvent::Quit ||
            (event->type() == QEvent::Close && (qobject_cast<MainWindow *>(watched) || qobject_cast<LoginWindow *>(watched))))
        {
            qDebug() << "应用程序主窗口即将关闭，执行清理操作...";

            // 这里执行任何必要的清理操作
            // 确保日志文件被正确关闭
            QMutexLocker locker(&g_logMutex);
            QFile logFile("debug.txt");
            if (logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text))
            {
                QTextStream stream(&logFile);
                stream.setEncoding(QStringConverter::Utf8);
                stream << QDateTime::currentDateTime().toString("yyyy年MM月dd日hh:mm:ss") << "       应用程序正常关闭" << Qt::endl;
                logFile.close();
            }
        }

        // 继续传递事件给其他过滤器
        return QObject::eventFilter(watched, event);
    }
};

// 强制终止所有线程的函数
void forceQuitAllThreads()
{
    qDebug() << "强制终止所有线程...";

    // 等待所有注册的线程退出
    for (QThread *thread : g_appThreads)
    {
        if (thread && thread->isRunning())
        {
            qDebug() << "正在终止线程...";
            thread->quit();
            if (!thread->wait(3000))
            { // 等待3秒
                qDebug() << "线程未响应，强制终止";
                thread->terminate();
                thread->wait(); // 等待线程结束
            }
        }
    }

    // 等待线程池中的所有线程
    QThreadPool::globalInstance()->waitForDone(3000); // 等待3秒

    qDebug() << "所有线程已终止";
}

// 取消所有进行中的网络请求
void cancelAllPendingRequests()
{
    qDebug() << "正在取消所有进行中的网络请求...";

    // 查找所有QNetworkReply对象
    QList<QNetworkReply *> allReplies = QApplication::instance()->findChildren<QNetworkReply *>();
    for (QNetworkReply *reply : allReplies)
    {
        if (reply->isRunning())
        {
            qDebug() << "取消进行中的网络请求: " << reply->url().toString();
            reply->abort(); // 中断请求
        }
    }

    qDebug() << "所有网络请求已取消";
}

// 确保关闭所有网络连接
void forceCloseAllNetworkConnections()
{
    qDebug() << "强制关闭所有网络连接...";

    // 中断所有WebSocket连接 - 使用安全的延迟删除机制
    QList<QWebSocket *> allWebSockets = QApplication::instance()->findChildren<QWebSocket *>();
    for (QWebSocket *ws : allWebSockets)
    {
        if (ws)
        {
            // 断开所有信号连接
            ws->disconnect();

            // 安排延迟删除，避免直接操作可能引起的跨线程问题
            ws->deleteLater();

            qDebug() << "WebSocket连接已安排删除";
        }
    }

    // 中断所有网络请求 - 使用安全的延迟删除机制
    QList<QNetworkReply *> allReplies = QApplication::instance()->findChildren<QNetworkReply *>();
    for (QNetworkReply *reply : allReplies)
    {
        if (reply)
        {
            // 断开所有信号连接
            reply->disconnect();

            // 安排延迟删除，避免直接操作可能引起的跨线程问题
            reply->deleteLater();

            qDebug() << "网络请求已安排删除";
        }
    }

    // 中断所有网络访问管理器 - 断开连接但不删除
    QList<QNetworkAccessManager *> allNetworkManagers = QApplication::instance()->findChildren<QNetworkAccessManager *>();
    for (QNetworkAccessManager *nam : allNetworkManagers)
    {
        if (nam)
        {
            // 断开所有信号连接
            nam->disconnect();

            qDebug() << "网络管理器连接已断开";
        }
    }

    qDebug() << "所有网络连接已安排关闭";
}

// 应用程序即将退出时的清理函数
void appCleanup()
{
    qDebug() << "应用程序即将退出，执行清理...";

    // 取消所有进行中的网络请求
    cancelAllPendingRequests();

    // 强制关闭所有网络连接
    forceCloseAllNetworkConnections();

    // 给网络连接和请求一些时间来完成关闭
    QThread::msleep(500); // 等待500毫秒
    qDebug() << "等待网络连接关闭完成...";

    // 再次检查并强制关闭所有网络连接
    forceCloseAllNetworkConnections();

    // 强制终止所有线程
    forceQuitAllThreads();

    // 写入日志
    QMutexLocker locker(&g_logMutex);
    QFile logFile("debug.txt");
    if (logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text))
    {
        QTextStream stream(&logFile);
        stream.setEncoding(QStringConverter::Utf8);
        stream << QDateTime::currentDateTime().toString("yyyy年MM月dd日hh:mm:ss") << "       应用程序清理完成，即将退出" << Qt::endl;
        logFile.close();
    }
}

// 程序即将结束时调用的函数
void cleanupOnExit()
{
    qDebug() << "正在执行退出前清理...";

    // 在这里添加任何需要在程序退出前执行的清理代码
    // 例如，确保所有文件已关闭、资源已释放等

    // 写入最后的日志记录
    QMutexLocker locker(&g_logMutex);
    QFile logFile("debug.txt");
    if (logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text))
    {
        QTextStream stream(&logFile);
        stream.setEncoding(QStringConverter::Utf8);
        stream << QDateTime::currentDateTime().toString("yyyy年MM月dd日hh:mm:ss") << "       应用程序退出清理完成" << Qt::endl;
        logFile.close();
    }
}

// 注册线程函数，用于跟踪应用程序创建的线程
void registerAppThread(QThread *thread)
{
    if (thread)
    {
        g_appThreads.append(thread);
        qDebug() << "注册线程，当前线程数:" << g_appThreads.size();
    }
}

int main(int argc, char *argv[])
{
    // 设置高DPI支持 - 由于Qt 6已默认启用高DPI支持，移除这些已弃用的设置
    // QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    // QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);

    // 设置Qt插件路径，确保SQLite驱动能被找到
    QCoreApplication::addLibraryPath("D:/StudyAndWork/Qt/6.5.3/msvc2019_64/plugins");

    // 检查SQLite驱动是否可用
    qDebug() << "可用的数据库驱动:" << QSqlDatabase::drivers();

    // 打开文件进行追加写入
    QFile logFile("debug.txt");

    if (logFile.open(QIODevice::WriteOnly))
    {
        logFile.write("");
        logFile.close();
    }

    // 创建应用程序对象
    QApplication app(argc, argv);

    // 注册自定义消息处理函数
    qInstallMessageHandler(customMessageHandler);

    // 创建并安装全局事件过滤器
    AppEventFilter *eventFilter = new AppEventFilter(&app);
    app.installEventFilter(eventFilter);

    // 注册退出清理函数
    qAddPostRoutine(cleanupOnExit);

    // 连接应用程序的aboutToQuit信号
    QObject::connect(&app, &QApplication::aboutToQuit, []()
                     {
        qDebug() << "应用程序即将退出，执行最终清理...";
        appCleanup(); });

    // 检查SSL支持
    if (!QSslSocket::supportsSsl())
    {
        qDebug() << "警告：SSL支持不可用，这可能会影响API连接";
        qDebug() << "OpenSSL版本：" << QSslSocket::sslLibraryVersionString();
    }
    else
    {
        qDebug() << "SSL支持可用，OpenSSL版本：" << QSslSocket::sslLibraryVersionString();
    }

    // 创建登录窗口
    LoginWindow loginWindow;
    loginWindow.show();

    // 运行应用程序
    int result = app.exec();

    // 应用程序退出前的清理工作
    qDebug() << "应用程序正在退出，执行最终清理...";

    // 强制清理所有资源
    appCleanup();

    // 等待所有线程完成
    QThreadPool::globalInstance()->waitForDone();

    // 确保所有事件都被处理
    QCoreApplication::processEvents();

    qDebug() << "应用程序清理完成，即将退出";

    return result;
}
