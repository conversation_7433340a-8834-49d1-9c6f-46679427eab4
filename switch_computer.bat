@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo        Qt Environment Switcher
echo ========================================
echo.
echo Please select your computer configuration:
echo 1. Current Computer (Z: drive)
echo 2. Huawei Computer (D: drive)
echo.
set /p choice="Enter your choice (1-2): "

if "!choice!"=="1" (
    set computer_type=current_computer
    set qt_path=Z:/StudyAndWork/Qt/6.5.3/msvc2019_64
    echo Switching to Current Computer configuration...
) else if "!choice!"=="2" (
    set computer_type=huawei_computer
    set qt_path=D:/StudyAndWork/Qt/6.5.3/msvc2019_64
    echo Switching to Huawei Computer configuration...
) else (
    echo Invalid choice!
    pause
    exit /b 1
)

echo.
echo Selected: !computer_type!
echo Qt Path: !qt_path!
echo.

echo Updating configuration file...
echo {> computer_config.json
echo   "qt_version": "6.5.3",>> computer_config.json
echo   "qt_paths": {>> computer_config.json
echo     "current_computer": "Z:/StudyAndWork/Qt",>> computer_config.json
echo     "huawei_computer": "D:/StudyAndWork/Qt">> computer_config.json
echo   },>> computer_config.json
echo   "qt_kit": "msvc2019_64",>> computer_config.json
echo   "compiler_paths": {>> computer_config.json
echo     "current_computer": "system",>> computer_config.json
echo     "huawei_computer": "system">> computer_config.json
echo   },>> computer_config.json
echo   "build_system": "cmake",>> computer_config.json
echo   "current_config": "!computer_type!">> computer_config.json
echo }>> computer_config.json

echo Updating VSCode configuration files...

echo Updating c_cpp_properties.json...
powershell -Command "$qtPath='!qt_path!'; (Get-Content '.vscode\c_cpp_properties.json') -replace 'Z:/StudyAndWork/Qt/6.5.3/msvc2019_64', $qtPath -replace 'D:/StudyAndWork/Qt/6.5.3/msvc2019_64', $qtPath | Set-Content '.vscode\c_cpp_properties.json'"

echo Updating settings.json...
powershell -Command "$qtPath='!qt_path!'; (Get-Content '.vscode\settings.json') -replace 'Z:/StudyAndWork/Qt/6.5.3/msvc2019_64', $qtPath -replace 'D:/StudyAndWork/Qt/6.5.3/msvc2019_64', $qtPath | Set-Content '.vscode\settings.json'"

echo Updating launch.json...
powershell -Command "$qtPath='!qt_path!'; (Get-Content '.vscode\launch.json') -replace 'Z:/StudyAndWork/Qt/6.5.3/msvc2019_64', $qtPath -replace 'D:/StudyAndWork/Qt/6.5.3/msvc2019_64', $qtPath | Set-Content '.vscode\launch.json'"

echo.
echo ========================================
echo Configuration switched successfully!
echo ========================================
echo.
echo Current Qt Path: !qt_path!
echo.
echo Please restart VSCode to apply the changes.
echo.
pause
