# Qt VSCode 快速环境配置

## 📋 配置概述

这是一个简化的Qt VSCode环境配置，专注于快速构建功能：
- **Ctrl+R**: 选择Debug/Release构建方式
- **Ctrl+B**: 选择Debug/Release构建
- 支持CMake和QMake两种构建系统
- 支持MinGW和MSVC两种编译器
- 支持多台电脑间切换
- 自动识别项目文件和源码结构

## 🔧 配置参数

### Qt环境配置（多电脑支持）
```json
{
  "qt_version": "6.5.3",
  "qt_paths": {
    "current_computer": "Z:/StudyAndWork/Qt",
    "huawei_computer": "D:/StudyAndWork/Qt"
  },
  "qt_kit": "msvc2019_64",
  "compiler_paths": {
    "current_computer": "system",
    "huawei_computer": "system"
  }
}
```

### 构建系统选择
```json
{
  "build_system": "cmake"
}
```
> **可选值**: `"qmake"` 或 `"cmake"`

## 📝 填写说明

### 1. `qt_version` - Qt版本号
- 填写您安装的Qt版本（两台电脑应该使用相同版本）
- **示例**: `"5.12.5"`, `"6.5.3"`, `"6.7.0"`, `"5.15.2"` 等

### 2. `qt_paths` - 不同电脑的Qt安装路径
- **current_computer**: 当前电脑（Z盘）的Qt安装根目录
- **huawei_computer**: 华为电脑（D盘）的Qt安装根目录
- **示例**: 
  ```json
  "qt_paths": {
    "current_computer": "Z:/StudyAndWork/Qt",
    "huawei_computer": "D:/StudyAndWork/Qt"
  }
  ```

### 3. `qt_kit` - Qt编译套件
- 查看 `{qt_path}/{qt_version}/` 目录下有哪些文件夹，选择您要使用的
- **两台电脑应该使用相同的套件**
- **常见选项**:
  - **MinGW 64位**: `"mingw_64"`
  - **MinGW 32位**: `"mingw_32"`
  - **MSVC 2019 64位**: `"msvc2019_64"`
  - **MSVC 2017 64位**: `"msvc2017_64"`
  - **MSVC 2022 64位**: `"msvc2022_64"`

### 4. `compiler_paths` - 不同电脑的编译器工具路径
#### 如果选择MinGW套件：
```json
"compiler_paths": {
  "current_computer": "Z:/StudyAndWork/Qt/Tools/mingw1120_64",
  "huawei_computer": "D:/StudyAndWork/Qt/Tools/mingw1120_64"
}
```

#### 如果选择MSVC套件：
- **填写 `"system"`**：让系统自动查找MSVC编译器
```json
"compiler_paths": {
  "current_computer": "system",
  "huawei_computer": "system"
}
```

### 5. `build_system` - 构建系统
- **QMake**: `"qmake"` - 使用 .pro 项目文件
- **CMake**: `"cmake"` - 使用 CMakeLists.txt 项目文件

## ⚠️ 重要技术说明

### 批处理脚本优化
生成的脚本会包含以下优化：
- **变量延迟展开**: 使用 `!variable!` 而不是 `%variable%`
- **PowerShell变量传递**: 使用 `$qtPath='!qt_path!'` 正确传递路径
- **英文注释**: 避免编码问题，确保兼容性
- **错误处理**: 完善的错误检测和提示

### VSCode任务配置
生成的任务会包含详细的描述信息：
- **Ctrl+R任务**: 提供构建类型选择菜单，包含详细说明
- **构建任务**: 每个任务都有明确的用途描述
- **快捷键绑定**: 自动配置Ctrl+R和Ctrl+B快捷键

## 🔍 如何确定正确的路径

### 步骤1：在当前电脑查看Qt目录结构
```
Z:/StudyAndWork/Qt/                    ← current_computer路径
├── 6.5.3/                           ← qt_version
│   ├── mingw_64/                     ← qt_kit选项
│   ├── msvc2019_64/                  ← qt_kit选项
│   └── android_xxx/
├── Tools/
│   ├── mingw1120_64/                 ← current_computer的compiler_path
│   ├── mingw900_32/                  
│   └── QtCreator/
└── 其他版本目录...
```

### 步骤2：在华为电脑查看Qt目录结构
```
D:/StudyAndWork/Qt/                    ← huawei_computer路径
├── 6.5.3/                           ← 相同的qt_version
│   ├── mingw_64/                     ← 相同的qt_kit
│   ├── msvc2019_64/                  
│   └── android_xxx/
├── Tools/
│   ├── mingw1120_64/                 ← huawei_computer的compiler_path
│   ├── mingw900_32/                  
│   └── QtCreator/
└── 其他版本目录...
```

### 步骤3：确保两台电脑配置一致
- ✅ **Qt版本相同**: 两台电脑使用相同的Qt版本
- ✅ **编译套件相同**: 两台电脑使用相同的qt_kit
- ✅ **编译器版本相同**: Tools目录下的编译器版本一致

## 📋 配置示例

### 示例1：Qt 6.5.3 + MinGW 64位（两台电脑）
```json
{
  "qt_version": "6.5.3",
  "qt_paths": {
    "current_computer": "Z:/StudyAndWork/Qt",
    "huawei_computer": "D:/StudyAndWork/Qt"
  },
  "qt_kit": "mingw_64",
  "compiler_paths": {
    "current_computer": "Z:/StudyAndWork/Qt/Tools/mingw1120_64",
    "huawei_computer": "D:/StudyAndWork/Qt/Tools/mingw1120_64"
  },
  "build_system": "qmake"
}
```

### 示例2：Qt 5.12.5 + MinGW 32位（两台电脑）
```json
{
  "qt_version": "5.12.5",
  "qt_paths": {
    "current_computer": "Z:/StudyAndWork/Qt5.12.5",
    "huawei_computer": "D:/StudyAndWork/Qt5.12.5"
  },
  "qt_kit": "mingw73_32",
  "compiler_paths": {
    "current_computer": "Z:/StudyAndWork/Qt5.12.5/Tools/mingw730_32",
    "huawei_computer": "D:/StudyAndWork/Qt5.12.5/Tools/mingw730_32"
  },
  "build_system": "cmake"
}
```

### 示例3：Qt 6.5.3 + MSVC 2019 64位（两台电脑）
```json
{
  "qt_version": "6.5.3",
  "qt_paths": {
    "current_computer": "Z:/StudyAndWork/Qt",
    "huawei_computer": "D:/StudyAndWork/Qt"
  },
  "qt_kit": "msvc2019_64",
  "compiler_paths": {
    "current_computer": "system",
    "huawei_computer": "system"
  },
  "build_system": "qmake"
}
```

## 🚀 使用方法

### 1. 配置步骤
1. 将此MD文件放到您的Qt项目根目录
2. 根据上面的说明填写两台电脑的配置参数
3. 确保项目中有 `.pro` 文件（qmake）或 `CMakeLists.txt` 文件（cmake）
4. 告诉AI："请根据这个配置帮我设置Qt环境"

### 2. 电脑切换
配置完成后，会自动生成 `switch_computer.bat` 脚本：
```bash
# 运行切换脚本
switch_computer.bat

# 选择目标电脑配置
# 重启VSCode应用更改
```

### 3. 快捷键操作
- **Ctrl+R**: 弹出构建类型选择菜单，包含以下选项：
  - `Qt: Select Build Type` - ⭐ 交互式选择构建类型 - 日常开发首选
  - `Qt: Build Debug` - ⭐ 一键构建并运行 Debug 版本 - 日常开发
  - `Qt: Build Release` - ⭐ 一键构建并运行 Release 版本 - 发布测试
  - `Qt: Clean Debug` - 🧹 清理 Debug 构建文件
  - `Qt: Clean Release` - 🧹 清理 Release 构建文件
- **Ctrl+B**: 使用默认构建类型（Debug）构建
- **F5**: 启动调试

### 4. 命令行操作
```bash
# 交互式选择构建类型
qt_build.bat

# 直接构建Debug版本
qt_build.bat debug

# 直接构建Release版本
qt_build.bat release

# 清理构建文件
qt_build.bat clean
```

## 📁 自动生成的目录结构

```
QtProject/
├── .vscode/                 # VSCode配置（自动生成）
│   ├── c_cpp_properties.json
│   ├── launch.json
│   ├── settings.json
│   ├── tasks.json
│   └── keybindings.json
├── build/                   # 构建输出（自动创建）
│   ├── debug/
│   └── release/
├── generated_files/         # Qt生成文件（自动创建）
│   ├── moc/
│   ├── ui/
│   └── rcc/
├── [您的源码文件]           # 当前目录的所有源文件
├── CMakeLists.txt          # CMake项目文件（如果选择cmake）
├── *.pro                   # QMake项目文件（如果选择qmake）
├── qt_build.bat           # 构建脚本（自动生成）
├── switch_computer.bat    # 电脑切换脚本（自动生成）
└── computer_config.json   # 电脑配置记录（自动生成）
```

## ✅ 配置完成后的功能

- ✅ **Ctrl+R** 快速选择构建类型（带详细说明）
- ✅ **Ctrl+B** 快速构建
- ✅ **F5** 一键调试
- ✅ **电脑切换** 一键切换不同电脑的Qt环境
- ✅ **批处理脚本优化** 变量延迟展开和PowerShell兼容
- ✅ IntelliSense 代码补全
- ✅ UI文件自动生成到 `generated_files/` 目录
- ✅ 构建输出分离到 `build/debug/` 和 `build/release/`
- ✅ 自动识别项目名称和源文件
- ✅ 支持MinGW和MSVC编译器
- ✅ 自动配置编译器环境
- ✅ 两台电脑间无缝切换

## 💡 多电脑使用流程

1. **初次配置**: 在一台电脑上配置好环境
2. **同步项目**: 将整个项目文件夹同步到另一台电脑
3. **切换环境**: 在另一台电脑上运行 `switch_computer.bat`
4. **选择配置**: 选择对应的电脑配置
5. **重启VSCode**: 应用新的环境配置

## 🔧 技术改进说明

### 批处理脚本改进
- **变量延迟展开**: 将 `%variable%` 改为 `!variable!`
- **PowerShell变量传递**: 使用 `$qtPath='!qt_path!'` 正确传递路径
- **英文注释**: 移除中文注释，避免编码问题
- **错误处理**: 完善的错误检测和用户友好提示

### VSCode任务改进
- **详细描述**: 每个任务都有明确的用途说明
- **图标标识**: 使用 ⭐ 和 🧹 等图标区分任务类型
- **快捷键优化**: Ctrl+R提供完整的构建选项菜单

---

**填写两台电脑的Qt路径，一次配置，两台电脑无缝切换！** 🎉