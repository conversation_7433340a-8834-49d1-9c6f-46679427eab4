^D:\STUDYANDWORK\QTPROJECTS\BINANCE\CMAKEFILES\F5B512055DBA2050FBFB66FB582B4F39\GENERATE.STAMP.RULE
setlocal
D:\StudyAndWork\Qt\Tools\CMake_64\bin\cmake.exe -SD:/StudyAndWork/QtProjects/Binance -BD:/StudyAndWork/QtProjects/Binance --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/StudyAndWork/QtProjects/Binance/Binance.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
