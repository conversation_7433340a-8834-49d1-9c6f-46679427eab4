# 币安量化交易系统微信小程序设计文档

## 1. 系统概述

### 1.1 设计背景
币安量化交易系统目前已经具备基本的套利功能和用户界面，为了满足用户的远程操作需求，扩展系统的可用性，我们计划开发一个配套的微信小程序，使用户能够通过移动设备远程监控和控制交易系统。

### 1.2 系统目标
- 实现桌面客户端与微信小程序的实时双向通信
- 通过微信小程序远程监控交易系统状态
- 通过微信小程序远程执行交易操作
- 保持桌面客户端与微信小程序界面的同步更新
- 确保系统的安全性和稳定性

### 1.3 适用范围
本设计适用于币安量化交易系统的桌面客户端与微信小程序的开发和实现。

## 2. 系统架构设计

### 2.1 总体架构
系统采用客户端-服务器-小程序三层架构：
1. **桌面客户端层**：基于Qt的币安量化交易系统，是系统的核心，负责交易逻辑和主要数据处理
2. **通信服务器层**：负责桌面客户端与微信小程序之间的数据传输和转发
3. **微信小程序层**：提供移动端用户界面，用于远程监控和控制

```
+------------------+      +------------------+      +------------------+
|                  |      |                  |      |                  |
|  微信小程序(WxMP) |<---->|  通信服务器(Srv) |<---->|  桌面客户端(Qt)  |
|                  |      |                  |      |                  |
+------------------+      +------------------+      +------------------+
```

### 2.2 通信架构
系统使用WebSocket技术实现实时双向通信：
1. 桌面客户端与通信服务器之间建立WebSocket连接
2. 微信小程序与通信服务器之间建立WebSocket连接
3. 通信服务器作为中间层，负责消息的转发和处理

### 2.3 安全架构
为保障系统安全，采取以下措施：
1. 客户端与服务器之间采用TLS/SSL加密通信
2. 实现基于JWT(JSON Web Token)的身份验证机制
3. 所有敏感操作需要二次验证
4. API请求采用签名机制防止篡改
5. 设置操作权限级别，限制敏感操作的执行

## 3. 功能模块设计

### 3.1 桌面客户端模块

#### 3.1.1 WebSocket通信模块
- **功能描述**：负责与通信服务器建立和维护WebSocket连接，发送和接收消息
- **主要类**：`WsClient`
- **核心方法**：
  - `connectToServer()`: 连接服务器
  - `sendMessage(MessageType type, QJsonObject data)`: 发送消息
  - `handleMessage(QJsonObject message)`: 处理接收到的消息

#### 3.1.2 消息处理模块
- **功能描述**：处理来自微信小程序的命令，调用相应的本地方法，并将结果返回
- **主要类**：`MessageHandler`
- **核心方法**：
  - `processCommand(const QString &command, const QJsonObject &params)`: 处理命令
  - `executeTradeOperation(const QJsonObject &params)`: 执行交易操作
  - `getSystemStatus()`: 获取系统状态

#### 3.1.3 状态同步模块
- **功能描述**：将桌面客户端的状态变化实时同步到微信小程序
- **主要类**：`StatusSynchronizer`
- **核心方法**：
  - `syncAccountInfo()`: 同步账户信息
  - `syncOrderStatus()`: 同步订单状态
  - `syncPriceInfo()`: 同步价格信息
  - `syncArbitrageStatus()`: 同步套利状态

#### 3.1.4 安全验证模块
- **功能描述**：验证微信小程序发送的操作请求的合法性
- **主要类**：`SecurityValidator`
- **核心方法**：
  - `validateToken(const QString &token)`: 验证令牌
  - `verifyOperation(const QString &operation, const QJsonObject &params)`: 验证操作

### 3.2 通信服务器模块

#### 3.2.1 WebSocket服务模块
- **功能描述**：管理WebSocket连接，处理连接的建立、维护和关闭
- **技术选型**：Node.js + ws/socket.io

#### 3.2.2 消息转发模块
- **功能描述**：将消息在桌面客户端和微信小程序之间进行转发
- **消息格式**：
  ```json
  {
    "type": "command/response/notification",
    "action": "操作类型",
    "data": {
      // 具体数据
    },
    "timestamp": **********,
    "token": "JWT令牌"
  }
  ```

#### 3.2.3 会话管理模块
- **功能描述**：管理用户会话，包括认证、会话维护和超时处理
- **会话标识**：使用UUID作为会话ID

#### 3.2.4 日志记录模块
- **功能描述**：记录系统运行日志，包括连接状态、消息传递和错误信息

### 3.3 微信小程序模块

#### 3.3.1 用户界面模块
- **功能描述**：提供与桌面客户端相似的用户界面，支持远程监控和操作
- **主要页面**：
  - 登录页面
  - 账户信息页面
  - 价格监控页面
  - 订单管理页面
  - 套利设置页面
  - 历史记录页面

#### 3.3.2 WebSocket通信模块
- **功能描述**：与通信服务器建立和维护WebSocket连接，发送和接收消息
- **核心方法**：
  - `connectSocket()`: 连接WebSocket服务器
  - `sendMessage(type, action, data)`: 发送消息
  - `onMessage(callback)`: 处理接收到的消息

#### 3.3.3 数据管理模块
- **功能描述**：管理从服务器接收的数据，更新本地状态和界面
- **数据存储**：使用微信小程序的本地存储和全局状态管理

#### 3.3.4 安全认证模块
- **功能描述**：处理用户身份验证，生成和管理安全令牌
- **认证流程**：
  1. 用户在小程序中输入桌面客户端生成的配对码
  2. 将配对码发送到服务器进行验证
  3. 验证成功后，服务器生成JWT令牌返回给小程序
  4. 小程序在后续请求中使用该令牌

## 4. 数据设计

### 4.1 通信数据格式

#### 4.1.1 命令消息
```json
{
  "type": "command",
  "action": "placeOrder",
  "data": {
    "symbol": "USDCUSDT",
    "side": "BUY",
    "type": "LIMIT",
    "quantity": "100",
    "price": "0.9998"
  },
  "timestamp": **********,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### 4.1.2 响应消息
```json
{
  "type": "response",
  "action": "placeOrder",
  "requestId": "uuid-request-id",
  "data": {
    "success": true,
    "orderId": "12345678",
    "message": "下单成功"
  },
  "timestamp": 1629789605
}
```

#### 4.1.3 通知消息
```json
{
  "type": "notification",
  "action": "orderStatusChanged",
  "data": {
    "orderId": "12345678",
    "status": "FILLED",
    "filledQuantity": "100",
    "filledPrice": "0.9998"
  },
  "timestamp": 1629789610
}
```

### 4.2 状态同步数据

#### 4.2.1 账户信息
```json
{
  "usdtBalance": "1000.50",
  "usdcBalance": "500.25",
  "usdtFreeBalance": "800.30",
  "usdcFreeBalance": "300.15",
  "usdtLockedBalance": "200.20",
  "usdcLockedBalance": "200.10"
}
```

#### 4.2.2 价格信息
```json
{
  "symbol": "USDCUSDT",
  "bidPrice": "0.9998",
  "askPrice": "1.0001",
  "latestPrice": "0.9999",
  "isBuyerMaker": false,
  "timestamp": 1629789615
}
```

#### 4.2.3 订单状态
```json
{
  "orderId": "12345678",
  "symbol": "USDCUSDT",
  "side": "BUY",
  "type": "LIMIT",
  "price": "0.9998",
  "origQty": "100",
  "executedQty": "100",
  "status": "FILLED",
  "time": 1629789620,
  "arbitrageStatus": "ARBITRAGING"
}
```

#### 4.2.4 套利状态
```json
{
  "status": "ARBITRAGING",
  "buyOrderId": "12345678",
  "buyPrice": "0.9998",
  "buyQuantity": "100",
  "buyOrderTime": 1629789625,
  "sellOrderId": "",
  "targetSellPrice": "1.0005",
  "profit": "0.07",
  "profitRate": "0.07%"
}
```

## 5. 界面设计

### 5.1 微信小程序界面设计

#### 5.1.1 登录页面
- 输入服务器地址和验证码
- 显示连接状态
- 登录按钮

#### 5.1.2 主界面
- 顶部导航栏：显示当前页面名称和连接状态
- 底部标签栏：账户、交易、套利、历史、设置

#### 5.1.3 账户信息页面
- 显示USDT和USDC余额（总额、可用、锁定）
- 账户信息刷新按钮
- 显示API连接状态

#### 5.1.4 价格监控页面
- 显示当前买一价、卖一价和最新成交价
- 显示价格变动趋势（上涨/下跌箭头和百分比）
- 显示市场强弱指标
- 显示买卖订单队列位置（当有挂单时）

#### 5.1.5 订单管理页面
- 当前订单信息显示（订单ID、价格、数量、状态等）
- 下单表单（数量输入、自动套利选项等）
- 下单和结束套利按钮
- 订单状态图标和进度条

#### 5.1.6 历史记录页面
- 历史订单列表，包含：
  - 订单时间
  - 买入/卖出价格
  - 交易量
  - 利润率
  - 状态
- 筛选和排序选项

#### 5.1.7 设置页面
- 服务器连接设置
- 推送通知设置
- 安全设置（二次验证等）
- 关于和帮助信息

### 5.2 界面交互流程

#### 5.2.1 登录流程
1. 用户打开微信小程序
2. 输入服务器地址和验证码
3. 点击登录按钮，建立WebSocket连接
4. 连接成功后跳转到主界面

#### 5.2.2 下单流程
1. 用户进入订单管理页面
2. 输入交易数量和价格
3. 点击下单按钮
4. 弹出确认对话框
5. 确认后发送下单命令到桌面客户端
6. 接收下单结果并更新界面

#### 5.2.3 监控套利流程
1. 买单成交后自动跳转到套利监控页面
2. 显示当前套利状态和进度
3. 实时更新价格和队列位置信息
4. 套利完成或用户主动结束套利时更新状态

## 6. 接口设计

### 6.1 WebSocket接口

#### 6.1.1 连接建立
- 端点：`ws://[服务器地址]:[端口]/ws`
- 握手参数：
  ```
  {
    "clientType": "desktop/wxmp",
    "clientId": "唯一设备标识",
    "version": "1.0.0"
  }
  ```

#### 6.1.2 命令接口
| 命令名称 | 描述 | 参数 |
|---------|------|------|
| connect | 建立连接 | {clientId, verifyCode} |
| getStatus | 获取系统状态 | {} |
| getAccountInfo | 获取账户信息 | {} |
| getPriceInfo | 获取价格信息 | {symbol} |
| placeOrder | 下单 | {symbol, side, type, quantity, price} |
| cancelOrder | 取消订单 | {symbol, orderId} |
| startArbitrage | 开始套利 | {settings} |
| stopArbitrage | 停止套利 | {} |
| getOrderHistory | 获取历史订单 | {startTime, endTime, limit} |

#### 6.1.3 通知接口
| 通知名称 | 描述 | 数据 |
|---------|------|------|
| connectionStatus | 连接状态变化 | {status, message} |
| accountInfoUpdated | 账户信息更新 | {账户信息数据} |
| priceUpdated | 价格信息更新 | {价格信息数据} |
| orderStatusChanged | 订单状态变化 | {订单状态数据} |
| arbitrageStatusChanged | 套利状态变化 | {套利状态数据} |
| queuePositionUpdated | 队列位置更新 | {positionPercent, filledAmount} |
| operationResult | 操作结果 | {success, message, data} |

### 6.2 安全接口

#### 6.2.1 认证接口
- 生成配对码：桌面客户端生成临时配对码
- 验证配对码：微信小程序提交配对码进行验证
- 刷新令牌：更新过期的JWT令牌

#### 6.2.2 安全校验接口
- 签名验证：验证请求数据签名
- 操作权限检查：检查操作的权限级别
- 敏感操作二次验证：需要额外验证码的操作

## 7. 安全性设计

### 7.1 身份验证机制
1. **初始配对**：
   - 桌面客户端生成8位随机配对码，显示给用户
   - 用户在微信小程序中输入配对码
   - 服务器验证配对码，建立设备与账户的绑定关系

2. **会话管理**：
   - 使用JWT令牌进行会话认证
   - 令牌包含设备ID、用户ID和过期时间
   - 定期刷新令牌保持会话活跃

### 7.2 数据传输安全
1. **传输加密**：
   - WebSocket连接使用WSS(WebSocket Secure)协议
   - 所有数据使用TLS/SSL加密传输

2. **数据签名**：
   - 所有操作请求附带时间戳和签名
   - 签名使用HMAC-SHA256算法，基于共享密钥和请求内容生成

### 7.3 操作安全
1. **权限级别**：
   - 只读操作（Level 1）：无需二次验证
   - 普通操作（Level 2）：需要有效会话
   - 敏感操作（Level 3）：需要二次验证

2. **操作审计**：
   - 记录所有敏感操作的执行情况
   - 包括操作类型、执行时间、执行设备和结果

## 8. 实现计划

### 8.1 桌面客户端改造

#### 8.1.1 添加WebSocket通信模块
- 实现WebSocket客户端类
- 添加消息处理逻辑
- 整合到MainWindow

#### 8.1.2 添加状态同步机制
- 扩展CurrentOrder类，添加状态通知功能
- 在各更新点添加同步代码
- 实现批量状态同步方法

#### 8.1.3 添加安全验证机制
- 实现配对码生成和验证
- 添加JWT令牌验证
- 实现操作权限检查

### 8.2 通信服务器开发

#### 8.2.1 搭建WebSocket服务器
- 使用Node.js + Express + ws/socket.io
- 实现连接管理和消息路由

#### 8.2.2 实现消息转发
- 建立客户端与小程序的映射关系
- 实现消息格式转换和路由

#### 8.2.3 实现安全机制
- 配对码验证逻辑
- JWT令牌生成和验证
- 请求签名验证

### 8.3 微信小程序开发

#### 8.3.1 创建项目基础结构
- 设置项目配置
- 创建页面框架
- 实现导航逻辑

#### 8.3.2 实现WebSocket通信
- 封装WebSocket API
- 实现消息发送和接收
- 处理连接状态管理

#### 8.3.3 开发用户界面
- 实现各功能页面
- 添加数据绑定和更新逻辑
- 实现操作交互

## 9. 测试计划

### 9.1 单元测试
- WebSocket通信模块测试
- 消息处理逻辑测试
- 安全机制测试

### 9.2 集成测试
- 桌面客户端与服务器通信测试
- 微信小程序与服务器通信测试
- 端到端消息传递测试

### 9.3 功能测试
- 账户信息同步测试
- 价格信息同步测试
- 订单操作测试
- 套利流程测试

### 9.4 性能测试
- 长连接稳定性测试
- 消息吞吐量测试
- 并发连接测试

### 9.5 安全测试
- 认证机制安全测试
- 数据传输安全测试
- 操作权限测试

## 10. 部署方案

### 10.1 服务器部署
- 推荐使用云服务器（阿里云/腾讯云）
- 配置要求：2核4G以上，带宽10Mbps以上
- 操作系统：CentOS 7.x或Ubuntu 18.04+
- 运行环境：Node.js 14+, Nginx

### 10.2 网络配置
- 配置域名和SSL证书
- 设置Nginx反向代理
- 配置WebSocket升级支持
- 设置防火墙规则

### 10.3 桌面客户端配置
- 添加服务器连接配置界面
- 支持多服务器配置和切换
- 实现连接状态监控和自动重连

### 10.4 微信小程序发布
- 完成小程序审核和发布流程
- 配置小程序服务器域名
- 设置小程序发布版本和灰度策略

## 11. 维护方案

### 11.1 日志管理
- 服务器日志收集和分析
- 客户端错误日志上报
- 建立告警机制

### 11.2 版本更新
- 桌面客户端自动更新机制
- 服务器平滑升级策略
- 微信小程序版本控制

### 11.3 数据备份
- 服务器配置和数据定期备份
- 用户配置云同步

## 12. 扩展计划

### 12.1 多设备支持
- 支持多个桌面客户端同时连接
- 实现设备间状态同步
- 添加设备管理功能

### 12.2 推送通知
- 集成微信订阅消息
- 支持关键事件推送（订单成交、套利完成等）
- 自定义通知规则

### 12.3 高级功能
- 支持多交易策略配置
- 添加数据分析和图表展示
- 实现自定义交易规则

## 13. 风险评估

### 13.1 技术风险
- WebSocket连接不稳定：实现重连机制和离线缓存
- 服务器负载过高：实现负载均衡和扩容机制
- 数据不同步：实现数据版本控制和冲突解决

### 13.2 安全风险
- 通信被拦截：使用SSL/TLS加密所有通信
- 未授权访问：实现多层次的身份验证和授权
- 配对码泄露：配对码设置有效期和使用次数限制

### 13.3 业务风险
- 交易执行延迟：优化通信过程，减少延迟
- 操作冲突：实现操作锁定机制，防止并发操作
- 状态不一致：实现定期全量同步，确保状态一致

## 14. 总结

本设计文档详细描述了币安量化交易系统与微信小程序的集成方案，包括系统架构、功能模块、数据设计、界面设计、接口设计、安全性设计、实现计划、测试计划、部署方案和维护方案等内容。

通过实现桌面客户端与微信小程序的实时双向通信，用户可以随时随地监控和操作交易系统，大大提高了系统的可用性和便捷性。同时，我们采取了多种安全措施，确保系统的安全性和稳定性。

在后续开发中，我们将按照本文档的规划进行实施，并根据实际情况进行必要的调整和优化，以确保项目的顺利进行和最终成功。 