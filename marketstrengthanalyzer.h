#ifndef MARKETSTRENGTHANALYZER_H
#define MARKETSTRENGTHANALYZER_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include <QString>
#include <QList>
#include <QDateTime>

// 定义市场强度数据结构
struct MarketStrengthData {
    double bidAskRatio;        // 买卖比率 (买一总额/卖一总额)
    double bidVolume;          // 买一总额
    double askVolume;          // 卖一总额
    double bidPrice;           // 买一价
    double askPrice;           // 卖一价
    QDateTime timestamp;       // 时间戳
    
    bool isBullish() const {   // 是否看涨
        return bidAskRatio > 1.0;
    }
    
    QString strengthLevel() const {
        if (bidAskRatio > 3.0) return "强烈看涨";
        if (bidAskRatio > 1.5) return "看涨";
        if (bidAskRatio > 1.0) return "轻微看涨";
        if (bidAskRatio > 0.67) return "轻微看跌";
        if (bidAskRatio > 0.33) return "看跌";
        return "强烈看跌";
    }
};

// 市场强度分析器类
// 负责分析买卖一价的强弱关系，判断市场趋势
class MarketStrengthAnalyzer : public QObject
{
    Q_OBJECT

public:
    // 获取单例实例
    static MarketStrengthAnalyzer* getInstance();
    // 释放单例实例
    static void releaseInstance();

    // 获取当前市场强度数据
    MarketStrengthData getCurrentStrengthData() const;
    
    // 获取历史市场强度数据
    QList<MarketStrengthData> getHistoryStrengthData(int count = 10) const;
    
    // 获取当前趋势方向 (1=看涨, -1=看跌, 0=中性)
    int getCurrentTrend() const;
    
    // 获取市场强度描述
    QString getStrengthDescription() const;
    
    // 获取价格趋势预测
    QString getPriceTrendPrediction() const;
    
    // 获取买单强度
    double getBuyStrength() const;
    
    // 获取卖单强度
    double getSellStrength() const;
    
    // 获取买卖强度比率
    double getBuySellRatio() const;

signals:
    // 市场强度更新信号
    void strengthUpdated(const MarketStrengthData &strengthData);
    
    // 趋势变化信号 (当趋势从看涨转为看跌或从看跌转为看涨时触发)
    void trendChanged(int previousTrend, int currentTrend);

public slots:
    // 处理订单簿深度数据更新
    void handleOrderBookDepthUpdated(const QJsonObject &depthData);
    
    // 重置分析器状态
    void reset();

private:
    // 私有构造和析构函数
    explicit MarketStrengthAnalyzer(QObject *parent = nullptr);
    ~MarketStrengthAnalyzer();

    // 禁止拷贝构造和赋值操作
    MarketStrengthAnalyzer(const MarketStrengthAnalyzer&) = delete;
    MarketStrengthAnalyzer& operator=(const MarketStrengthAnalyzer&) = delete;

    // 分析历史数据，检测趋势变化
    void analyzeHistoryData();

    // 计算买一价订单总金额
    double calculateBidVolume(const QJsonArray &bids, double &price) const;
    
    // 计算卖一价订单总金额
    double calculateAskVolume(const QJsonArray &asks, double &price) const;
    
    // 单例实例
    static MarketStrengthAnalyzer* m_instance;
    
    // 当前市场强度数据
    MarketStrengthData m_currentStrengthData;
    
    // 历史市场强度数据 (最近的记录在前面)
    QList<MarketStrengthData> m_historyStrengthData;
    
    // 历史数据最大保存数量
    static const int MAX_HISTORY_SIZE = 100;
    
    // 当前趋势 (1=看涨, -1=看跌, 0=中性)
    int m_currentTrend;
    
    // 上一次趋势
    int m_previousTrend;
};

#endif // MARKETSTRENGTHANALYZER_H 