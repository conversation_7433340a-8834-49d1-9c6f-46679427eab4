#include "mainwindow.h"
#include "./ui_mainwindow.h"
#include <QFile>
#include <QTextStream>
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
#include <QStringConverter>
#endif
#include "loginwindow.h"
#include "marketstrengthanalyzer.h"
#include "orderqueuepositiontracker.h"
#include <QDebug>
#include <QMessageBox>
#include <QSettings>
#include <QVBoxLayout>
#include <QUuid>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::MainWindow), m_apiWorker(nullptr), m_isBuyerMaker(false), m_currentOrderId(""), m_useWebSocketForBookTicker(true),
      m_useWebSocketForLatestPrice(true)
{
    ui->setupUi(this);
    setupInitialUI();
    // 设置窗口标题
    setWindowTitle("币安USDC/USDT套利工具");

    // 初始化label_newOrderMsg为不可见
    ui->label_newOrderMsg->setVisible(false);

    // 初始化账户信息对象
    m_accountInfo = new AccountInfo(this);

    // 初始化各种定时器
    m_refreshTimer = new QTimer(this);
    m_priceRefreshTimer = new QTimer(this);

    // 初始化API管理器和其他组件
    // m_apiManager = new ApiManager(this); // 移除这行代码，ApiWorker将由loginwindow创建并传递
    m_arbitrageTimer = new QTimer(this);

    // 初始化等待对话框
    m_waitDialog = new QDialog(this);
    m_waitDialog->setWindowTitle("请稍候");
    m_waitDialog->setFixedSize(200, 100);
    m_waitDialog->setModal(true);

    // 获取历史表格控件指针
    m_historyTable = ui->historyTableWidget;

    // 初始化音频播放器
    initSoundPlayers();

    // 初始化市场强度和订单队列位置的UI控件
    m_marketStrengthLabel = ui->label_marketStrengthLabel;
    m_marketStrengthLabel->setText("买卖强弱:");
    m_queuePositionLabel = ui->label_queuePositionLabel;
    m_queuePositionLabel->setText("等待数据...");
    m_queuePositionProgressBar = ui->progressBar_currentPrecent;

    // // 添加到状态栏
    // ui->statusbar->addPermanentWidget(m_marketStrengthLabel);
    // ui->statusbar->addPermanentWidget(m_queuePositionLabel, 1); // 1是伸展因子，让标签占据更多空间
    // ui->statusbar->addPermanentWidget(m_queuePositionProgressBar);

    // 注意：移除了设置超时时间的默认值，使用UI设计中的设置

    // 初始化界面元素
    initMenu();
    initHistoryTable();
    initSignalsAndSlots();

    // 定时刷新账户信息和价格信息
    m_refreshTimer->setInterval(10000); // 每10秒刷新一次
    connect(m_refreshTimer, &QTimer::timeout, this, &MainWindow::refreshAccountInfo);
    m_refreshTimer->start();

    m_priceRefreshTimer->setInterval(1000); // 每1秒刷新一次
    connect(m_priceRefreshTimer, &QTimer::timeout, this, &MainWindow::refreshPriceInfo);
    m_priceRefreshTimer->start();

    // 初始化状态栏
    updateArbitrageStatus();

    // 开启自动登录
    QTimer::singleShot(100, this, &MainWindow::onAutoLogin);

    // 初始化超时时间控件
    // QDateTime defaultTimeout = QDateTime::currentDateTime().addSecs(3600); // 默认1小时后
    QDateTime defaultTimeout = QDateTime::currentDateTime();
    ui->dateTimeEdit_timeOut->setDateTime(defaultTimeout);
    ui->dateTimeEdit_timeOut->setMinimumDateTime(QDateTime::currentDateTime());
    ui->dateTimeEdit_timeOut->setDisplayFormat("yyyy-MM-dd hh:mm:ss");

    // 初始化超时复选框
    ui->checkBox_useTimeOut->setChecked(false);

    // 连接超时复选框状态变化信号
    connect(ui->checkBox_useTimeOut, &QCheckBox::stateChanged, this, [this](int state)
            { ui->dateTimeEdit_timeOut->setEnabled(state == Qt::Checked); });

    // 初始设置超时时间编辑框的启用状态
    ui->dateTimeEdit_timeOut->setEnabled(ui->checkBox_useTimeOut->isChecked());

    // 初始化API权重检查定时器
    m_apiWeightCheckTimer = new QTimer(this);
    connect(m_apiWeightCheckTimer, &QTimer::timeout, this, &MainWindow::checkApiWeightInfo);
    // 每60秒检查一次API权重
    m_apiWeightCheckTimer->start(60000);

    // 初始化数据库
    initDatabase();

    // 初始化止损计时器
    m_stopLossTimer = new QTimer(this);
    connect(m_stopLossTimer, &QTimer::timeout, this, &MainWindow::onStopLossTimeout);
}

MainWindow::~MainWindow()
{
    // 首先断开所有信号连接，防止后续操作触发任何回调
    disconnectAllConnections();

    // 停止所有定时器
    if (m_refreshTimer)
    {
        if (m_refreshTimer->isActive())
        {
            // 如果定时器正在运行，先尝试以安全的方式停止
            m_refreshTimer->disconnect(); // 断开所有连接
            if (m_refreshTimer->thread() == QThread::currentThread())
            {
                // 如果在同一线程中，可以直接停止
                m_refreshTimer->stop();
            }
            else
            {
                // 安排定时器延迟删除
                m_refreshTimer->deleteLater();
                m_refreshTimer = nullptr;
            }
        }
    }

    if (m_priceRefreshTimer)
    {
        if (m_priceRefreshTimer->isActive())
        {
            // 如果定时器正在运行，先尝试以安全的方式停止
            m_priceRefreshTimer->disconnect(); // 断开所有连接
            if (m_priceRefreshTimer->thread() == QThread::currentThread())
            {
                // 如果在同一线程中，可以直接停止
                m_priceRefreshTimer->stop();
            }
            else
            {
                // 安排定时器延迟删除
                m_priceRefreshTimer->deleteLater();
                m_priceRefreshTimer = nullptr;
            }
        }
    }

    // 停止套利相关活动（断开连接后执行）
    // 释放CurrentOrder单例
    CurrentOrder *currentOrder = CurrentOrder::getInstance();
    currentOrder->disconnect();
    currentOrder->resetToInitial();
    CurrentOrder::releaseInstance();

    // 停止WebSocket连接（不要尝试通过API停止，直接断开连接）
    if (m_apiWorker)
    {
        // 不要调用会产生网络请求的方法，如stopUserDataStream
    }

    // 删除定时器对象
    if (m_refreshTimer)
    {
        delete m_refreshTimer;
        m_refreshTimer = nullptr;
    }

    if (m_priceRefreshTimer)
    {
        delete m_priceRefreshTimer;
        m_priceRefreshTimer = nullptr;
    }

    // 清理API权重检查定时器
    if (m_apiWeightCheckTimer)
    {
        if (m_apiWeightCheckTimer->isActive())
        {
            m_apiWeightCheckTimer->stop();
        }
        delete m_apiWeightCheckTimer;
        m_apiWeightCheckTimer = nullptr;
    }

    // 清理数据库管理器
    if (m_databaseManager)
    {
        delete m_databaseManager;
        m_databaseManager = nullptr;
    }

    // 清理止损计时器
    if (m_stopLossTimer)
    {
        if (m_stopLossTimer->isActive())
        {
            m_stopLossTimer->stop();
        }
        delete m_stopLossTimer;
        m_stopLossTimer = nullptr;
    }

    // 清理音频播放器

    if (m_gettingMoneySoundPlayer)
    {
        m_gettingMoneySoundPlayer->stop();
        delete m_gettingMoneySoundPlayer;
        m_gettingMoneySoundPlayer = nullptr;
    }

    if (m_successSoundPlayer)
    {
        m_successSoundPlayer->stop();
        delete m_successSoundPlayer;
        m_successSoundPlayer = nullptr;
    }

    if (m_audioOutput_success)
    {
        delete m_audioOutput_success;
        m_audioOutput_success = nullptr;
    }
    if (m_audioOutput_gettingMoney)
    {
        delete m_audioOutput_gettingMoney;
        m_audioOutput_gettingMoney = nullptr;
    }

    // 删除UI
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }

    qDebug().noquote() << "MainWindow析构函数执行完毕";
}

void MainWindow::setApiWorker(ApiWorker *apiWorker)
{
    // 设置API工作对象
    m_apiWorker = apiWorker;

    // 将apiWorker设置到CurrentOrder
    CurrentOrder::getInstance()->setApiWorker(apiWorker);

    // 设置WebSocketClient到CurrentOrder
    if (apiWorker)
    {
        WebSocketClient *webSocketClient = apiWorker->getWebSocketClient();
        if (webSocketClient)
        {
            CurrentOrder::getInstance()->setWebSocketClient(webSocketClient);

            // 直接连接WebSocketClient的tradeDataReceived信号到OrderQueuePositionTracker的handleLatestTradeUpdated槽函数
            OrderQueuePositionTracker *queueTracker = OrderQueuePositionTracker::getInstance();
            connect(webSocketClient, &WebSocketClient::tradeDataReceived, queueTracker, &OrderQueuePositionTracker::handleLatestTradeUpdated,
                    Qt::UniqueConnection);
        }
        else
        {
        }
    }

    // 连接信号和槽
    if (m_apiWorker)
    {
        // 连接ApiWorker的信号到MainWindow的槽
        initSignalsAndSlots();
    }

    // 获取账户信息
    refreshAccountInfo();

    // 获取价格信息
    refreshPriceInfo();

    // 启动账户信息刷新定时器（每3秒刷新一次）
    m_refreshTimer->start(3000);

    // 启动价格信息刷新定时器（每1秒刷新一次，修改自0.5秒）
    m_priceRefreshTimer->start(1000);

    // 启动订单ID检测定时器，初始状态下不启动，等到有订单时再启动
    // 这里不需要调用，因为我们在handleOrderStatusChanged中会根据状态来启动或停止
    // startOrderIdCheckTimer();

    // 启动WebSocket连接，接收实时订单更新 - 使用QMetaObject::invokeMethod确保在正确的线程中调用
    if (m_apiWorker && m_apiWorker->thread() != thread())
    {
        // 如果ApiWorker在不同线程中，使用跨线程调用
        QMetaObject::invokeMethod(m_apiWorker, "startUserDataStream", Qt::QueuedConnection);
    }
    else if (m_apiWorker)
    {
        // 如果在同一线程中，可以直接调用
        m_apiWorker->startUserDataStream();
    }

    // 添加以下代码
    if (m_apiWorker && m_apiWorker->getWebSocketClient())
    {
        WebSocketClient *webSocketClient = m_apiWorker->getWebSocketClient();

        // 设置超时参数 - 默认为120秒，可根据实际需求调整
        webSocketClient->setInactivityTimeout(180000); // 3分钟无响应触发重连
    }
}

void MainWindow::handleAccountInfoUpdated(const QJsonObject &accountInfo)
{
    // 更新账户信息
    m_accountInfo->updateAccountInfo(accountInfo);
    isFirstRefreshAccount = true;
    ui->label_accountUpdateTime->setText(QDateTime::currentDateTime().toString("yyyy年MM月dd日hh:mm:ss"));
}

void MainWindow::handleAccountInfoUpdatedForWebsocket(double usdc, double usdc_locked, double usdt, double usdt_locked, QString updateTime)
{
    m_accountInfo->updateAccountInfoForWebsocket(usdc, usdc_locked, usdt, usdt_locked);
    ui->label_accountUpdateTime->setText(updateTime);
}

void MainWindow::handleUsdtBalanceUpdated(const QString &balance)
{
    // 更新USDT总余额显示
    ui->usdtBalanceLabel->setText(balance);
}

void MainWindow::handleUsdcBalanceUpdated(const QString &balance)
{
    // 更新USDC总余额显示
    ui->usdcBalanceLabel->setText(balance);
}

void MainWindow::handleUsdtFreeBalanceUpdated(const QString &balance)
{
    // 更新USDT可用余额显示
    ui->usdtFreeBalanceLabel->setText(balance);
}

void MainWindow::handleUsdcFreeBalanceUpdated(const QString &balance)
{
    // 更新USDC可用余额显示
    ui->usdcFreeBalanceLabel->setText(balance);
}

void MainWindow::handleUsdtLockedBalanceUpdated(const QString &balance)
{
    // 更新USDT锁定余额显示
    ui->usdtLockedBalanceLabel->setText(balance);
}

void MainWindow::handleUsdcLockedBalanceUpdated(const QString &balance)
{
    // 更新USDC锁定余额显示
    ui->usdcLockedBalanceLabel->setText(balance);
}

void MainWindow::handleBestOrderBookTickerUpdated(const QJsonObject &ticker)
{
    // 更新最佳挂单价格
    if (ticker.contains("bidPrice") && ticker.contains("askPrice"))
    {
        // 记录旧的买一价和卖一价，用于比较变化
        QString oldBidPrice = m_bidPrice;
        QString oldAskPrice = m_askPrice;

        // 更新买一价和卖一价
        m_bidPrice = ticker["bidPrice"].toString();
        m_askPrice = ticker["askPrice"].toString();

        // 记录买一价变化的日志
        if (oldBidPrice != m_bidPrice)
        {
            // 如果是首次获取价格（oldBidPrice为空），则不显示变化提示
            if (!oldBidPrice.isEmpty())
            {
                // 计算变化幅度
                double oldPrice = oldBidPrice.toDouble();
                double newPrice = m_bidPrice.toDouble();
                double change = ((newPrice - oldPrice) / oldPrice) * 100.0;

                // 显示价格变化图标和颜色
                QString changeText = QString::number(change, 'f', 2) + "%";
                if (change > 0)
                {
                    ui->bidPriceLabel->setText(m_bidPrice + " ↑ " + changeText);
                    ui->bidPriceLabel->setStyleSheet("color: #00aa00;"); // 绿色
                }
                else if (change < 0)
                {
                    ui->bidPriceLabel->setText(m_bidPrice + " ↓ " + changeText);
                    ui->bidPriceLabel->setStyleSheet("color: #aa0000;"); // 红色
                }
                else
                {
                    ui->bidPriceLabel->setText(m_bidPrice + " - " + changeText);
                    ui->bidPriceLabel->setStyleSheet("color: #000000;"); // 黑色
                }
            }
            else
            {
                // 首次获取价格
                ui->bidPriceLabel->setText(m_bidPrice);
                ui->bidPriceLabel->setStyleSheet("color: #000000;"); // 黑色
            }
        }
        else
        {
            // 价格没有变化，只是简单地更新显示
            ui->bidPriceLabel->setText(m_bidPrice);
        }

        // 更新卖一价显示
        ui->askPriceLabel->setText(m_askPrice);

        // 获取当前订单对象
        CurrentOrder *currentOrder = CurrentOrder::getInstance();

        // 检查当前订单状态
        CurrentOrderStatus currentStatus = currentOrder->getStatus();

        // 如果处于买入挂单状态，检查买一价是否高于下单价格
        if (currentStatus == CurrentOrderStatus::BUY_PENDING)
        {
            double currentBidPrice = m_bidPrice.toDouble();
            double orderPrice = currentOrder->getBuyPrice();

            // 如果当前买一价高于下单价格，撤销订单并重新下单
            if (currentBidPrice > orderPrice && orderPrice > 0)
            {
                qDebug().noquote() << "检测到买一价上涨：" << orderPrice << " -> " << currentBidPrice << "，准备撤销当前订单并重新下单";
                if (m_waitForNewOrder)
                {
                    return;
                }

                // 显示正在重新下单的提示
                ui->label_newOrderMsg->setVisible(true);

                // 记录总投资金额（USDT）供后续使用
                m_lastTotalInvestment = orderPrice * currentOrder->getBuyQuantity().toDouble();

                // 记录原订单价格
                m_lastOrderPrice = QString::number(orderPrice);

                // 设置新价格等待下单的标记 - 撤单成功后会使用这个标记下新单
                m_waitForNewOrder = true;

                // 获取订单ID
                QString orderId = currentOrder->getBuyOrderId();

                // 先撤销当前订单
                emit requestCancelOrder(m_symbol, orderId);

                // 重置订单队列跟踪器，因为要重新下单
                OrderQueuePositionTracker::getInstance()->reset();

                // 使用状态栏显示提示信息
                QString message = QString("检测到买一价上涨: %1 -> %2，正在撤销订单并重新下单...").arg(orderPrice, 0, 'f', 4).arg(currentBidPrice, 0, 'f', 4);
                ui->statusbar->showMessage(message, 5000);
            }
        }
        // 如果处于套利中状态，监控卖一价下降
        else if (currentStatus == CurrentOrderStatus::ARBITRAGING)
        {
            // 如果正在等待卖单确认，跳过价格下降检测
            if (m_waitingForSellOrderConfirmation)
            {
                return;
            }

            // 修改止损判断逻辑：比较当前卖一价与卖单价格
            double currentAskPrice = m_askPrice.toDouble();
            double sellOrderPrice = currentOrder->getSellPrice();

            // 检测价格下降

            // 检测卖一价是否低于卖单价格（首次止损）
            if (currentAskPrice < sellOrderPrice && m_currentStopLossValue == 0.0)
            {
                qDebug().noquote() << "检测到卖一价低于卖单价格: 卖单价格=" << sellOrderPrice << "当前卖一价=" << currentAskPrice
                                   << "差值=" << (sellOrderPrice - currentAskPrice);

                // 处理第一次价格下降
                handleFirstPriceDrop();
            }

            // 仍然更新上一次卖一价，用于其他目的
            m_lastAskPrice = m_askPrice;
        }
        // 如果已经在第一次止损状态，检测是否发生第二次下降
        else if (currentStatus == CurrentOrderStatus::STOP_LOSS_FIRST_DROP)
        {
            double currentAskPrice = m_askPrice.toDouble();
            double originalSellPrice = currentOrder->getOriginalSellPrice();

            // 使用原始套利价格进行第二次下降检测
            double originalPriceDiff = originalSellPrice - currentAskPrice;

            // 检测卖一价是否继续下降（第二次止损，基于原始价格的价格差必须大于第一次）
            if (originalPriceDiff > m_currentStopLossValue)
            {
                qDebug().noquote() << "检测到卖一价第二次下降: 原始套利价格=" << originalSellPrice << "当前卖一价=" << currentAskPrice
                                   << "原始价格差值=" << originalPriceDiff << "大于当前止损差值:" << m_currentStopLossValue;

                // 处理第二次价格下降
                handleSecondPriceDrop();
            }

            // 仍然更新上一次卖一价，用于其他目的
            m_lastAskPrice = m_askPrice;
        }
        // 如果处于初始状态或其他状态，重置止损相关变量
        else if (currentStatus == CurrentOrderStatus::INITIAL || currentStatus == CurrentOrderStatus::BUY_PENDING)
        {
            // 重置止损状态
            resetStopLossState();
        }
    }
}

void MainWindow::handleApiKeysValidated(bool valid, const QString &errorMessage)
{
    if (valid)
    {
        // API密钥验证成功，更新状态栏
        ui->statusbar->showMessage("API密钥验证成功", 3000);

        // 刷新账户信息
        refreshAccountInfo();

        // 刷新价格信息
        refreshPriceInfo();
    }
    else
    {
        // API密钥验证失败，显示错误消息
        QString errorDetails = getFriendlyErrorMessage(errorMessage);
        ui->statusbar->showMessage("API密钥验证失败：" + errorDetails, 5000);
        QMessageBox::critical(this, "API密钥验证失败", "无法验证API密钥：" + errorDetails);
        qDebug().noquote() << "API密钥验证失败：" << errorMessage;
    }
}

void MainWindow::handleLatestPriceUpdated(const QJsonObject &priceInfo)
{
    // 更新最新成交价格
    if (priceInfo.contains("price") && priceInfo.contains("isBuyerMaker"))
    {
        m_latestPrice = priceInfo["price"].toString();
        m_isBuyerMaker = priceInfo["isBuyerMaker"].toBool();
        // qDebug().noquote() << priceInfo;
        // 更新价格显示
        updatePriceInfoDisplay();

        // 获取订单跟踪器单例
        OrderQueuePositionTracker *queueTracker = OrderQueuePositionTracker::getInstance();

        // 获取订单跟踪器状态
        OrderQueuePositionTracker::OrderStatus trackerStatus = queueTracker->getOrderStatus();

        // 根据订单跟踪器的状态决定是否请求订单簿数据
        if (trackerStatus != OrderQueuePositionTracker::Initial)
        {
            // 直接调用订单簿深度数据处理函数，而不是通过信号槽连接
            // 获取当前的订单簿深度数据
            static QDateTime lastOrderBookRequestTime = QDateTime::currentDateTime().addSecs(-60);
            QDateTime currentTime = QDateTime::currentDateTime();
            int intervalSeconds = 15; // 默认15秒间隔

            // 根据不同的订单状态决定获取订单簿深度的频率
            if (trackerStatus == OrderQueuePositionTracker::BuyPending)
            {
                intervalSeconds = 10; // 买单挂着时，10秒间隔
            }
            else if (trackerStatus == OrderQueuePositionTracker::Arbitraging)
            {
                intervalSeconds = 5; // 套利中状态，5秒间隔
            }

            // 根据间隔时间决定是否请求新的订单簿数据
            if (lastOrderBookRequestTime.secsTo(currentTime) >= intervalSeconds)
            {
                // 请求新的订单簿数据
                emit requestOrderBookDepth(m_symbol, 20);
                lastOrderBookRequestTime = currentTime;
            }
        }
    }
}

void MainWindow::refreshAccountInfo()
{
    if (isFirstRefreshAccount)
    {
        m_refreshTimer->disconnect();
        return;
    }

    // 刷新账户信息
    if (m_apiWorker)
    {
        // 使用Qt::QueuedConnection确保在正确的线程中调用
        QMetaObject::invokeMethod(m_apiWorker, "getAccountInfo", Qt::QueuedConnection);
    }
    else
    {
        qDebug() << "错误：ApiWorker未初始化，无法刷新账户信息";
    }
}

void MainWindow::refreshPriceInfo()
{
    // 如果使用WebSocket获取BookTicker数据，只需要在首次启动时订阅一次
    if (m_useWebSocketForBookTicker)
    {
        static bool hasSubscribedBookTicker = false;
        if (!hasSubscribedBookTicker)
        {
            // 订阅BookTicker流
            emit subscribeBookTickerStream(m_symbol);
            hasSubscribedBookTicker = true;
        }
    }
    else
    {
        // 如果不使用WebSocket，则通过REST API获取最佳挂单价格
        emit requestBestOrderBookTicker(m_symbol);
    }

    // 如果使用WebSocket获取最新成交价格，只需要在首次启动时订阅一次
    if (m_useWebSocketForLatestPrice)
    {
        static bool hasSubscribedTrade = false;
        if (!hasSubscribedTrade)
        {
            // 订阅Trade流
            emit subscribeTradeDataStream(m_symbol);
            hasSubscribedTrade = true;
        }
    }
    else
    {
        // 如果不使用WebSocket，则通过REST API获取最新成交价格
        emit requestLatestPrice(m_symbol);
    }

    // 不再直接请求订单簿深度数据
    // 订单簿深度数据的请求现在在handleLatestPriceUpdated函数中根据需要触发
}

void MainWindow::on_actionExit_triggered()
{
    // 退出应用程序
    QApplication::quit();
}

void MainWindow::on_actionAbout_triggered()
{
    // 显示关于对话框
    QMessageBox::about(this, "关于", "币安量化交易系统\n\n版本：1.0.0\n\n基于币安API的量化交易软件");
}

void MainWindow::initMenu()
{
    // 连接菜单项的信号和槽
    connect(ui->actionExit, &QAction::triggered, this, &MainWindow::on_actionExit_triggered);
    connect(ui->actionAbout, &QAction::triggered, this, &MainWindow::on_actionAbout_triggered);
}

void MainWindow::initSignalsAndSlots()
{
    if (!m_apiWorker)
    {
        qDebug().noquote() << "错误：API工作对象为空，无法初始化信号和槽";
        return;
    }

    // 获取CurrentOrder单例对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 连接账户信息更新信号
    connect(m_apiWorker, &ApiWorker::accountInfoUpdated, this, &MainWindow::handleAccountInfoUpdated);
    connect(m_apiWorker, &ApiWorker::accountInfoUpdateForWebsocket, this, &MainWindow::handleAccountInfoUpdatedForWebsocket);

    // 连接USDT余额更新信号
    connect(m_accountInfo, &AccountInfo::usdtBalanceUpdated, this, &MainWindow::handleUsdtBalanceUpdated);

    // 连接USDC余额更新信号
    connect(m_accountInfo, &AccountInfo::usdcBalanceUpdated, this, &MainWindow::handleUsdcBalanceUpdated);

    // 连接USDT可用余额更新信号
    connect(m_accountInfo, &AccountInfo::usdtFreeBalanceUpdated, this, &MainWindow::handleUsdtFreeBalanceUpdated);

    // 连接USDC可用余额更新信号
    connect(m_accountInfo, &AccountInfo::usdcFreeBalanceUpdated, this, &MainWindow::handleUsdcFreeBalanceUpdated);

    // 连接USDT锁定余额更新信号
    connect(m_accountInfo, &AccountInfo::usdtLockedBalanceUpdated, this, &MainWindow::handleUsdtLockedBalanceUpdated);

    // 连接USDC锁定余额更新信号
    connect(m_accountInfo, &AccountInfo::usdcLockedBalanceUpdated, this, &MainWindow::handleUsdcLockedBalanceUpdated);

    // 连接最佳挂单价格更新信号
    connect(m_apiWorker, &ApiWorker::bestOrderBookTickerUpdated, this, &MainWindow::handleBestOrderBookTickerUpdated);

    // 连接最新成交价格更新信号
    connect(m_apiWorker, &ApiWorker::latestPriceUpdated, this, &MainWindow::handleLatestPriceUpdated);

    // 连接下单结果信号
    connect(m_apiWorker, &ApiWorker::orderPlaced, this, &MainWindow::handleOrderPlaced);

    // 连接取消订单结果信号
    connect(m_apiWorker, SIGNAL(orderCancelled(bool, QJsonObject, QString)), this, SLOT(handleOrderCancelled(bool, QJsonObject, QString)));

    // 连接订单状态更新信号
    connect(m_apiWorker, &ApiWorker::currentOrderStatusUpdated, this, &MainWindow::handleOrderStatusUpdated);

    // 连接所有挂单信息信号
    connect(m_apiWorker, &ApiWorker::openOrdersReceived, this, &MainWindow::handleOpenOrdersReceived);

    // 连接所有挂单信息信号到CurrentOrder单例，用于状态同步
    connect(m_apiWorker, SIGNAL(openOrdersReceived(bool, QJsonArray, QString)), currentOrder, SLOT(handleOpenOrdersReceived(bool, QJsonArray, QString)));

    // 连接WebSocket订单更新信号
    connect(m_apiWorker, &ApiWorker::orderUpdateReceived, this, &MainWindow::handleOrderUpdateReceived);

    // 连接API密钥验证结果信号
    connect(m_apiWorker, &ApiWorker::apiKeysValidated, this, &MainWindow::handleApiKeysValidated);

    // 连接请求信号到API工作对象
    connect(this, &MainWindow::requestBestOrderBookTicker, m_apiWorker, &ApiWorker::getBestOrderBookTicker, Qt::QueuedConnection);
    connect(this, &MainWindow::requestLatestPrice, m_apiWorker, &ApiWorker::getLatestPrice, Qt::QueuedConnection);
    connect(this, SIGNAL(requestPlaceOrder(QString, QString, QString, QString, QString, QString)), m_apiWorker,
            SLOT(placeOrder(QString, QString, QString, QString, QString, QString)), Qt::QueuedConnection);
    connect(this, &MainWindow::requestCancelOrder, m_apiWorker, &ApiWorker::cancelOrder, Qt::QueuedConnection);
    connect(this, &MainWindow::requestOpenOrders, m_apiWorker, &ApiWorker::getOpenOrders, Qt::QueuedConnection);

    // 连接CurrentOrder单例的信号（使用老式的SIGNAL/SLOT语法）
    connect(currentOrder, &CurrentOrder::statusChanged, this, &MainWindow::handleOrderStatusChanged);
    connect(currentOrder, &CurrentOrder::buyOrderFilled, this, &MainWindow::handleBuyOrderFilled);
    connect(currentOrder, &CurrentOrder::arbitrageCompleted, this, &MainWindow::handleArbitrageCompleted);
    connect(currentOrder, SIGNAL(orderCancelled(QString)), this, SLOT(handleOrderCancelled(QString)));

    // 设置超时定时器连接
    if (m_arbitrageTimer)
    {
        // 断开之前的连接，避免重复连接
        disconnect(m_arbitrageTimer, nullptr, this, nullptr);

        // 连接超时处理函数
        connect(m_arbitrageTimer, &QTimer::timeout, this, &MainWindow::onArbitrageTimeout);

        qDebug().noquote() << "套利超时定时器连接已建立";
    }

    // 订单簿深度数据相关
    connect(this, &MainWindow::requestOrderBookDepth, m_apiWorker, &ApiWorker::getOrderBookDepth);
    connect(m_apiWorker, &ApiWorker::orderBookDepthUpdated, this, &MainWindow::handleOrderBookDepthUpdated);
    connect(m_apiWorker, &ApiWorker::sg_sendMs, this, &MainWindow::updateMs);

    // 初始化市场强度分析器和订单队列位置跟踪器
    MarketStrengthAnalyzer *marketAnalyzer = MarketStrengthAnalyzer::getInstance();
    OrderQueuePositionTracker *queueTracker = OrderQueuePositionTracker::getInstance();

    // 将订单簿深度数据转发给分析器和跟踪器
    connect(m_apiWorker, &ApiWorker::orderBookDepthUpdated, marketAnalyzer, &MarketStrengthAnalyzer::handleOrderBookDepthUpdated);

    // 直接连接订单簿深度数据信号到OrderQueuePositionTracker
    connect(m_apiWorker, &ApiWorker::orderBookDepthUpdated, queueTracker, &OrderQueuePositionTracker::handleOrderBookDepthUpdated, Qt::UniqueConnection);

    // 直接连接WebSocketClient的tradeDataReceived信号到OrderQueuePositionTracker的handleLatestTradeUpdated槽函数
    if (m_apiWorker && m_apiWorker->getWebSocketClient())
    {
        connect(m_apiWorker->getWebSocketClient(), &WebSocketClient::tradeDataReceived, queueTracker, &OrderQueuePositionTracker::handleLatestTradeUpdated,
                Qt::UniqueConnection);
    }
    else
    {
        qDebug().noquote() << "警告：未能连接WebSocketClient的交易数据信号，WebSocketClient不可用";
    }

    // 连接市场强度分析器的信号
    connect(marketAnalyzer, &MarketStrengthAnalyzer::strengthUpdated, this, [this](const MarketStrengthData &data)
            {
        // 更新UI显示买卖一价强弱关系
        QString strengthText = QString("买/卖比: %1 (%2)").arg(data.bidAskRatio, 0, 'f', 2).arg(data.strengthLevel());
        m_marketStrengthLabel->setText(strengthText);

        // 设置样式
        QString styleSheet;
        if (data.bidAskRatio > 1.0) {
            // 看涨 - 绿色
            styleSheet = "color: green; font-weight: bold;";
        } else {
            // 看跌 - 红色
            styleSheet = "color: red; font-weight: bold;";
        }
        m_marketStrengthLabel->setStyleSheet(styleSheet); });

    // 连接订单队列位置跟踪器的信号
    connect(queueTracker, &OrderQueuePositionTracker::positionUpdated, this, [this](double positionPercent, double filledAmount)
            {
        // 接收到买单队列位置更新信号时调用updateMarketInfo
        updateMarketInfo(); });

    // 连接卖单队列位置更新信号
    connect(queueTracker, &OrderQueuePositionTracker::sellPositionUpdated, this, [this](double positionPercent, double filledAmount)
            {
        // 接收到卖单队列位置更新信号时调用updateMarketInfo
        updateMarketInfo(); });

    // 在买单下单成功后设置订单队列跟踪器的买单信息
    connect(this, &MainWindow::orderFilled, this, [queueTracker, this](const QString &side, const QString &quantity, double price)
            {
        if (side.toUpper() == "BUY") {
            queueTracker->setMyBuyOrder(m_symbol, price, quantity.toDouble());
        } });

    // 当买单成交或操作结束时重置跟踪器
    connect(CurrentOrder::getInstance(), &CurrentOrder::buyOrderFilled, this, [queueTracker]()
            { queueTracker->reset(); });

    connect(CurrentOrder::getInstance(), &CurrentOrder::statusChanged, this, [queueTracker](CurrentOrderStatus status)
            {
        if (status == CurrentOrderStatus::INITIAL) {
            queueTracker->reset();
        } });

    // 连接API请求信号
    connect(this, &MainWindow::validateApiKeys, m_apiWorker, &ApiWorker::validateApiKeys);
    connect(this, &MainWindow::requestAccountInfo, m_apiWorker, &ApiWorker::getAccountInfo);
    connect(this, &MainWindow::requestBestOrderBookTicker, m_apiWorker, &ApiWorker::getBestOrderBookTicker);
    connect(this, &MainWindow::subscribeBookTickerStream, m_apiWorker, &ApiWorker::subscribeBookTickerStream);
    connect(this, &MainWindow::unsubscribeBookTickerStream, m_apiWorker, &ApiWorker::unsubscribeBookTickerStream);
    connect(this, &MainWindow::subscribeTradeDataStream, m_apiWorker, &ApiWorker::subscribeTradeStream);
    connect(this, &MainWindow::unsubscribeTradeDataStream, m_apiWorker, &ApiWorker::unsubscribeTradeStream);
    connect(this, &MainWindow::requestLatestPrice, m_apiWorker, &ApiWorker::getLatestPrice);

    // 如果WebSocketClient可用，连接交易数据信号到MainWindow
    if (m_apiWorker && m_apiWorker->getWebSocketClient())
    {
        WebSocketClient *webSocketClient = m_apiWorker->getWebSocketClient();
        // 连接WebSocketClient的tradeDataReceived信号到MainWindow
        connect(webSocketClient, &WebSocketClient::tradeDataReceived, this, &MainWindow::handleTradeDataReceived, Qt::QueuedConnection);
    }
}

void MainWindow::updateAccountInfoDisplay()
{
    // 更新账户信息显示
    ui->usdtBalanceLabel->setText(m_accountInfo->getUSDTBalance());
    ui->usdcBalanceLabel->setText(m_accountInfo->getUSDCBalance());
    ui->usdtFreeBalanceLabel->setText(m_accountInfo->getUSDTFreeBalance());
    ui->usdcFreeBalanceLabel->setText(m_accountInfo->getUSDCFreeBalance());
    ui->usdtLockedBalanceLabel->setText(m_accountInfo->getUSDTLockedBalance());
    ui->usdcLockedBalanceLabel->setText(m_accountInfo->getUSDCLockedBalance());
}

void MainWindow::updatePriceInfoDisplay()
{
    // 获取价格信息
    QString symbol = m_symbol;
    QString bidPrice = m_bidPrice;
    QString askPrice = m_askPrice;
    QString latestPrice = m_latestPrice;

    // 更新价格显示
    ui->bidPriceLabel->setText(bidPrice);
    ui->askPriceLabel->setText(askPrice);
    ui->latestPriceLabel->setText(latestPrice);

    // 设置价格颜色
    // 买一价使用绿色
    ui->bidPriceLabel->setStyleSheet("color: #009900; font-weight: bold;");

    // 卖一价使用红色
    ui->askPriceLabel->setStyleSheet("color: #FF0000; font-weight: bold;");

    // 最新价格根据交易类型设置颜色
    if (m_isBuyerMaker)
    {
        // 买方挂单(maker)，卖方吃单(taker)，价格通常下跌，使用红色
        ui->latestPriceLabel->setStyleSheet("color: #FF0000; font-weight: bold;");
    }
    else
    {
        // 卖方挂单(maker)，买方吃单(taker)，价格通常上涨，使用绿色
        ui->latestPriceLabel->setStyleSheet("color: #009900; font-weight: bold;");
    }
}

void MainWindow::on_btn_order_clicked()
{
    // 创建唯一的事件键，使用时间戳确保唯一性
    QString eventKey = "place_order_" + QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");

    // 检查是否已处理过此事件（防止短时间内多次点击）
    if (isEventProcessed(eventKey))
    {
        qDebug().noquote() << "检测到短时间内重复下单请求，已忽略";
        return;
    }

    if (ui->checkBox_useTimeOut->isChecked() && (QDateTime::currentDateTime() >= ui->dateTimeEdit_timeOut->dateTime()))
    {
        QMessageBox::warning(this, "错误", "当前时间已经超过超时设置时间！");
        return;
    }

    // 先禁用下单按钮，防止重复点击
    ui->btn_order->setEnabled(false);

    // 获取当前订单对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 检查当前订单状态，必须处于初始状态才能下单
    if (!currentOrder->isInitialState())
    {
        QString message = "当前已有进行中的订单，状态：" + currentOrder->getStatusText();
        QMessageBox::warning(this, "无法下单", message);
        ui->checkBox_automatic->setEnabled(true);
        ui->checkBox_automatic->setChecked(false);
        // 关闭全自动模式并恢复到最初状态
        if (!ui->checkBox_automatic->isChecked())
        {
            // 确保下单按钮可用
            ui->btn_order->setEnabled(true);
        }
        orderDialogShowFlag = true;
        return;
    }

    // 获取USDT金额
    int priceInt = ui->spinBox_price->value();
    if (priceInt <= 0)
    {
        QMessageBox::warning(this, "错误", "请输入有效的USDT金额");
        ui->checkBox_automatic->setEnabled(true);
        ui->checkBox_automatic->setChecked(false);
        // 关闭全自动模式并恢复到最初状态
        if (!ui->checkBox_automatic->isChecked())
        {
            // 确保下单按钮可用
            ui->btn_order->setEnabled(true);
        }
        orderDialogShowFlag = true;
        return;
    }

    // 获取USDT可用余额
    double usdtFreeBalance = m_accountInfo->getUSDTFreeBalance().toDouble();
    if (usdtFreeBalance <= 0)
    {
        QMessageBox::warning(this, "错误", "USDT可用余额不足");
        ui->checkBox_automatic->setEnabled(true);
        ui->checkBox_automatic->setChecked(false);
        // 关闭全自动模式并恢复到最初状态
        if (!ui->checkBox_automatic->isChecked())
        {
            // 确保下单按钮可用
            ui->btn_order->setEnabled(true);
        }
        orderDialogShowFlag = true;
        return;
    }

    // 验证下单金额是否小于等于USDT可用余额
    if (priceInt > usdtFreeBalance)
    {
        QMessageBox::warning(this, "错误", "下单金额不能大于USDT可用余额");
        ui->checkBox_automatic->setEnabled(true);
        ui->checkBox_automatic->setChecked(false);
        // 关闭全自动模式并恢复到最初状态
        if (!ui->checkBox_automatic->isChecked())
        {
            // 确保下单按钮可用
            ui->btn_order->setEnabled(true);
        }
        orderDialogShowFlag = true;
        return;
    }

    // 使用最新买一价作为买入价格
    double buyPrice = m_bidPrice.toDouble();
    if (buyPrice <= 0)
    {
        QMessageBox::warning(this, "错误", "当前买一价无效，请等待价格更新");
        ui->checkBox_automatic->setEnabled(true);
        ui->checkBox_automatic->setChecked(false);
        // 关闭全自动模式并恢复到最初状态
        if (!ui->checkBox_automatic->isChecked())
        {
            // 确保下单按钮可用
            ui->btn_order->setEnabled(true);
        }
        orderDialogShowFlag = true;
        return;
    }

    // 记录当前买一价，用于日志和确认
    QString currentBidPrice = m_bidPrice;

    // 计算可购买的USDC数量
    double quantity = priceInt / buyPrice;

    // 使用四舍五入而不是向下取整，以获得更准确的数量
    quantity = round(quantity);
    if (quantity < 1.0)
    {
        quantity = 1.0;
    }

    // 确认下单
    QString message = QString("确认以下订单信息：\n\n"
                              "交易对：%1\n"
                              "方向：买入\n"
                              "类型：限价单\n"
                              "价格：%2 USDT\n"
                              "数量：%3 USDC\n"
                              "总金额：%4 USDT\n\n"
                              "套利条件：当卖一价大于买入价格时自动卖出\n"
                              "当前卖一价：%5 USDT\n\n"
                              "是否确认下单？")
                          .arg(m_symbol)
                          .arg(currentBidPrice)
                          .arg(QString::number(quantity, 'f', 0))
                          .arg(QString::number(quantity * buyPrice, 'f', 2))
                          .arg(m_askPrice);

    // 不管是不是全自动模式，都要显示一次确认对话框
    if (orderDialogShowFlag)
    {
        if (ui->checkBox_automatic->isChecked())
        {
            ui->checkBox_automatic->setEnabled(false);
            orderDialogShowFlag = false;
        }

        QMessageBox::StandardButton reply = QMessageBox::question(this, "确认下单", message, QMessageBox::Yes | QMessageBox::No);

        if (reply == QMessageBox::Yes)
        {
            // 保存下单参数
            m_pendingOrderSymbol = m_symbol;
            m_pendingOrderSide = "BUY";
            m_pendingOrderType = "LIMIT";
            m_pendingOrderQuantity = QString::number(quantity, 'f', 0);
            m_pendingOrderPrice = currentBidPrice;

            // 设置下单状态
            m_isPlacingOrder = true;

            // 显示正在检查挂单的提示
            ui->statusbar->showMessage("正在检查币安平台挂单状态，请稍候...");
            QApplication::processEvents(); // 立即更新UI

            // 先检查币安平台上是否有挂单
            checkBinanceOpenOrders();

            // 注意：实际下单操作将在handleOpenOrdersReceived方法中进行
            qDebug().noquote() << "正在检查币安平台挂单状态，稍后将执行下单操作";
        }
        else
        {
            ui->checkBox_automatic->setEnabled(true);
            ui->checkBox_automatic->setChecked(false);
            // 关闭全自动模式并恢复到最初状态
            if (!ui->checkBox_automatic->isChecked())
            {
                // 确保下单按钮可用
                ui->btn_order->setEnabled(true);
            }
            orderDialogShowFlag = true;
        }
    }
    else if (ui->checkBox_automatic->isChecked())
    {
        // 如果处于全自动模式则直接下单
        // 保存下单参数
        m_pendingOrderSymbol = m_symbol;
        m_pendingOrderSide = "BUY";
        m_pendingOrderType = "LIMIT";
        m_pendingOrderQuantity = QString::number(quantity, 'f', 0);
        m_pendingOrderPrice = currentBidPrice;

        // 设置下单状态
        m_isPlacingOrder = true;

        // 显示正在检查挂单的提示
        ui->statusbar->showMessage("全自动模式：正在检查币安平台挂单状态，请稍候...");
        QApplication::processEvents(); // 立即更新UI

        // 先检查币安平台上是否有挂单
        checkBinanceOpenOrders();

        // 注意：实际下单操作将在handleOpenOrdersReceived方法中进行
        qDebug().noquote() << "全自动模式：正在检查币安平台挂单状态，稍后将执行下单操作";
    }
}

void MainWindow::on_btn_cancle_clicked()
{
    // 获取当前订单对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 检查当前订单状态
    CurrentOrderStatus status = currentOrder->getStatus();

    if (status == CurrentOrderStatus::INITIAL)
    {
        // 初始状态，不需要任何操作
        QMessageBox::information(this, "提示", "当前没有进行中的订单");
        return;
    }

    // 根据订单状态执行不同操作
    QString message;
    switch (status)
    {
    case CurrentOrderStatus::BUY_PENDING:
        message = "确定要撤销当前买单并结束套利吗？";
        break;

    case CurrentOrderStatus::ARBITRAGING:
        message = "确定要撤销当前卖单并结束套利吗？";
        break;

    case CurrentOrderStatus::STOP_LOSS_FIRST_DROP:
        message = "确定要撤销当前止损卖单并结束套利吗？";
        break;

    case CurrentOrderStatus::STOP_LOSS_SECOND_DROP:
        message = "确定要撤销当前强制止损卖单并结束套利吗？";
        break;

    default:
        return;
    }

    // 确认操作
    QMessageBox::StandardButton reply = QMessageBox::question(this, "确认结束套利", message, QMessageBox::Yes | QMessageBox::No);

    if (reply == QMessageBox::Yes)
    {
        // 禁用撤单按钮，防止重复点击
        ui->btn_cancle->setEnabled(false);

        // 隐藏重新下单提示，因为要结束套利，不会再有重新下单行为
        ui->label_newOrderMsg->setVisible(false);

        // 重置等待重新下单标记
        m_waitForNewOrder = false;

        // 取消订阅交易流数据
        unsubscribeFromPriceTrade();
        qDebug().noquote() << "手动结束套利，已取消交易数据订阅";

        // 重置订单队列跟踪器
        OrderQueuePositionTracker::getInstance()->reset();
        qDebug().noquote() << "手动结束套利，重置订单队列跟踪器";

        // 创建等待对话框
        m_waitDialog = new QDialog(this);
        m_waitDialog->setWindowTitle("请稍候");
        m_waitDialog->setFixedSize(300, 100);
        m_waitDialog->setModal(true);

        // 添加布局和标签
        QVBoxLayout *layout = new QVBoxLayout(m_waitDialog);
        QLabel *label = new QLabel("正在处理撤销订单请求，请稍候...", m_waitDialog);
        label->setAlignment(Qt::AlignCenter);
        layout->addWidget(label);

        // 显示等待对话框（非模态，不阻塞主线程）
        m_waitDialog->show();

        // 执行取消操作
        bool cancelSuccess = currentOrder->cancelCurrentOrder();

        if (!cancelSuccess)
        {
            if (m_waitDialog)
            {
                m_waitDialog->accept();
                m_waitDialog->deleteLater();
                m_waitDialog = nullptr;
            }

            ui->btn_cancle->setEnabled(true);
            QMessageBox::warning(this, "操作失败", "结束套利操作失败，请重试");
        }
        else
        {
            ui->checkBox_automatic->setEnabled(true);
            ui->checkBox_automatic->setChecked(false);

            // 关闭全自动模式并恢复到最初状态
            if (!ui->checkBox_automatic->isChecked())
            {
                // 确保下单按钮可用
                ui->btn_order->setEnabled(true);
            }
            orderDialogShowFlag = true;

            ui->statusbar->showMessage("结束套利操作已发送，请等待结果...", 3000);
        }
    }
}

void MainWindow::handleOrderPlaced(bool success, const QJsonObject &orderInfo, const QString &errorMessage)
{
    // 关闭等待对话框
    if (m_waitDialog && m_waitDialog->isVisible())
    {
        m_waitDialog->accept();
        m_waitDialog->deleteLater();
        m_waitDialog = nullptr;
    }

    // 清除下单标志
    m_isPlacingOrder = false;

    // 获取当前订单对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    if (!success)
    {
        // 添加详细的错误日志，方便排查问题
        qDebug().noquote() << "下单失败详细错误: " << errorMessage;

        // 处理下单失败的情况 - 添加网络错误自动重试逻辑
        QString friendlyError = getFriendlyErrorMessage(errorMessage);

        // 判断是否是网络相关错误
        bool isNetworkError = errorMessage.contains("Error transferring") || errorMessage.contains("network error") ||
                              errorMessage.contains("Connection closed") || errorMessage.contains("server replied:");
        // 判断是否是业务规则错误，如LOT_SIZE
        bool isBusinessRuleError = errorMessage.contains("LOT_SIZE") || errorMessage.contains("PRICE_FILTER") || errorMessage.contains("MIN_NOTIONAL") ||
                                   errorMessage.contains("MAX_NUM_ORDERS") || errorMessage.contains("code:-1013") || errorMessage.contains("code:-1010");
        // 如果是网络错误且未超过最大重试次数，尝试自动重试
        if (isNetworkError && m_orderRetryCount < MAX_RETRY_COUNT && !m_pendingOrderSymbol.isEmpty())
        {
            m_orderRetryCount++;
            QString retryMessage = QString("网络错误，正在尝试重新下单 (尝试 %1/%2)...").arg(m_orderRetryCount).arg(MAX_RETRY_COUNT);
            ui->statusbar->showMessage(retryMessage, 5000);
            qDebug().noquote() << retryMessage;

            // 禁用下单按钮，防止用户干扰重试过程
            ui->btn_order->setEnabled(false);

            // 延迟2秒后重试，给网络一些恢复时间
            QTimer::singleShot(2000, this, [this]()
                               {
                // 重新下单时需要改成true
                m_isPlacingOrder = true;
                // 再次检查是否有挂单，以确保可以安全下单
                checkBinanceOpenOrders();

                // 2秒后再执行下单操作，确保获取挂单信息完成
                QTimer::singleShot(2000, this, &MainWindow::proceedWithOrder); });
            return;
        } // 如果是业务规则错误，直接提示用户而不重试
        else if (isBusinessRuleError)
        {
            QString specificError = "下单参数不符合交易所规则：";

            if (errorMessage.contains("LOT_SIZE"))
            {
                specificError += "订单数量不符合要求，请修改数量为整数值。";
            }
            else if (errorMessage.contains("PRICE_FILTER"))
            {
                specificError += "订单价格不符合要求，请调整价格精度。";
            }
            else if (errorMessage.contains("MIN_NOTIONAL"))
            {
                specificError += "订单金额太小，请增加购买数量。";
            }
            else
            {
                specificError += friendlyError;
            }

            QMessageBox::warning(this, "下单失败", specificError);
            ui->statusbar->showMessage(specificError, 5000);

            // 重置当前订单对象状态为初始状态
            CurrentOrder::getInstance()->resetToInitial();
            qDebug().noquote() << "由于业务规则错误，已重置订单对象为初始状态";
            // 重置重试计数
            m_orderRetryCount = 0;

            // 清空当前订单ID
            m_currentOrderId = "";

            // 确保下单按钮和全自动复选框可用
            ui->btn_order->setEnabled(true);
            ui->checkBox_automatic->setEnabled(true);

            // 隐藏重新下单提示
            ui->label_newOrderMsg->setVisible(false);

            // 更新UI显示
            updateCurrentOrderDisplay();

            return;
        }
        else
        {
            // 重置重试计数
            m_orderRetryCount = 0;

            // 显示错误消息
            QMessageBox::warning(this, "下单失败", "创建订单失败: " + friendlyError);

            // 清空当前订单ID
            m_currentOrderId = "";

            // 更新状态栏
            ui->statusbar->showMessage("创建订单失败: " + friendlyError, 5000);

            // 确保下单按钮可用
            ui->btn_order->setEnabled(true);
            ui->checkBox_automatic->setEnabled(true);
            ui->checkBox_automatic->setChecked(false);
            // 隐藏重新下单提示，因为这不是重新下单的情况
            ui->label_newOrderMsg->setVisible(false);

            // 恢复初始状态
            CurrentOrder::getInstance()->resetToInitial();

            // 更新UI显示
            updateCurrentOrderDisplay();

            return;
        }
    }

    // 重置重试计数
    m_orderRetryCount = 0;

    // 获取订单ID和状态
    QString orderId = orderInfo["orderId"].toVariant().toString();
    QString orderSide = orderInfo["side"].toString();
    QString orderSymbol = orderInfo["symbol"].toString();
    QString orderType = orderInfo["type"].toString();
    QString orderStatus = orderInfo["status"].toString();

    // 查看订单是否已被接受
    qDebug().noquote() << "订单信息：" << orderInfo;

    // 判断是买单还是卖单
    if (orderSide == "BUY")
    {
        // 更新当前订单对象
        currentOrder->setBuyOrderInfo(orderInfo);
    }
    else if (orderSide == "SELL")
    {
        // 更新当前订单对象
        currentOrder->setSellOrderInfo(orderInfo);
        m_gettingMoneySoundPlayer->play();
    }

    // 更新UI显示
    updateCurrentOrderDisplay();

    // 显示订单信息
    displayOrderInfo(orderInfo);

    // 根据订单方向显示不同消息
    if (orderStatus == "NEW" || orderStatus == "PARTIALLY_FILLED")
    {
        if (orderSide == "BUY")
        {
            // 买单成功消息
            m_currentOrderId = orderId;
            ui->statusbar->showMessage(tr("买单创建成功，订单ID：%1").arg(orderId), 5000);
            qDebug().noquote() << "买单创建成功，订单ID：" << orderId;

            // 启动套利定时器（如果启用了超时设置）
            if (ui->checkBox_useTimeOut->isChecked())
            {
                // 记录超时时间
                m_timeoutTime = ui->dateTimeEdit_timeOut->dateTime();
                qDebug().noquote() << "套利超时时间设置为：" << m_timeoutTime.toString("yyyy-MM-dd hh:mm:ss");

                // 设置定时器为1秒间隔
                m_arbitrageTimer->setInterval(1000);
                m_arbitrageTimer->start();

                qDebug().noquote() << "套利超时定时器已启动，间隔：1000毫秒";
                qDebug().noquote() << "套利超时定时器是否活跃：" << (m_arbitrageTimer->isActive() ? "是" : "否");

                // 禁用超时设置控件
                ui->checkBox_useTimeOut->setEnabled(false);
                ui->dateTimeEdit_timeOut->setEnabled(false);
            }
            else
            {
                qDebug().noquote() << "超时选项未启用，不启动套利超时定时器";
            }
        }
        else if (orderSide == "SELL")
        {
            // 卖单创建成功，重置等待卖单确认标志
            m_waitingForSellOrderConfirmation = false;

            // 正常的卖单成功消息
            ui->statusbar->showMessage(tr("卖单创建成功，订单ID：%1，关联买单ID：%2").arg(orderId).arg(currentOrder->getBuyOrderId()), 5000);
            qDebug().noquote() << "卖单创建成功，订单ID：" << orderId << "，关联买单ID：" << currentOrder->getBuyOrderId();

            // 卖单创建成功后，设置卖单信息到队列跟踪器
            if (orderInfo.contains("symbol") && orderInfo.contains("price") && orderInfo.contains("origQty"))
            {
                QString symbol = orderInfo["symbol"].toString();
                double price = orderInfo["price"].toString().toDouble();
                double quantity = orderInfo["origQty"].toString().toDouble();

                qDebug().noquote() << "卖单创建成功，设置卖单信息到队列跟踪器：" << symbol << price << quantity;

                // 获取OrderQueuePositionTracker实例
                OrderQueuePositionTracker *tracker = OrderQueuePositionTracker::getInstance();

                // 设置卖单信息到队列跟踪器
                tracker->setMySellOrder(symbol, price, quantity);

                // 根据当前订单状态设置队列跟踪器状态
                CurrentOrderStatus currentStatus = currentOrder->getStatus();

                if (currentStatus == CurrentOrderStatus::ARBITRAGING)
                {
                    tracker->setOrderStatus(OrderQueuePositionTracker::Arbitraging);
                    qDebug().noquote() << "设置队列跟踪器状态为套利中";
                }
                else if (currentStatus == CurrentOrderStatus::STOP_LOSS_FIRST_DROP)
                {
                    tracker->setOrderStatus(OrderQueuePositionTracker::StopLossFirstDrop);
                    qDebug().noquote() << "设置队列跟踪器状态为第一次止损";
                }
                else if (currentStatus == CurrentOrderStatus::STOP_LOSS_SECOND_DROP)
                {
                    tracker->setOrderStatus(OrderQueuePositionTracker::Arbitraging); // 第二次止损仍使用套利状态
                    qDebug().noquote() << "设置队列跟踪器状态为套利中（第二次止损）";
                }

                subscribeToPriceTrade(symbol, price);
                // 立即请求订单簿深度数据，确保卖单队列能被初始化
                emit requestOrderBookDepth(symbol, 20);
            }
        }
    }
    else
    {
        // 其他状态处理
        QString message = tr("订单已提交但状态异常：%1").arg(orderStatus);
        ui->statusbar->showMessage(message, 5000);
        qDebug().noquote() << message;

        checkBinanceOpenOrders();
    }

    if (!ui->checkBox_automatic->isChecked())
    {
        // 确保下单按钮可用
        ui->btn_order->setEnabled(true);
    }

    // 如果是买单，设置订单队列跟踪器的买单信息
    if (orderInfo.contains("side") && orderInfo["side"].toString().toUpper() == "BUY")
    {
        QString symbol = orderInfo["symbol"].toString();
        double price = orderInfo["price"].toString().toDouble();
        double quantity = orderInfo["origQty"].toString().toDouble();
        // 设置订单队列跟踪器
        OrderQueuePositionTracker::getInstance()->setMyBuyOrder(symbol, price, quantity);
        subscribeToPriceTrade(symbol, price);
    }
}

void MainWindow::handleOrderCancelled(bool success, const QJsonObject &orderInfo, const QString &errorMessage)
{
    // 关闭等待对话框
    if (m_waitDialog && m_waitDialog->isVisible())
    {
        m_waitDialog->accept();
        m_waitDialog->deleteLater();
        m_waitDialog = nullptr;
    }

    // 确保结束套利按钮可用，无论操作是否成功
    ui->btn_cancle->setEnabled(true);

    if (success)
    {
        qDebug().noquote() << "订单取消成功";
        qDebug().noquote() << "订单信息：" << orderInfo;

        // 处理操作成功的情况
        if (orderInfo.contains("orderId"))
        {
            QString orderId = QString::number(orderInfo["orderId"].toDouble());
            QString symbol = orderInfo["symbol"].toString();
            qDebug().noquote() << "成功取消订单：" << orderId << " 交易对：" << symbol;

            // 获取当前订单对象
            CurrentOrder *currentOrder = CurrentOrder::getInstance();

            // 重置订单队列跟踪器
            OrderQueuePositionTracker::getInstance()->reset();
            qDebug().noquote() << "订单取消成功，重置订单队列跟踪器";

            // 处理止损相关的订单取消
            if (m_firstStopLoss)
            {
                qDebug().noquote() << "检测到第一次止损标记，执行第一次价格下降的止损操作";
                m_firstStopLoss = false;
                executeFirstDropStopLoss();
            }
            else if (m_secondStopLoss)
            {
                qDebug().noquote() << "检测到第二次止损标记，执行第二次价格下降的强制止损操作";
                m_secondStopLoss = false;
                executeSecondDropStopLoss();
            }
            // 如果有等待重新下单的标记，并且交易对正确
            else if (m_waitForNewOrder && symbol == m_symbol)
            {
                qDebug().noquote() << "检测到等待重新下单标记，准备检查平台上是否有挂单";
                m_orderCancledFailCount = 0;
                // 检查是否有挂单，仅发送请求，在handleOpenOrdersReceived中处理结果
                checkBinanceOpenOrders();
            }
            else
            {
                // 处理普通的撤单完成情况
                ui->statusbar->showMessage("订单已取消：" + orderId, 3000);

                // 隐藏重新下单提示，因为这不是重新下单的情况
                ui->label_newOrderMsg->setVisible(false);

                // 恢复初始状态
                CurrentOrder::getInstance()->resetToInitial();

                // 更新UI显示
                updateCurrentOrderDisplay();

                // 刷新余额信息
                refreshAccountInfo();

                // 如果撤单成功，停止套利超时定时器并重新启用超时设置控件
                if (m_arbitrageTimer->isActive())
                {
                    m_arbitrageTimer->stop();
                    qDebug().noquote() << "撤单成功，停止套利超时定时器";
                }

                // 重新启用超时设置控件
                ui->checkBox_useTimeOut->setEnabled(true);
                ui->dateTimeEdit_timeOut->setEnabled(ui->checkBox_useTimeOut->isChecked());
            }
        }
    }
    else
    {
        // 处理操作失败的情况
        qDebug().noquote() << "订单取消失败：" << errorMessage;

        // 获取当前订单对象
        CurrentOrder *currentOrder = CurrentOrder::getInstance();

        ui->statusbar->showMessage("订单取消失败：" + getFriendlyErrorMessage(errorMessage), 5000);
        if (++m_orderCancledFailCount > MAX_RETRY_COUNT)
        {
            // 如果取消订单重试次数超过最大次数则直接结束套利，否则继续取消
            //  隐藏重新下单提示，因为撤单失败无法重新下单
            ui->label_newOrderMsg->setVisible(false);

            // 撤单失败时也重置等待标记
            m_waitForNewOrder = false;
            m_firstStopLoss = false;
            m_secondStopLoss = false;

            // 停止定时器
            m_arbitrageTimer->stop();

            // 重新启用超时设置控件
            ui->checkBox_useTimeOut->setEnabled(true);
            ui->dateTimeEdit_timeOut->setEnabled(ui->checkBox_useTimeOut->isChecked());

            // 获取当前订单状态
            CurrentOrderStatus status = currentOrder->getStatus();

            // 恢复初始状态
            currentOrder->resetToInitial();

            ui->checkBox_automatic->setEnabled(true);
            ui->checkBox_automatic->setChecked(false);
            // 关闭全自动模式并恢复到最初状态，确保下单按钮可用
            ui->btn_order->setEnabled(true);
            orderDialogShowFlag = true;

            // 更新界面
            updateCurrentOrderDisplay();

            // 更新状态栏
            ui->statusbar->showMessage("无法取消订单导致套利终止，已回退到初始状态", 5000);

            // 刷新账户信息
            refreshAccountInfo();
            //
            qDebug().noquote() << "无法取消订单的导致套利终止";
            m_orderCancledFailCount = 0;

            // 弹出超时提示
            QMessageBox::information(this, "套利终止", "无法取消订单的导致套利终止。");
        }
        else
        {
            // 如果是止损相关的取消订单，重试取消
            CurrentOrder *currentOrder = CurrentOrder::getInstance();
            CurrentOrderStatus status = currentOrder->getStatus();

            if (status == CurrentOrderStatus::STOP_LOSS_FIRST_DROP || status == CurrentOrderStatus::STOP_LOSS_SECOND_DROP)
            {
                QString currentSellOrderId = currentOrder->getSellOrderId();
                if (!currentSellOrderId.isEmpty())
                {
                    qDebug().noquote() << "止损取消订单失败，尝试重新取消 (尝试 " << m_orderCancledFailCount << "/" << MAX_RETRY_COUNT << ")";
                    emit requestCancelOrder(m_symbol, currentSellOrderId);
                }
            }
            else
            {
                // 其他情况下，重试取消当前订单
                emit requestCancelOrder(m_symbol, CurrentOrder::getInstance()->getBuyOrderId());
            }
        }
    }
}

// 添加一个辅助方法来获取友好的错误信息
QString MainWindow::getFriendlyErrorMessage(const QString &errorMessage)
{
    // 根据API错误信息返回友好的中文提示
    if (errorMessage.contains("Unknown order"))
    {
        return "订单不存在，可能已成交或已被取消";
    }
    else if (errorMessage.contains("Market is closed"))
    {
        return "市场已关闭，请稍后再试";
    }
    else if (errorMessage.contains("This action disabled"))
    {
        return "该操作被禁用，请联系客服或稍后再试";
    }
    else if (errorMessage.contains("Connection closed") || errorMessage.contains("Error transferring"))
    {
        return "网络连接中断，请检查网络连接后重试";
    }
    else if (errorMessage.contains("server replied:"))
    {
        return "币安服务器响应异常，系统将自动重试，请稍候...";
    }
    else if (errorMessage.contains("Rate limit"))
    {
        return "API请求频率超出限制，请稍后再试";
    }
    else if (errorMessage.contains("Invalid API-key"))
    {
        return "API密钥无效，请检查您的API密钥配置";
    }
    else if (errorMessage.contains("Timestamp for this request"))
    {
        return "请求时间戳错误，请检查系统时间是否准确";
    }
    else if (errorMessage.contains("operation canceled"))
    {
        return "操作已取消，请稍后重试";
    }
    else if (errorMessage.contains("Binance error"))
    {
        return "币安服务器返回错误，请稍后再试";
    }
    else if (errorMessage.contains("Min notional"))
    {
        return "订单金额过小，请增加交易金额";
    }
    else if (errorMessage.contains("Insufficient funds"))
    {
        return "余额不足，请检查账户余额";
    }
    else if (errorMessage.contains("Invalid symbol"))
    {
        return "无效的交易对，请检查交易对设置";
    }
    else if (errorMessage.contains("Service unavailable"))
    {
        return "币安服务暂时不可用，请稍后再试";
    }
    else if (errorMessage.contains("System busy"))
    {
        return "系统繁忙，请稍后再试";
    }
    else if (errorMessage.contains("timeout"))
    {
        return "请求超时，请检查网络连接后重试";
    }
    else if (errorMessage.contains("SSL handshake failed"))
    {
        return "SSL连接失败，请检查网络环境后重试";
    }
    else if (errorMessage.contains("Host not found") || errorMessage.contains("No hostname specified"))
    {
        return "无法连接到交易所服务器，请检查网络后重试";
    }

    // 默认情况
    return errorMessage;
}

void MainWindow::displayOrderInfo(const QJsonObject &orderInfo)
{
    // 显示订单信息
    QString orderId = orderInfo["orderId"].toString();
    QString symbol = orderInfo["symbol"].toString();
    QString side = orderInfo["side"].toString();
    QString type = orderInfo["type"].toString();
    QString price = orderInfo["price"].toString();
    QString origQty = orderInfo["origQty"].toString();
    QString executedQty = orderInfo["executedQty"].toString();
    QString cummulativeQuoteQty = orderInfo["cummulativeQuoteQty"].toString();
    QString status = orderInfo["status"].toString();
    QString timeInForce = orderInfo["timeInForce"].toString();
    qint64 time = orderInfo["transactTime"].toVariant().toLongLong();
    QDateTime orderTime = QDateTime::fromMSecsSinceEpoch(time);

    qDebug().noquote() << "time时间:" << orderInfo["transactTime"].toVariant() << orderInfo["time"].toVariant();

    // 更新上次订单信息
    m_lastOrderInfo = orderInfo;
    m_lastOrderSymbol = symbol;
    m_lastOrderSide = side;
    m_lastOrderType = type;
    m_lastOrderPrice = price;
    m_lastOrderQuantity = origQty;
    m_lastOrderStatus = status;
    m_lastOrderTime = orderTime;
    m_lastOrderExecutedQty = executedQty;
    m_lastOrderCummulativeQuoteQty = cummulativeQuoteQty;

    // 计算实际成交均价
    if (executedQty.toDouble() > 0)
    {
        m_lastOrderActualPrice = cummulativeQuoteQty.toDouble() / executedQty.toDouble();
    }
    else
    {
        m_lastOrderActualPrice = 0;
    }

    // 只在状态栏显示简短信息，不再弹出对话框
    QString statusMessage = QString("订单成功 - ID: %1, %2 %3 %4, 价格: %5, 数量: %6").arg(orderId).arg(symbol).arg(side).arg(type).arg(price).arg(origQty);
    ui->statusbar->showMessage(statusMessage, 5000);

    // 记录关键订单信息
    qDebug().noquote() << "订单" << (side == "BUY" ? "买入" : "卖出") << "成功 - ID:" << orderId << "价格:" << price << "数量:" << origQty;

    // 注释掉显示模态对话框的代码
    /*
    QString message = QString("订单信息：\n"
                              "订单号：%1\n"
                              "交易对：%2\n"
                              "方向：%3\n"
                              "类型：%4\n"
                              "价格：%5\n"
                              "数量：%6\n"
                              "已成交数量：%7\n"
                              "已成交金额：%8\n"
                              "状态：%9\n"
                              "时效：%10\n"
                              "下单时间：%11")
                          .arg(orderId)
                          .arg(symbol)
                          .arg(side)
                          .arg(type)
                          .arg(price)
                          .arg(origQty)
                          .arg(executedQty)
                          .arg(cummulativeQuoteQty)
                          .arg(status)
                          .arg(timeInForce)
                          .arg(orderTime.toString("yyyy-MM-dd hh:mm:ss"));

    // 显示消息
    QMessageBox::information(this, "订单信息", message);
    */
}

// 处理WebSocket接收到的实时订单更新
void MainWindow::handleOrderUpdateReceived(const QJsonObject &orderUpdate)
{
    // qDebug().noquote() << "收到订单更新：" << orderUpdate;

    // 检查是否是错误信息
    if (orderUpdate.contains("errorType"))
    {
        QString errorType = orderUpdate["errorType"].toString();
        QString errorMessage = orderUpdate["errorMessage"].toString();
        QString requestType = orderUpdate.value("requestType").toString();

        qDebug().noquote() << "收到错误类型：" << errorType << "，请求类型：" << requestType;

        // 处理无法获取订单状态的情况
        if (errorType == "GetOrderStatus")
        {
            m_orderStatusQueryFailCount++;
            qDebug().noquote() << "订单状态查询失败计数：" << m_orderStatusQueryFailCount;

            // 在状态栏显示简单错误信息
            ui->statusbar->showMessage("订单状态查询失败：" + errorMessage, 3000);

            // 只有当失败次数超过阈值且距离上次显示错误消息一定时间后才显示提示
            QDateTime currentTime = QDateTime::currentDateTime();
            bool shouldShowMessage = m_orderStatusQueryFailCount >= MAX_ORDER_STATUS_QUERY_FAIL_COUNT &&
                                     (m_lastErrorMessageTime.isNull() || m_lastErrorMessageTime.secsTo(currentTime) > 60); // 每分钟最多提示一次

            if (shouldShowMessage)
            {
                // 重置计数并更新上次显示时间
                m_orderStatusQueryFailCount = 0;
                m_lastErrorMessageTime = currentTime;

                // 创建一个非模态对话框，避免阻塞UI线程
                QMessageBox *msgBox = new QMessageBox(this);
                msgBox->setWindowTitle("订单状态查询提示");
                msgBox->setText("近期多次无法获取订单状态，请检查网络连接和API状态。");
                msgBox->setIcon(QMessageBox::Warning);
                msgBox->setStandardButtons(QMessageBox::Ok);

                // 设置为非模态对话框
                msgBox->setModal(false);

                // 设置关闭后自动删除
                msgBox->setAttribute(Qt::WA_DeleteOnClose);

                // 显示对话框
                msgBox->show();
            }

            return;
        }
        else
        {
            // 如果是网络错误相关的更新，则进行特殊处理
            qDebug().noquote() << "收到网络错误信息: " << errorType << " - " << errorMessage;

            return;
        }
    }
}

void MainWindow::handleOrderStatusChanged(CurrentOrderStatus status)
{
    qDebug().noquote() << "订单状态变化：" << CurrentOrder::getInstance()->getStatusText();

    // 更新订单状态显示
    ui->label_orderStatus->setText(CurrentOrder::getInstance()->getStatusText());
    ui->label_orderStatus->setStyleSheet(CurrentOrder::getInstance()->getStatusColor());

    // 根据订单状态更新UI
    updateCurrentOrderDisplay();

    // 根据订单状态决定是否显示结束套利按钮
    bool showEndButton = (status != CurrentOrderStatus::INITIAL);
    ui->btn_cancle->setVisible(showEndButton);
    ui->btn_cancle->setEnabled(showEndButton); // 确保按钮在可见时也是可用的
    ui->btn_cancle->setText("结束套利");

    // 根据状态管理下单按钮的可用性
    if (status == CurrentOrderStatus::INITIAL)
    {
        // 无论是否自动模式，回到初始状态时都要确保下单按钮可用
        // 这样即使是撤销订单失败等异常情况回退到初始状态，用户也能重新操作
        ui->btn_order->setEnabled(true);
        ui->checkBox_automatic->setEnabled(true);
        qDebug().noquote() << "状态变为初始状态，确保下单按钮和自动模式复选框可用";
    }
    else if (!ui->checkBox_automatic->isChecked())
    {
        // 手动模式下，非初始状态时禁用下单按钮
        ui->btn_order->setEnabled(false);
        qDebug().noquote() << "手动模式：状态变为非初始状态，禁用下单按钮";
    }

    // 如果状态变为初始状态，取消订阅和重置止损状态
    if (status == CurrentOrderStatus::INITIAL)
    {
        // 取消订阅交易流数据
        unsubscribeFromPriceTrade();
        qDebug().noquote() << "状态变为初始状态，已取消交易数据订阅";
    }
}

void MainWindow::handleOrderCancelled(const QString &orderId)
{
    qDebug().noquote() << "订单已取消：" << orderId;

    // 取消订阅交易流数据
    unsubscribeFromPriceTrade();
    qDebug().noquote() << "订单取消，已取消交易数据订阅";

    // 更新UI显示
    ui->statusbar->showMessage("订单已取消：" + orderId, 3000);

    // 更新账户信息
    refreshAccountInfo();

    // 更新当前订单显示
    updateCurrentOrderDisplay();

    if (!ui->checkBox_automatic->isChecked())
    {
        // 确保下单按钮可用
        ui->btn_order->setEnabled(true);
    }
}

// 处理套利完成
void MainWindow::handleArbitrageCompleted(const QString &buyOrderId, const QString &sellOrderId, double buyPrice, double sellPrice, double amount,
                                          double profit, bool isStopLoss)
{
    // 创建唯一的事件键
    QString eventKey = "arbitrage_" + buyOrderId + "_" + sellOrderId;

    // 检查是否已处理过此事件
    if (isEventProcessed(eventKey))
    {
        return;
    }

    qDebug().noquote() << (isStopLoss ? "止损完成" : "套利完成") << "- 买入:" << buyPrice << "卖出:" << sellPrice << "利润:" << profit;

    // 取消订阅交易流数据
    unsubscribeFromPriceTrade();
    qDebug().noquote() << (isStopLoss ? "止损完成，已取消交易数据订阅" : "套利完成，已取消交易数据订阅");

    // 获取当前订单对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 设置状态文本
    QString statusText = isStopLoss ? "止损完成" : "已完成";

    // 添加到历史记录表格
    addHistoryRecord(sellOrderId, currentOrder->getBuyOrderTime(), currentOrder->getBuyOrderTime(), currentOrder->getSellOrderFillTime(), statusText, buyPrice,
                     sellPrice, amount, profit);

    // 显示套利完成消息
    QString message = isStopLoss ? QString("止损完成：利润=%1 USDT").arg(profit, 0, 'f', 4) : QString("套利完成：利润=%1 USDT").arg(profit, 0, 'f', 4);
    ui->statusbar->showMessage(message, 5000);
    m_successSoundPlayer->play();
    // 更新账户信息
    refreshAccountInfo();

    // 检查是否是全自动模式
    bool isAutomatic = ui->checkBox_automatic->isChecked();

    // 在这里重置状态，确保在添加历史记录后执行
    currentOrder->resetToInitial();

    // 重置止损状态标记，确保新一轮套利不受上一轮影响
    resetStopLossState();

    // 使用单独线程延迟执行清理操作，避免阻塞UI
    QTimer::singleShot(1000, this, [this, isAutomatic]()
                       {
        // 恢复初始状态 - 已经在上面执行，这里不再需要
        // CurrentOrder::getInstance()->resetToInitial();

        // 更新UI
        updateCurrentOrderDisplay();

        if (!isAutomatic) {
            // 手动模式，确保下单按钮可用
            ui->btn_order->setEnabled(true);
            qDebug().noquote() << "手动模式：套利完成后恢复到初始状态，等待用户操作";
        } else {
            // 全自动模式，添加重试机制
            qDebug().noquote() << "全自动模式：套利完成后准备自动开始新一轮套利";

            // 定义重试函数（使用静态变量解决递归引用问题）
            int maxRetries = 3;
            int retryDelay = 5000; // 5秒

            // 使用static确保函数对象在lambda递归调用时可用
            static std::function<void(int)> tryPlaceOrder;
            tryPlaceOrder = [this, retryDelay](int retriesLeft) {
                qDebug().noquote() << "尝试开始新一轮套利，剩余重试次数: " << retriesLeft;

                // 确保当前处于初始状态
                if (CurrentOrder::getInstance()->getStatus() != CurrentOrderStatus::INITIAL) {
                    qDebug().noquote() << "当前不是初始状态，无法开始新一轮套利";
                    return;
                }

                // A. 先检查币安挂单状态
                checkBinanceOpenOrders();

                // B. 设置一个延迟，等待响应后再尝试下单
                QTimer::singleShot(2000, this, [this, retriesLeft, retryDelay]() {
                    // 跳过订单确认对话框
                    orderDialogShowFlag = false;

                    // 尝试下单
                    on_btn_order_clicked();

                    // 设置检查定时器，如果5秒后仍在初始状态，说明下单可能失败
                    QTimer::singleShot(5000, this, [this, retriesLeft, retryDelay]() {
                        if (CurrentOrder::getInstance()->getStatus() == CurrentOrderStatus::INITIAL && retriesLeft > 0) {
                            qDebug().noquote() << "开始新一轮套利失败，准备重试...";
                            QTimer::singleShot(retryDelay, this, [retriesLeft]() { tryPlaceOrder(retriesLeft - 1); });
                        } else if (CurrentOrder::getInstance()->getStatus() != CurrentOrderStatus::INITIAL) {
                            qDebug().noquote() << "新一轮套利已成功开始，状态:" << CurrentOrder::getInstance()->getStatusText();
                        } else {
                            qDebug().noquote() << "开始新一轮套利失败，重试次数已用完";

                            // 改进：全自动模式重试次数耗尽的处理
                            // 1. 发出警告声音
                            QApplication::beep();

                            // 2. 自动关闭全自动模式
                            ui->checkBox_automatic->setChecked(false);
                            ui->checkBox_automatic->setEnabled(true);

                            // 3. 显示通知消息
                            QString errorMsg = "全自动模式重试失败，已自动关闭全自动模式，请检查网络或API状态";
                            ui->statusbar->showMessage(errorMsg, 10000);

                            // 4. 显示详细对话框
                            QMessageBox::warning(this, "全自动模式停止",
                                                 "系统尝试开始新一轮套利失败，已达到最大重试次数。\n\n"
                                                 "可能原因:\n"
                                                 "1. 币安API连接问题\n"
                                                 "2. 网络连接不稳定\n"
                                                 "3. 账户余额不足\n"
                                                 "4. API速率限制\n\n"
                                                 "已自动关闭全自动模式，请检查后手动重新开启。");

                            // 5. 启用下单按钮，允许用户手动操作
                            ui->btn_order->setEnabled(true);

                            // 6. 恢复订单对象初始状态
                            CurrentOrder::getInstance()->resetToInitial();

                            // 7. 更新UI显示
                            updateCurrentOrderDisplay();

                            // 8. 重置重试计数和其他相关状态
                            m_orderRetryCount = 0;
                            orderDialogShowFlag = true;

                            qDebug().noquote() << "全自动模式已停止，需要用户手动干预";
                        }
                    });
                });
            };

            // 开始第一次尝试
            tryPlaceOrder(maxRetries);
        }

        qDebug().noquote() << "套利完成后清理完成，软件恢复到初始状态，历史记录已保留"; });

    // 清理已处理事件集合
    cleanupProcessedEvents();
}

void MainWindow::initHistoryTable()
{
    // 使用UI文件中定义的表格控件，而不是动态创建
    m_historyTable = ui->historyTableWidget;

    // 设置表格列数和表头
    m_historyTable->setColumnCount(9);
    QStringList headers;
    headers << "订单开始时间" << "套利开始时间" << "套利结束时间" << "订单号"
            << "状态" << "下单价格" << "出售价格" << "交易额" << "利润率";
    m_historyTable->setHorizontalHeaderLabels(headers);

    // 设置表格属性
    m_historyTable->setEditTriggers(QAbstractItemView::NoEditTriggers);   // 不可编辑
    m_historyTable->setSelectionBehavior(QAbstractItemView::SelectRows);  // 选择整行
    m_historyTable->setSelectionMode(QAbstractItemView::SingleSelection); // 单选
    m_historyTable->setAlternatingRowColors(true);                        // 交替行颜色

    // 设置列宽
    m_historyTable->setColumnWidth(0, 150); // 订单开始时间
    m_historyTable->setColumnWidth(1, 150); // 套利开始时间
    m_historyTable->setColumnWidth(2, 150); // 套利结束时间
    m_historyTable->setColumnWidth(3, 100); // 订单号
    m_historyTable->setColumnWidth(4, 80);  // 状态
    m_historyTable->setColumnWidth(5, 80);  // 下单价格
    m_historyTable->setColumnWidth(6, 80);  // 出售价格
    m_historyTable->setColumnWidth(7, 100); // 交易额
    m_historyTable->setColumnWidth(8, 80);  // 利润率
}

void MainWindow::updateMs(quint64 ms)
{
    ui->label_currentMs->setText(QString::number(ms));
}

void MainWindow::addHistoryRecord(const QString &orderId, const QDateTime &startTime, const QDateTime &arbitrageStartTime, const QDateTime &arbitrageEndTime,
                                  const QString &status, double buyPrice, double sellPrice, double amount, double profit)
{
    // 计算利润率
    double profitRate = (profit / amount) * 100;

    // 添加新行
    int row = m_historyTable->rowCount();
    m_historyTable->insertRow(row);

    // 设置订单开始时间
    QTableWidgetItem *startTimeItem = new QTableWidgetItem(startTime.toString("yyyy-MM-dd hh:mm:ss"));
    m_historyTable->setItem(row, 0, startTimeItem);

    // 设置套利开始时间
    QTableWidgetItem *arbitrageStartTimeItem = new QTableWidgetItem(arbitrageStartTime.toString("yyyy-MM-dd hh:mm:ss"));
    m_historyTable->setItem(row, 1, arbitrageStartTimeItem);

    // 设置套利结束时间
    QTableWidgetItem *arbitrageEndTimeItem = new QTableWidgetItem(arbitrageEndTime.toString("yyyy-MM-dd hh:mm:ss"));
    m_historyTable->setItem(row, 2, arbitrageEndTimeItem);

    // 设置订单号
    QTableWidgetItem *orderIdItem = new QTableWidgetItem(orderId);
    m_historyTable->setItem(row, 3, orderIdItem);

    // 设置状态
    QTableWidgetItem *statusItem = new QTableWidgetItem(status);
    if (status == "已完成")
    {
        statusItem->setForeground(QBrush(QColor(0, 153, 0))); // 深绿色
    }
    else if (status == "已取消")
    {
        statusItem->setForeground(QBrush(QColor(255, 140, 0))); // 深橙色
    }
    else if (status == "已超时")
    {
        statusItem->setForeground(QBrush(QColor(255, 0, 0))); // 红色
    }
    else if (status == "止损完成")
    {
        statusItem->setForeground(QBrush(QColor(255, 80, 0))); // 橙红色
    }
    m_historyTable->setItem(row, 4, statusItem);

    // 设置下单价格
    QTableWidgetItem *buyPriceItem = new QTableWidgetItem(QString::number(buyPrice, 'f', 4));
    m_historyTable->setItem(row, 5, buyPriceItem);

    // 设置出售价格
    QTableWidgetItem *sellPriceItem = new QTableWidgetItem(QString::number(sellPrice, 'f', 4));
    m_historyTable->setItem(row, 6, sellPriceItem);

    // 设置交易额
    QTableWidgetItem *amountItem = new QTableWidgetItem(QString::number(amount, 'f', 2));
    m_historyTable->setItem(row, 7, amountItem);

    // 设置利润率
    QTableWidgetItem *profitRateItem = new QTableWidgetItem(QString::number(profitRate, 'f', 2) + "%");
    if (profitRate > 0)
    {
        profitRateItem->setForeground(QBrush(QColor(0, 153, 0))); // 深绿色
    }
    else if (profitRate < 0)
    {
        profitRateItem->setForeground(QBrush(QColor(153, 0, 0))); // 深红色
    }
    m_historyTable->setItem(row, 8, profitRateItem);

    // 同时插入数据库
    if (m_databaseManager && m_databaseManager->isConnected())
    {
        m_databaseManager->insertHistoryRecord(orderId, startTime, arbitrageStartTime,
                                               arbitrageEndTime, buyPrice, sellPrice,
                                               amount, profit, status);
    }
}

void MainWindow::initDatabase()
{
    // 创建数据库管理器
    m_databaseManager = new DatabaseManager(this);

    // 初始化数据库
    if (!m_databaseManager->initDatabase())
    {
        qDebug() << "数据库初始化失败";
        QMessageBox::warning(this, "数据库错误", "无法初始化数据库，历史记录将无法保存到数据库。");
        return;
    }

    // 设置数据库表格
    setupDbTableWidget();

    // 设置筛选控件连接
    setupDbFilterConnections();

    qDebug() << "数据库初始化成功";
}

void MainWindow::setupDbTableWidget()
{
    // 设置表格列数和列标题
    ui->dbTableWidget->setColumnCount(9);
    QStringList headers;
    headers << "订单开始时间" << "套利开始时间" << "套利结束时间" << "订单号" << "状态"
            << "买入价格" << "卖出价格" << "交易额" << "利润率";
    ui->dbTableWidget->setHorizontalHeaderLabels(headers);

    // 设置表格样式
    ui->dbTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);                      // 不可编辑
    ui->dbTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);                     // 选择整行
    ui->dbTableWidget->setAlternatingRowColors(true);                                           // 交替行颜色
    ui->dbTableWidget->horizontalHeader()->setStretchLastSection(true);                         // 最后一列自适应宽度
    ui->dbTableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents); // 列宽自适应内容

    // 初始化状态下拉框
    ui->comboBox_orderStatus_db->clear();
    ui->comboBox_orderStatus_db->addItem("全部");
    ui->comboBox_orderStatus_db->addItem("已完成");
    ui->comboBox_orderStatus_db->addItem("已取消");
    ui->comboBox_orderStatus_db->addItem("已超时");
    ui->comboBox_orderStatus_db->addItem("止损完成");
}

void MainWindow::setupDbFilterConnections()
{
    // 连接所有checkbox的状态变化信号
    connect(ui->checkBox_dateTimeEdit_orderStart_db, &QCheckBox::toggled,
            ui->dateTimeEdit_orderStart_db, &QDateTimeEdit::setEnabled);
    connect(ui->checkBox_dateTimeEdit_orderEnd_db, &QCheckBox::toggled,
            ui->dateTimeEdit_orderEnd_db, &QDateTimeEdit::setEnabled);
    connect(ui->checkBox_lineEdit_orderID_db, &QCheckBox::toggled,
            ui->lineEdit_orderID_db, &QLineEdit::setEnabled);
    connect(ui->checkBox_comboBox_orderStatus_db, &QCheckBox::toggled,
            ui->comboBox_orderStatus_db, &QComboBox::setEnabled);
    connect(ui->checkBox_doubleSpinBox_price_db, &QCheckBox::toggled,
            ui->doubleSpinBox_price_db, &QDoubleSpinBox::setEnabled);

    // 连接checkbox的状态变化到槽函数
    connect(ui->checkBox_dateTimeEdit_orderStart_db, &QCheckBox::toggled, this, &MainWindow::onDbFilterCheckBoxToggled);
    connect(ui->checkBox_dateTimeEdit_orderEnd_db, &QCheckBox::toggled, this, &MainWindow::onDbFilterCheckBoxToggled);
    connect(ui->checkBox_lineEdit_orderID_db, &QCheckBox::toggled, this, &MainWindow::onDbFilterCheckBoxToggled);
    connect(ui->checkBox_comboBox_orderStatus_db, &QCheckBox::toggled, this, &MainWindow::onDbFilterCheckBoxToggled);
    connect(ui->checkBox_doubleSpinBox_price_db, &QCheckBox::toggled, this, &MainWindow::onDbFilterCheckBoxToggled);

    // 初始状态下禁用所有筛选控件
    ui->dateTimeEdit_orderStart_db->setEnabled(false);
    ui->dateTimeEdit_orderEnd_db->setEnabled(false);
    ui->lineEdit_orderID_db->setEnabled(false);
    ui->comboBox_orderStatus_db->setEnabled(false);
    ui->doubleSpinBox_price_db->setEnabled(false);
}

QueryFilter MainWindow::buildQueryFilter()
{
    QueryFilter filter;

    // 开始时间筛选
    if (ui->checkBox_dateTimeEdit_orderStart_db->isChecked())
    {
        filter.useStartTime = true;
        filter.startTimeFrom = ui->dateTimeEdit_orderStart_db->dateTime();
        // 可以扩展为时间范围筛选
    }

    // 结束时间筛选
    if (ui->checkBox_dateTimeEdit_orderEnd_db->isChecked())
    {
        filter.useEndTime = true;
        filter.endTimeFrom = ui->dateTimeEdit_orderEnd_db->dateTime();
        // 可以扩展为时间范围筛选
    }

    // 订单ID筛选
    if (ui->checkBox_lineEdit_orderID_db->isChecked())
    {
        filter.useOrderId = true;
        filter.orderId = ui->lineEdit_orderID_db->text().trimmed();
    }

    // 状态筛选
    if (ui->checkBox_comboBox_orderStatus_db->isChecked())
    {
        filter.useStatus = true;
        QString status = ui->comboBox_orderStatus_db->currentText();
        if (status != "全部")
        {
            filter.status = status;
        }
        else
        {
            filter.useStatus = false; // "全部"表示不筛选状态
        }
    }

    // 交易额筛选
    if (ui->checkBox_doubleSpinBox_price_db->isChecked())
    {
        filter.useAmount = true;
        filter.minAmount = ui->doubleSpinBox_price_db->value();
        // 可以扩展为范围筛选
    }

    return filter;
}

void MainWindow::updateDbTableWidget(const QList<QVariantMap> &results)
{
    // 清空现有数据
    ui->dbTableWidget->setRowCount(0);

    // 填充查询结果
    for (int i = 0; i < results.size(); ++i)
    {
        const QVariantMap &record = results[i];
        ui->dbTableWidget->insertRow(i);
        fillDbTableRow(i, record);
    }

    qDebug() << "数据库表格已更新，显示" << results.size() << "条记录";
}

void MainWindow::fillDbTableRow(int row, const QVariantMap &record)
{
    // 解析时间字符串
    QDateTime startTime = QDateTime::fromString(record["start_time"].toString(), Qt::ISODate);
    QDateTime arbitrageStartTime = QDateTime::fromString(record["arbitrage_start_time"].toString(), Qt::ISODate);
    QDateTime arbitrageEndTime = QDateTime::fromString(record["arbitrage_end_time"].toString(), Qt::ISODate);

    // 设置订单开始时间
    QTableWidgetItem *startTimeItem = new QTableWidgetItem(startTime.toString("yyyy-MM-dd hh:mm:ss"));
    ui->dbTableWidget->setItem(row, 0, startTimeItem);

    // 设置套利开始时间
    QTableWidgetItem *arbitrageStartTimeItem = new QTableWidgetItem(arbitrageStartTime.toString("yyyy-MM-dd hh:mm:ss"));
    ui->dbTableWidget->setItem(row, 1, arbitrageStartTimeItem);

    // 设置套利结束时间
    QTableWidgetItem *arbitrageEndTimeItem = new QTableWidgetItem(arbitrageEndTime.toString("yyyy-MM-dd hh:mm:ss"));
    ui->dbTableWidget->setItem(row, 2, arbitrageEndTimeItem);

    // 设置订单号
    QTableWidgetItem *orderIdItem = new QTableWidgetItem(record["order_id"].toString());
    ui->dbTableWidget->setItem(row, 3, orderIdItem);

    // 设置状态（应用颜色）
    QString status = record["status"].toString();
    QTableWidgetItem *statusItem = new QTableWidgetItem(status);
    if (status == "已完成")
    {
        statusItem->setForeground(QBrush(QColor(0, 153, 0))); // 深绿色
    }
    else if (status == "已取消")
    {
        statusItem->setForeground(QBrush(QColor(255, 140, 0))); // 深橙色
    }
    else if (status == "已超时")
    {
        statusItem->setForeground(QBrush(QColor(255, 0, 0))); // 红色
    }
    else if (status == "止损完成")
    {
        statusItem->setForeground(QBrush(QColor(255, 80, 0))); // 橙红色
    }
    ui->dbTableWidget->setItem(row, 4, statusItem);

    // 设置买入价格
    double buyPrice = record["buy_price"].toDouble();
    QTableWidgetItem *buyPriceItem = new QTableWidgetItem(QString::number(buyPrice, 'f', 4));
    ui->dbTableWidget->setItem(row, 5, buyPriceItem);

    // 设置卖出价格
    double sellPrice = record["sell_price"].toDouble();
    QTableWidgetItem *sellPriceItem = new QTableWidgetItem(QString::number(sellPrice, 'f', 4));
    ui->dbTableWidget->setItem(row, 6, sellPriceItem);

    // 设置交易额
    double amount = record["amount"].toDouble();
    QTableWidgetItem *amountItem = new QTableWidgetItem(QString::number(amount, 'f', 2));
    ui->dbTableWidget->setItem(row, 7, amountItem);

    // 计算并设置利润率
    double profit = record["profit"].toDouble();
    double profitRate = (amount > 0) ? (profit / amount) * 100 : 0;
    QTableWidgetItem *profitRateItem = new QTableWidgetItem(QString::number(profitRate, 'f', 2) + "%");
    if (profitRate > 0)
    {
        profitRateItem->setForeground(QBrush(QColor(0, 153, 0))); // 深绿色
    }
    else if (profitRate < 0)
    {
        profitRateItem->setForeground(QBrush(QColor(153, 0, 0))); // 深红色
    }
    ui->dbTableWidget->setItem(row, 8, profitRateItem);
}

void MainWindow::on_btn_querry_clicked()
{
    if (!m_databaseManager || !m_databaseManager->isConnected())
    {
        QMessageBox::warning(this, "数据库错误", "数据库未连接，无法查询数据。");
        return;
    }

    // 构建查询筛选条件
    QueryFilter filter = buildQueryFilter();

    // 执行数据库查询
    QList<QVariantMap> results = m_databaseManager->queryHistoryRecords(filter);

    // 更新表格显示
    updateDbTableWidget(results);

    // 显示查询结果统计
    ui->statusbar->showMessage(QString("查询完成，共找到 %1 条记录").arg(results.size()), 3000);
}

void MainWindow::on_btn_exportEXCEL_clicked()
{
    // 检查是否有数据
    if (ui->dbTableWidget->rowCount() == 0)
    {
        QMessageBox::information(this, "提示", "没有数据可以导出，请先执行查询。");
        return;
    }

    // 文件选择对话框
    QString defaultFileName = QString("历史记录_%1.csv").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss"));
    QString fileName = QFileDialog::getSaveFileName(
        this,
        "导出数据文件",
        defaultFileName,
        "CSV文件(Excel兼容) (*.csv);;UTF-8 CSV文件 (*.csv);;所有文件 (*)");

    if (fileName.isEmpty())
    {
        return;
    }

    // 执行导出
    if (exportToExcel(fileName))
    {
        QMessageBox::information(this, "成功", "数据导出成功！\n\n提示：如果Excel中出现乱码，请使用\"数据\"→\"从文本\"功能导入CSV文件，并选择UTF-8编码。");
        ui->statusbar->showMessage("数据导出成功", 3000);
    }
    else
    {
        QMessageBox::warning(this, "失败", "数据导出失败！");
    }
}

bool MainWindow::exportToExcel(const QString &fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        qDebug() << "无法创建导出文件:" << fileName;
        return false;
    }

    QTextStream out(&file);

    // 根据文件名或用户选择决定编码方式
    // 默认使用GBK编码，这样Excel可以直接正确显示中文
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    out.setCodec("GBK"); // Qt5使用GBK编码
#else
    // Qt6中，使用本地编码（通常是GBK/GB2312）
    out.setEncoding(QStringConverter::System);
#endif

    // 写入表头
    QStringList headers;
    for (int col = 0; col < ui->dbTableWidget->columnCount(); ++col)
    {
        headers << ui->dbTableWidget->horizontalHeaderItem(col)->text();
    }
    out << headers.join(",") << "\n";

    // 写入数据行
    for (int row = 0; row < ui->dbTableWidget->rowCount(); ++row)
    {
        QStringList rowData;
        for (int col = 0; col < ui->dbTableWidget->columnCount(); ++col)
        {
            QTableWidgetItem *item = ui->dbTableWidget->item(row, col);
            QString cellData = item ? item->text() : "";

            // 如果数据包含逗号或引号，需要用引号包围并转义
            if (cellData.contains(",") || cellData.contains("\"") || cellData.contains("\n"))
            {
                cellData.replace("\"", "\"\""); // 转义引号
                cellData = "\"" + cellData + "\"";
            }

            rowData << cellData;
        }
        out << rowData.join(",") << "\n";
    }

    file.close();
    qDebug() << "数据导出完成:" << fileName;
    return true;
}

void MainWindow::onDbFilterCheckBoxToggled()
{
    // 这个槽函数可以用于实时更新或其他逻辑
    // 目前主要是通过信号连接自动启用/禁用控件
    qDebug() << "数据库筛选条件已更改";
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    // 显示确认对话框
    QMessageBox::StandardButton reply = QMessageBox::question(this, "确认退出", "确定要关闭软件吗？\n如果有进行中的套利订单，关闭软件可能会导致无法及时操作。",
                                                              QMessageBox::Yes | QMessageBox::No,
                                                              QMessageBox::No // 默认选择"否"按钮
    );

    if (reply != QMessageBox::Yes)
    {
        // 用户选择不关闭，忽略关闭事件
        event->ignore();
        return;
    }

    qDebug().noquote() << "MainWindow正在关闭...";

    // 首先断开所有信号连接，防止后续操作触发任何回调
    disconnectAllConnections();
    qDebug().noquote() << "所有信号连接已断开";

    // 停止所有定时器
    if (m_refreshTimer)
    {
        if (m_refreshTimer->isActive())
        {
            // 如果定时器正在运行，先尝试以安全的方式停止
            m_refreshTimer->disconnect(); // 断开所有连接
            if (m_refreshTimer->thread() == QThread::currentThread())
            {
                // 如果在同一线程中，可以直接停止
                m_refreshTimer->stop();
            }
            else
            {
                // 安排定时器延迟删除
                m_refreshTimer->deleteLater();
            }
        }
        qDebug().noquote() << "账户信息刷新定时器已停止";
    }

    if (m_priceRefreshTimer)
    {
        if (m_priceRefreshTimer->isActive())
        {
            m_priceRefreshTimer->disconnect();
            if (m_priceRefreshTimer->thread() == QThread::currentThread())
            {
                m_priceRefreshTimer->stop();
            }
            else
            {
                m_priceRefreshTimer->deleteLater();
            }
        }
        qDebug().noquote() << "价格信息刷新定时器已停止";
    }

    if (m_arbitrageTimer)
    {
        if (m_arbitrageTimer->isActive())
        {
            m_arbitrageTimer->disconnect();
            if (m_arbitrageTimer->thread() == QThread::currentThread())
            {
                m_arbitrageTimer->stop();
            }
            else
            {
                m_arbitrageTimer->deleteLater();
            }
        }
        qDebug().noquote() << "套利定时器已停止";
    }

    // 重置CurrentOrder单例
    CurrentOrder *currentOrder = CurrentOrder::getInstance();
    currentOrder->disconnect();
    currentOrder->resetToInitial();
    qDebug().noquote() << "当前订单状态变化： \"初始状态\"";
    qDebug().noquote() << "订单对象已恢复初始状态";
    CurrentOrder::releaseInstance();
    qDebug().noquote() << "CurrentOrder单例已释放";

    // 停止WebSocket连接（不直接调用apiWorker的方法）
    if (m_apiWorker)
    {
        qDebug().noquote() << "正在停止WebSocket连接...";
        // 只断开连接，不调用stopUserDataStream，避免跨线程操作
        m_apiWorker->disconnect();
        qDebug().noquote() << "WebSocket连接已断开";
    }

    // 写入关闭日志
    qDebug().noquote() << "MainWindow资源已清理完毕，准备关闭";

    // 继续关闭事件处理
    event->accept();
}

void MainWindow::disconnectAllConnections()
{
    // 断开API工作对象的所有信号连接
    if (m_apiWorker)
    {
        disconnect(m_apiWorker, nullptr, this, nullptr);
        disconnect(this, nullptr, m_apiWorker, nullptr);
    }

    // 断开账户信息对象的所有信号连接
    if (m_accountInfo)
    {
        disconnect(m_accountInfo, nullptr, this, nullptr);
    }

    // 不再使用OrderManager，这段代码可以删除
    // if (m_orderManager) {
    //    disconnect(m_orderManager, nullptr, this, nullptr);
    // }

    // 断开CurrentOrder单例的所有信号连接
    CurrentOrder *currentOrder = CurrentOrder::getInstance();
    disconnect(currentOrder, nullptr, this, nullptr);

    // 断开所有定时器的连接
    if (m_refreshTimer)
    {
        disconnect(m_refreshTimer, nullptr, this, nullptr);
    }

    if (m_priceRefreshTimer)
    {
        disconnect(m_priceRefreshTimer, nullptr, this, nullptr);
    }
}

void MainWindow::checkBinanceOpenOrders()
{
    // 检查币安平台上的挂单
    emit requestOpenOrders(m_symbol);
}

void MainWindow::proceedWithOrder()
{
    // 确保重新下单提示隐藏
    ui->label_newOrderMsg->setVisible(false);

    qDebug().noquote() << "发送下单请求:";
    qDebug().noquote() << "交易对: \"" << m_pendingOrderSymbol << "\"";
    qDebug().noquote() << "方向: \"" << m_pendingOrderSide << "\"";
    qDebug().noquote() << "类型: \"" << m_pendingOrderType << "\"";
    qDebug().noquote() << "数量: \"" << m_pendingOrderQuantity << "\"";
    qDebug().noquote() << "价格: \"" << m_pendingOrderPrice << "\"";

    // 继续下单流程
    if (!m_isPlacingOrder)
    {
        qDebug().noquote() << "未处于下单流程中，无法继续";
        if (!ui->checkBox_automatic->isChecked())
        {
            // 确保下单按钮可用
            ui->btn_order->setEnabled(true);
        }
        return;
    }

    // 更新状态栏
    ui->statusbar->showMessage("正在发送下单请求...");
    QApplication::processEvents(); // 立即更新UI

    // 发送下单请求
    emit requestPlaceOrder(m_pendingOrderSymbol, m_pendingOrderSide, m_pendingOrderType, m_pendingOrderQuantity, m_pendingOrderPrice, QString());

    // 注意：下单按钮将在handleOrderPlaced方法中重新启用
}

void MainWindow::handleOpenOrdersReceived(bool success, const QJsonArray &orders, const QString &errorMessage)
{
    // 如果是等待重新下单的情况
    if (m_waitForNewOrder)
    {
        if (success)
        {
            qDebug().noquote() << "收到挂单查询结果，检查是否可以重新下单";

            // 检查是否有当前交易对的挂单
            bool hasOpenOrders = false;
            for (int i = 0; i < orders.size(); ++i)
            {
                QJsonObject order = orders[i].toObject();
                if (order["symbol"].toString() == m_symbol)
                {
                    hasOpenOrders = true;
                    qDebug().noquote() << "发现平台上仍有" << m_symbol << "的挂单，ID:" << order["orderId"].toVariant().toString();
                    break;
                }
            }

            // 如果没有挂单，则进行重新下单
            if (!hasOpenOrders)
            {
                qDebug().noquote() << "平台上没有" << m_symbol << "的挂单，可以安全地重新下单";

                // 显示正在重新下单的提示
                ui->label_newOrderMsg->setVisible(true);

                // 先将当前订单对象重置为初始状态，避免状态不一致
                CurrentOrder *currentOrder = CurrentOrder::getInstance();
                currentOrder->resetToInitial();
                qDebug().noquote() << "已重置订单对象状态为初始状态，准备重新下单";

                // 重置订单队列跟踪器
                OrderQueuePositionTracker::getInstance()->reset();
                qDebug().noquote() << "已重置订单队列跟踪器";

                // 更新UI显示
                updateCurrentOrderDisplay();

                // 获取当前买一价
                double currentBidPrice = m_bidPrice.toDouble();

                // 获取原始订单数量 (从上一个订单保存下来)
                double originalQuantity = ui->spinBox_price->value() / m_lastOrderPrice.toDouble();
                if (originalQuantity < 1.0)
                    originalQuantity = 6.0; // 安全检查

                // 根据当前买一价和原始总投资金额计算新的数量
                double newQuantity = m_lastTotalInvestment / currentBidPrice;

                // 向下取整到整数
                newQuantity = floor(newQuantity);

                // 确保新数量不小于原始订单数量
                if (newQuantity < originalQuantity)
                {
                    newQuantity = originalQuantity;
                    qDebug().noquote() << "计算的新数量小于原始数量，使用原始数量: " << originalQuantity;
                }

                // 确保新数量不小于6.0
                if (newQuantity < 6.0)
                {
                    newQuantity = 6.0;
                    qDebug().noquote() << "计算的新数量小于6.0 USDC，使用最小数量: 6.0";
                }

                double usdtBalance = ui->usdtFreeBalanceLabel->text().toDouble();

                if (usdtBalance < 6.00)
                {
                    qDebug().noquote() << "账户可用余额小于6.0USDC取消自动重新下单";
                    ui->statusbar->showMessage("账户可用余额小于6.0USDC取消自动重新下单", 5000);
                    ui->label_newOrderMsg->setVisible(false); // 隐藏重新下单提示
                    m_waitForNewOrder = false;                // 重置标记

                    // 确保按钮和复选框可用状态
                    ui->btn_order->setEnabled(true);
                    ui->checkBox_automatic->setEnabled(true);

                    // 重置当前订单对象状态为初始状态
                    CurrentOrder::getInstance()->resetToInitial();

                    // 更新UI显示
                    updateCurrentOrderDisplay();

                    return;
                }

                qDebug().noquote() << "准备以新价格重新下单: 价格=" << currentBidPrice << ", 数量=" << newQuantity;

                // 设置待处理订单参数
                m_pendingOrderSymbol = m_symbol;
                m_pendingOrderSide = "BUY";
                m_pendingOrderType = "LIMIT";
                m_pendingOrderQuantity = QString::number(newQuantity, 'f', 0);
                m_pendingOrderPrice = m_bidPrice; // 使用当前买一价

                // 重置等待标记
                m_waitForNewOrder = false;

                // 设置下单标记，确保下单请求能被正确处理
                m_isPlacingOrder = true;

                // 禁用下单按钮，防止用户在API请求期间重复操作
                ui->btn_order->setEnabled(false);

                // 显示正在下单的状态信息
                ui->statusbar->showMessage("正在以新价格下单: " + m_bidPrice + "，请等待...", 5000);

                // 延长延迟时间到1500ms，给API服务器足够时间完成之前的操作
                qDebug().noquote() << "添加1.5秒延迟，确保API请求间隔合理";
                QTimer::singleShot(1500, this, [this]()
                                   {
                    // 隐藏重新下单提示
                    ui->label_newOrderMsg->setVisible(false);
                    proceedWithOrder(); });
            }
            else
            {
                qDebug().noquote() << "平台上仍有挂单，等待挂单清除后才能重新下单";
                ui->statusbar->showMessage("等待平台上的挂单清除后自动重新下单...", 3000);

                // 2秒后再次检查
                QTimer::singleShot(2000, this, [this]()
                                   {
                    if (m_waitForNewOrder) {
                        checkBinanceOpenOrders();
                    } });
            }
        }
        else
        {
            // 查询失败，记录错误并重置标记
            qDebug().noquote() << "查询挂单失败：" << errorMessage;
            ui->statusbar->showMessage("查询挂单失败，取消自动重新下单", 5000);
            ui->label_newOrderMsg->setVisible(false); // 隐藏重新下单提示
            m_waitForNewOrder = false;
        }

        return; // 已处理重新下单逻辑，不执行后续代码
    }

    // 原有的handleOpenOrdersReceived函数逻辑
    if (success)
    {
        // qDebug().noquote() << "获取挂单成功，共有 " << orders.size() << " 个挂单";

        // 处理平台上的挂单信息
        bool hasPendingOrder = false;
        QString pendingOrderId;
        QString pendingOrderSymbol;
        QString pendingOrderSide;
        double pendingOrderPrice = 0.0;
        QString pendingOrderStatus;

        for (int i = 0; i < orders.size(); ++i)
        {
            QJsonObject order = orders[i].toObject();
            // 检查是否有当前交易对的挂单
            if (order["symbol"].toString() == m_symbol)
            {
                hasPendingOrder = true;
                pendingOrderId = QString::number(order["orderId"].toDouble());
                pendingOrderSymbol = order["symbol"].toString();
                pendingOrderSide = order["side"].toString();
                pendingOrderPrice = order["price"].toString().toDouble();
                pendingOrderStatus = order["status"].toString();

                // qDebug().noquote() << "发现挂单：";
                // qDebug().noquote() << "订单ID: " << pendingOrderId;
                // qDebug().noquote() << "交易对: " << pendingOrderSymbol;
                // qDebug().noquote() << "方向: " << pendingOrderSide;
                // qDebug().noquote() << "价格: " << pendingOrderPrice;
                // qDebug().noquote() << "状态: " << pendingOrderStatus;

                // 更新当前订单ID
                m_currentOrderId = pendingOrderId;

                // 更新订单显示
                updateCurrentOrderDisplay();

                break;
            }
        }

        // 如果正在下单流程中，检查是否允许继续下单
        if (m_isPlacingOrder && !m_pendingOrderSymbol.isEmpty())
        {
            if (hasPendingOrder)
            {
                // 已有挂单，提示用户
                QString message = "平台上已有挂单，请先取消或等待其成交后再下单。";
                ui->statusbar->showMessage(message, 5000);
                QMessageBox::warning(this, "下单警告", message);
                qDebug().noquote() << message;

                // 重置下单流程状态
                m_isPlacingOrder = false;
                m_pendingOrderSymbol = "";
                m_pendingOrderSide = "";
                m_pendingOrderType = "";
                m_pendingOrderQuantity = "";
                m_pendingOrderPrice = "";
                // 确保下单按钮可用
                ui->btn_order->setEnabled(true);
                ui->checkBox_automatic->setEnabled(true);
                ui->checkBox_automatic->setChecked(false);
                // 隐藏重新下单提示，因为这不是重新下单的情况
                ui->label_newOrderMsg->setVisible(false);

                // 恢复初始状态
                CurrentOrder::getInstance()->resetToInitial();

                // 更新UI显示
                updateCurrentOrderDisplay();
                // // 启用下单按钮
                // if (!ui->checkBox_automatic->isChecked()) {
                //     // 确保下单按钮可用
                //     ui->btn_order->setEnabled(true);
                // }
            }
            else
            {
                // 没有挂单，可以继续下单
                qDebug().noquote() << "平台上没有挂单，可以继续下单流程";
                proceedWithOrder();
            }
        }
        else
        {
            // 正常显示挂单状态
            if (!hasPendingOrder)
            {
                // 如果没有挂单，并且当前订单ID不为空，则表示之前有挂单但现在没有了
                if (!m_currentOrderId.isEmpty())
                {
                    qDebug().noquote() << "订单 " << m_currentOrderId << " 不在挂单列表中";

                    // 查询该订单的状态 不用查询了，改为websocket订阅了
                    // emit requestOrderStatus(m_symbol, m_currentOrderId);

                    // 清空当前订单ID
                    m_currentOrderId = "";

                    // 更新UI显示
                    updateCurrentOrderDisplay();
                }

                qDebug().noquote() << "平台上没有挂单";
                ui->statusbar->showMessage("平台上没有挂单", 3000);
            }
            else
            {
                ui->statusbar->showMessage("平台上有挂单：" + pendingOrderId, 3000);
            }
        }
    }
    else
    {
        // 获取挂单失败
        qDebug().noquote() << "获取挂单失败：" << errorMessage;

        QString friendlyErrorMessage = getFriendlyErrorMessage(errorMessage);
        ui->statusbar->showMessage("获取挂单失败：" + friendlyErrorMessage, 5000);

        // 如果是在下单流程中，重置状态
        if (m_isPlacingOrder)
        {
            m_isPlacingOrder = false;
            if (!ui->checkBox_automatic->isChecked())
            {
                // 确保下单按钮可用
                ui->btn_order->setEnabled(true);
            }
        }
    }
}

// 实现updateCurrentOrderDisplay方法
void MainWindow::updateCurrentOrderDisplay()
{
    // 获取当前订单单例
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 如果处于初始状态且不是等待重新下单状态，清空显示
    if (currentOrder->isInitialState())
    {
        // 清空显示
        ui->label_orderId->setText("");
        ui->label_orderCount->setText("");
        ui->label_buyPrice->setText("");
        ui->label_totalCount->setText("");
        ui->label_orderTime->setText("");
        ui->label_orderStatus->setText("无订单");
        ui->label_orderStatus->setStyleSheet("color: #000000;");

        // 处理结束套利按钮
        ui->btn_cancle->setVisible(false);
        ui->btn_cancle->setEnabled(false);

        // 如果不是等待重新下单状态，隐藏提示
        if (!m_waitForNewOrder)
        {
            ui->label_newOrderMsg->setVisible(false);
        }

        return;
    }

    // 当显示非初始状态的订单信息时，确保重新下单提示是隐藏的
    // 除非明确在等待重新下单
    if (!m_waitForNewOrder)
    {
        ui->label_newOrderMsg->setVisible(false);
    }

    // 获取订单信息
    bool isSellingState = currentOrder->getStatus() == CurrentOrderStatus::ARBITRAGING ||
                          currentOrder->getStatus() == CurrentOrderStatus::STOP_LOSS_FIRST_DROP ||
                          currentOrder->getStatus() == CurrentOrderStatus::STOP_LOSS_SECOND_DROP;

    QString orderId = isSellingState ? currentOrder->getSellOrderId() : currentOrder->getBuyOrderId();

    // 设置订单ID
    ui->label_orderId->setText(orderId);

    // 设置数量
    ui->label_orderCount->setText(currentOrder->getBuyQuantity());

    // 根据状态显示价格
    if (isSellingState)
    {
        ui->label_buyPrice->setText(QString::number(currentOrder->getSellPrice(), 'f', 4));
    }
    else
    {
        ui->label_buyPrice->setText(QString::number(currentOrder->getBuyPrice(), 'f', 4));
    }

    // 计算总金额
    double price = isSellingState ? currentOrder->getSellPrice() : currentOrder->getBuyPrice();
    double quantity = currentOrder->getBuyQuantity().toDouble();
    double total = price * quantity;
    ui->label_totalCount->setText(QString::number(total, 'f', 2));

    // 设置订单时间
    QDateTime orderTime = isSellingState ? currentOrder->getSellOrderTime() : currentOrder->getBuyOrderTime();
    ui->label_orderTime->setText(orderTime.toString("yyyy-MM-dd hh:mm:ss"));

    // 设置订单状态
    ui->label_orderStatus->setText(currentOrder->getStatusText());
    ui->label_orderStatus->setStyleSheet(currentOrder->getStatusColor());

    // 处理结束套利按钮
    ui->btn_cancle->setVisible(true);
    ui->btn_cancle->setEnabled(true);
    ui->btn_cancle->setText("结束套利");
}

// 实现updateAccountInfo方法
void MainWindow::updateAccountInfo()
{
    // 更新账户信息
    updateAccountInfoDisplay();
}

// 实现updateArbitrageStatus方法
void MainWindow::updateArbitrageStatus()
{
    // 暂时为空实现，后续可以添加套利状态更新逻辑
    qDebug().noquote() << "更新套利状态";
}

// 处理自动登录
void MainWindow::onAutoLogin()
{
    qDebug().noquote() << "执行自动登录";

    // 从配置中读取API密钥和Secret
    QSettings settings("BinanceArbitrage", "Settings");
    QString apiKey = settings.value("ApiKey", "").toString();
    QString secretKey = settings.value("SecretKey", "").toString();

    if (!apiKey.isEmpty() && !secretKey.isEmpty())
    {
        // 设置API密钥和Secret
        if (m_apiWorker)
        {
            m_apiWorker->setApiKeys(apiKey, secretKey);
            // 设置API权重输出阈值
            m_apiWorker->setApiWeightOutputThreshold(m_apiWeightOutputThreshold);
            // 验证API密钥
            m_apiWorker->validateApiKeys();
        }
    }
}

// 处理买单成交
void MainWindow::handleBuyOrderFilled(const QString &orderId, double price, const QString &quantity)
{
    qDebug().noquote() << "买单成交：订单ID=" << orderId << "，价格=" << price << "，数量=" << quantity;

    // 取消买入价格的订阅
    unsubscribeFromPriceTrade();
    qDebug().noquote() << "买单成交，取消买入价格交易数据订阅";

    // 更新UI显示
    QString message = QString("买单已成交：价格=%1，数量=%2").arg(price).arg(quantity);
    ui->statusbar->showMessage(message, 5000);

    // 更新UI显示订单状态
    updateCurrentOrderDisplay();

    // 更新账户信息
    refreshAccountInfo();

    // 获取当前订单对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 更新状态为套利中
    currentOrder->setOrderStatus(CurrentOrderStatus::ARBITRAGING);

    if (m_apiWorker)
    {
        // 保存卖单挂单价格
        double sellPrice = price + SELL_PRICE_UP;

        // 保存原始套利卖单价格，用于后续的价格回升检测
        currentOrder->setOriginalSellPrice(sellPrice);
        qDebug().noquote() << "保存原始套利卖单价格:" << sellPrice;

        // 记录卖单下单时间
        currentOrder->m_sellOrderTime = QDateTime::currentDateTime();

        // 设置等待卖单确认标志
        m_waitingForSellOrderConfirmation = true;

        // 发送卖出订单请求 - 使用QMetaObject::invokeMethod确保在正确的线程中执行
        qDebug() << "正在提交卖单，使用买一价格+: " << SELL_PRICE_UP << "=" << sellPrice;
        qDebug() << "下单请求关联买单ID: \"" << CurrentOrder::getInstance()->m_buyOrderId << "\"";

        // 使用QueuedConnection确保在正确的线程中执行
        QMetaObject::invokeMethod(m_apiWorker, "placeOrder", Qt::QueuedConnection, Q_ARG(QString, m_symbol), // 交易对
                                  Q_ARG(QString, "SELL"),                                                    // 订单方向
                                  Q_ARG(QString, "LIMIT"),                                                   // 订单类型
                                  Q_ARG(QString, quantity),                                                  // 数量（与买入数量相同）
                                  Q_ARG(QString, QString::number(sellPrice, 'f', 4)),                        // 价格
                                  Q_ARG(QString, CurrentOrder::getInstance()->m_buyOrderId)                  // 关联买单ID
        );

        qDebug() << "卖出请求已发送，等待卖单创建";
    }
}

void MainWindow::handleOrderStatusUpdated(QString orderId, QString symbol, QString side, QString status, qint64 updateTime)
{
    // 创建唯一的事件键
    QString eventKey = orderId + "_" + status + "_" + QString::number(updateTime);

    // 检查是否已处理过此事件
    if (isEventProcessed(eventKey))
    {
        return;
    }

    qDebug().noquote() << "订单状态更新 - ID:" << orderId << "状态:" << status;

    // 获取当前订单单例
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 检查这个订单是否是当前跟踪的订单
    bool isBuyOrder = (orderId == currentOrder->getBuyOrderId());
    bool isSellOrder = (orderId == currentOrder->getSellOrderId());

    // 检查是否是待处理的强制止损订单
    QString pendingStopLossClientOrderId = currentOrder->getPendingStopLossClientOrderId();
    bool isStopLossOrder = false;

    // 如果有待处理的止损订单，检查WebSocket消息中是否包含相应的clientOrderId
    // 注意：由于WebSocket消息中不包含clientOrderId，我们无法直接通过这种方式匹配
    // 但是，如果存在待处理的止损订单ID，且当前订单不是已知的买单或卖单，则可能是止损订单
    if (!pendingStopLossClientOrderId.isEmpty() && side.toUpper() == "SELL" && !isBuyOrder && !isSellOrder)
    {
        qDebug().noquote() << "检测到可能的止损订单状态更新，orderId: " << orderId;
        qDebug().noquote() << "待处理的止损clientOrderId: " << pendingStopLossClientOrderId;
        isStopLossOrder = true;

        // 如果是止损订单且状态为已成交，则处理止损订单成交
        if (status == "FILLED")
        {
            qDebug().noquote() << "止损订单已成交，更新状态";

            // 更新当前订单的卖单ID和状态
            currentOrder->setSellOrderId(orderId);
            currentOrder->setOrderStatus(CurrentOrderStatus::ARBITRAGING); // 设置为套利中状态

            // 清除待处理的止损clientOrderId
            currentOrder->clearPendingStopLossClientOrderId();

            // 如果有时间戳，记录卖单成交时间
            if (updateTime > 0)
            {
                QDateTime fillTime = QDateTime::fromMSecsSinceEpoch(updateTime);
                currentOrder->setSellOrderFillTime(fillTime);
                qDebug().noquote() << "止损订单成交时间(从API)：" << fillTime.toString("yyyy-MM-dd hh:mm:ss.zzz");
            }

            // 通知CurrentOrder处理卖单成交
            currentOrder->handleSellOrderFilled(true, status, orderId, "");

            // 更新UI显示
            updateCurrentOrderDisplay();

            // 刷新账户信息
            refreshAccountInfo();

            return; // 处理完止损订单后直接返回
        }
    }

    if (!isBuyOrder && !isSellOrder && !isStopLossOrder)
    {
        // 不是当前跟踪的订单
        qDebug().noquote() << "警告：收到的订单状态更新不是当前跟踪的订单";
        return;
    }

    // 根据订单状态进行处理
    if (status == "FILLED")
    {
        // 订单已成交
        if (isBuyOrder)
        {
            // 检查当前订单的状态，避免重复处理买单成交
            if (currentOrder->getStatus() == CurrentOrderStatus::ARBITRAGING)
            {
                qDebug().noquote() << "当前已处于套利中状态，忽略API查询返回的买单成交事件";
                return;
            }

            // 买单成交，通知CurrentOrder处理
            currentOrder->handleBuyOrderFilled(true, status, orderId, "");

            // 买单成交后立即获取最新价格，检查是否可以立即套利
            refreshPriceInfo();
        }
        else if (isSellOrder)
        {
            // 卖单成交，通知CurrentOrder处理

            // 如果有时间戳，记录卖单成交时间
            if (updateTime > 0)
            {
                QDateTime fillTime = QDateTime::fromMSecsSinceEpoch(updateTime);
                currentOrder->setSellOrderFillTime(fillTime);
                qDebug().noquote() << "卖单成交时间(从API)：" << fillTime.toString("yyyy-MM-dd hh:mm:ss.zzz");
            }
            else
            {
                // 如果没有时间戳，使用当前时间
                QDateTime now = QDateTime::currentDateTime();
                currentOrder->setSellOrderFillTime(now);
                qDebug().noquote() << "卖单成交时间(本地)：" << now.toString("yyyy-MM-dd hh:mm:ss.zzz");
            }

            currentOrder->handleSellOrderFilled(true, status, orderId, "");
        }

        // 更新UI显示
        updateCurrentOrderDisplay();

        // 刷新账户信息
        refreshAccountInfo();
    }
    else if (status == "CANCELED" || status == "EXPIRED" || status == "REJECTED")
    {
        // 订单已取消或过期
        currentOrder->handleOrderCancelled(true, orderId, "");

        // 更新UI显示
        updateCurrentOrderDisplay();
    }
    else if (status == "NEW" || status == "PARTIALLY_FILLED")
    {
        // 订单状态为新建或部分成交，更新UI显示
        updateCurrentOrderDisplay();
    }
    else
    {
        // 其他状态，记录日志
        qDebug().noquote() << "收到未处理的订单状态：" << status;
    }

    // 更新状态栏显示
    QString statusMessage = QString("订单 %1 状态更新：%2").arg(orderId).arg(status);
    ui->statusbar->showMessage(statusMessage, 3000);
}

void MainWindow::setupInitialUI()
{
    qDebug() << "Setting up initial UI...";

    // 初始化UI组件状态
    if (ui)
    {
        // 设置订单状态标签的初始文本
        if (ui->label_orderStatus)
        {
            ui->label_orderStatus->setText("初始状态");
        }

        // 清空订单相关显示
        if (ui->label_orderId)
        {
            ui->label_orderId->setText("-");
        }
        if (ui->label_orderCount)
        {
            ui->label_orderCount->setText("-");
        }
        if (ui->label_buyPrice)
        {
            ui->label_buyPrice->setText("-");
        }
        if (ui->label_totalCount)
        {
            ui->label_totalCount->setText("-");
        }
        if (ui->label_orderTime)
        {
            ui->label_orderTime->setText("-");
        }

        // 设置按钮状态
        if (ui->btn_order)
        {
            if (!ui->checkBox_automatic->isChecked())
            {
                // 确保下单按钮可用
                ui->btn_order->setEnabled(true);
            }
        }
        if (ui->btn_cancle)
        {
            ui->btn_cancle->setEnabled(false);
            ui->btn_cancle->setText("结束套利");
        }
    }

    qDebug() << "Initial UI setup completed";
}

// 添加在setupInitialUI函数后
void MainWindow::initSoundPlayers()
{
    qDebug() << "初始化音频播放器";
    // 创建音频输出对象
    m_audioOutput_success = new QAudioOutput(this);
    m_audioOutput_success->setVolume(1.0); // 设置音量为100%
    m_audioOutput_gettingMoney = new QAudioOutput(this);
    m_audioOutput_gettingMoney->setVolume(1.0); // 设置音量为100%

    // 创建套利完成音频播放器
    m_successSoundPlayer = new QMediaPlayer(this);
    m_successSoundPlayer->setAudioOutput(m_audioOutput_success);
    m_successSoundPlayer->setSource(QUrl("qrc:/资源/success.mp3"));

    // 创建套利中状态音频播放器
    m_gettingMoneySoundPlayer = new QMediaPlayer(this);
    m_gettingMoneySoundPlayer->setAudioOutput(m_audioOutput_gettingMoney);
    m_gettingMoneySoundPlayer->setSource(QUrl("qrc:/资源/gettingMoney.mp3"));
}

// 修改超时处理函数
void MainWindow::onArbitrageTimeout()
{
    // qDebug().noquote() << "套利超时检查触发...";

    // 如果未启用超时，直接返回
    if (!ui->checkBox_useTimeOut->isChecked())
    {
        m_arbitrageTimer->stop();
        qDebug().noquote() << "套利超时功能未启用，停止定时器";
        return;
    }

    // 获取当前时间
    QDateTime currentTime = QDateTime::currentDateTime();

    // 检查是否已超时
    if (currentTime >= m_timeoutTime)
    {
        qDebug().noquote() << "套利已超时！当前时间：" << currentTime.toString("yyyy-MM-dd hh:mm:ss") << "，超时设定："
                           << m_timeoutTime.toString("yyyy-MM-dd hh:mm:ss");

        // 停止定时器
        m_arbitrageTimer->stop();

        // 重新启用超时设置控件
        ui->checkBox_useTimeOut->setEnabled(true);
        ui->dateTimeEdit_timeOut->setEnabled(ui->checkBox_useTimeOut->isChecked());

        // 获取当前订单对象
        CurrentOrder *currentOrder = CurrentOrder::getInstance();

        // 获取当前订单状态
        CurrentOrderStatus status = currentOrder->getStatus();

        // 如果当前有活跃订单，取消它
        if (status != CurrentOrderStatus::INITIAL)
        {
            // 执行取消操作
            currentOrder->cancelCurrentOrder();

            // 恢复初始状态
            currentOrder->resetToInitial();

            ui->checkBox_automatic->setEnabled(true);
            ui->checkBox_automatic->setChecked(false);
            // 关闭全自动模式并恢复到最初状态
            if (!ui->checkBox_automatic->isChecked())
            {
                // 确保下单按钮可用
                ui->btn_order->setEnabled(true);
            }
            orderDialogShowFlag = true;

            // 更新界面
            updateCurrentOrderDisplay();

            // 更新状态栏
            ui->statusbar->showMessage("套利已自动停止（时间到），所有订单已撤销", 5000);

            // 刷新账户信息
            refreshAccountInfo();

            qDebug().noquote() << "已执行结束套利操作";

            // 弹出超时提示
            QMessageBox::information(this, "套利超时", "套利已超过设定时间，系统将自动结束当前套利流程。");
        }
    }
    else
    {
        // 未超时，继续计时
        // qDebug().noquote() << "套利未超时，继续计时。当前时间：" << currentTime.toString("yyyy-MM-dd hh:mm:ss")
        //                   << "，超时设定：" << m_timeoutTime.toString("yyyy-MM-dd hh:mm:ss");

        // 不停止定时器，继续每秒检查一次
    }
}

// 添加处理订单簿深度数据的方法
void MainWindow::handleOrderBookDepthUpdated(const QJsonObject &depthData)
{
    // 检查数据有效性
    if (!depthData.contains("bids") || !depthData.contains("asks"))
    {
        qDebug().noquote() << "订单簿深度数据无效，缺少买单或卖单数据";
        return;
    }

    QJsonArray bids = depthData["bids"].toArray();
    QJsonArray asks = depthData["asks"].toArray();

    if (bids.isEmpty() || asks.isEmpty())
    {
        qDebug().noquote() << "订单簿深度数据中买单或卖单为空";
        return;
    }

    // 获取当前订单对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();
    CurrentOrderStatus currentStatus = currentOrder->getStatus();

    // 将订单簿深度数据传递给OrderQueuePositionTracker
    OrderQueuePositionTracker::getInstance()->handleOrderBookDepthUpdated(depthData);

    // 只在特定状态下记录日志，避免过多日志输出
    if (currentStatus == CurrentOrderStatus::BUY_PENDING)
    {
        // 买单挂单状态下，记录订单队列位置信息
        OrderQueuePositionTracker *tracker = OrderQueuePositionTracker::getInstance();
        if (tracker->getCurrentPositionPercent() > 0.0)
        {
            // 队列位置有更新，添加日志（低频率）
            static QDateTime lastLogTime = QDateTime::currentDateTime().addSecs(-60); // 初始化为过去时间
            if (lastLogTime.secsTo(QDateTime::currentDateTime()) >= 10)
            { // 每10秒最多记录一次
                double filledAmount = tracker->getFilledAmount();
                double initialAmount = tracker->getInitialTotalAmount();
                double remainingAmount = initialAmount - filledAmount;
                // qDebug().noquote() << "订单队列位置：" << (tracker->getCurrentPositionPercent() * 100) << "%，"
                //                    << "剩余：" << remainingAmount << "，"
                //                    << "已成交：" << filledAmount << "，"
                //                    << "初始总量：" << initialAmount;
                lastLogTime = QDateTime::currentDateTime();
            }
        }
    }
}

void MainWindow::updateMarketInfo()
{
    // 更新市场相关信息显示
    // 获取市场强度分析器和订单队列位置跟踪器
    MarketStrengthAnalyzer *marketAnalyzer = MarketStrengthAnalyzer::getInstance();
    OrderQueuePositionTracker *queueTracker = OrderQueuePositionTracker::getInstance();

    // 更新市场强度信息
    double buyStrength = marketAnalyzer->getBuyStrength();
    double sellStrength = marketAnalyzer->getSellStrength();
    double ratio = marketAnalyzer->getBuySellRatio();

    // 更新市场强度标签 - 只显示买卖强弱和比例以及看涨或看跌
    QString trendText = ratio > 1.0 ? "看涨" : (ratio < 1.0 ? "看跌" : "持平");
    QString strengthText = QString("买卖强弱: %1 %2").arg(ratio, 0, 'f', 2).arg(trendText);
    m_marketStrengthLabel->setText(strengthText);

    // 设置强度标签的颜色
    if (ratio > 1.3)
    {
        // 买单明显强于卖单，显示绿色
        m_marketStrengthLabel->setStyleSheet("color: green;");
    }
    else if (ratio < 0.7)
    {
        // 卖单明显强于买单，显示红色
        m_marketStrengthLabel->setStyleSheet("color: red;");
    }
    else
    {
        // 买卖相对均衡，显示黑色
        m_marketStrengthLabel->setStyleSheet("color: black;");
    }

    // 获取当前订单状态
    CurrentOrderStatus currentStatus = CurrentOrder::getInstance()->getStatus();
    // 获取队列跟踪器状态
    OrderQueuePositionTracker::OrderStatus trackerStatus = queueTracker->getOrderStatus();

    // 根据订单状态更新队列位置信息
    if (currentStatus == CurrentOrderStatus::BUY_PENDING)
    {
        // 买单挂单状态，显示买单队列位置
        double positionPercent = queueTracker->getCurrentPositionPercent() * 100;
        double filledAmount = queueTracker->getFilledAmount();
        double totalAmount = queueTracker->getInitialTotalAmount();
        double remainingAmount = totalAmount - filledAmount;

        // 只显示百分比和剩余金额，不显示总金额
        QString queueText = QString("买单队列: %1% (剩余: %2 USDT)").arg(positionPercent, 0, 'f', 2).arg(remainingAmount, 0, 'f', 4);

        m_queuePositionLabel->setText(queueText);
        m_queuePositionProgressBar->setValue(qRound(positionPercent));
        m_queuePositionProgressBar->setStyleSheet("QProgressBar::chunk { background-color: green; }");
    }
    else if (currentStatus == CurrentOrderStatus::ARBITRAGING)
    {
        // 套利中状态，显示卖单队列位置
        double positionPercent = queueTracker->getSellPositionPercent() * 100;
        double filledAmount = queueTracker->getSellFilledAmount();
        double totalAmount = queueTracker->getSellInitialTotalAmount();
        double remainingAmount = totalAmount - filledAmount;

        // 只显示百分比和剩余金额，不显示总金额
        QString queueText = QString("卖单队列: %1% (剩余: %2 USDT)").arg(positionPercent, 0, 'f', 2).arg(remainingAmount, 0, 'f', 2);

        m_queuePositionLabel->setText(queueText);
        m_queuePositionProgressBar->setValue(qRound(positionPercent));
        m_queuePositionProgressBar->setStyleSheet("QProgressBar::chunk { background-color: red; }");
    }
    else if (queueTracker->getOrderStatus() == OrderQueuePositionTracker::StopLossFirstDrop)
    {
        // 第一次止损状态，显示卖单队列位置
        double positionPercent = queueTracker->getSellPositionPercent() * 100;
        double filledAmount = queueTracker->getSellFilledAmount();
        double totalAmount = queueTracker->getSellInitialTotalAmount();
        double remainingAmount = totalAmount - filledAmount;

        // 只显示百分比和剩余金额，不显示总金额
        QString queueText = QString("止损队列: %1% (剩余: %2 USDT)").arg(positionPercent, 0, 'f', 2).arg(remainingAmount, 0, 'f', 2);

        m_queuePositionLabel->setText(queueText);
        m_queuePositionProgressBar->setValue(qRound(positionPercent));
        m_queuePositionProgressBar->setStyleSheet("QProgressBar::chunk { background-color: orange; }");
    }
    else
    {
        // 其他状态，清空队列位置显示
        m_queuePositionLabel->setText("等待数据...");
        m_queuePositionProgressBar->setValue(0);
        m_queuePositionProgressBar->setStyleSheet("");
    }
}

// 订阅特定价格的交易数据
bool MainWindow::subscribeToPriceTrade(const QString &symbol, double price)
{
    CurrentOrder *currentOrder = CurrentOrder::getInstance();
    if (!currentOrder)
    {
        qDebug() << "错误：CurrentOrder实例不存在";
        return false;
    }

    // 检查WebSocketClient是否设置
    if (!currentOrder->getWebSocketClient())
    {
        qDebug() << "错误：WebSocketClient未设置，尝试设置";

        // 尝试从ApiWorker获取WebSocketClient
        if (m_apiWorker)
        {
            WebSocketClient *webSocketClient = m_apiWorker->getWebSocketClient();
            if (webSocketClient)
            {
                currentOrder->setWebSocketClient(webSocketClient);
                qDebug() << "WebSocketClient已成功设置到CurrentOrder";
            }
            else
            {
                qDebug() << "错误：无法从ApiWorker获取WebSocketClient";
                return false;
            }
        }
        else
        {
            qDebug() << "错误：ApiWorker不可用，无法获取WebSocketClient";
            return false;
        }
    }

    qDebug() << "开始订阅交易对" << symbol << "的价格" << price << "的交易数据";
    bool result = currentOrder->subscribe(symbol, price);
    if (result)
    {
        qDebug() << "订阅成功";
    }
    else
    {
        qDebug() << "订阅失败，可能需要检查WebSocket连接";
    }

    return result;
}

// 取消订阅
bool MainWindow::unsubscribeFromPriceTrade()
{
    CurrentOrder *currentOrder = CurrentOrder::getInstance();
    if (!currentOrder)
    {
        qDebug() << "错误：CurrentOrder实例不存在";
        return false;
    }

    if (!currentOrder->isSubscribed())
    {
        qDebug() << "当前没有活跃的订阅，无需取消";
        return true;
    }

    QString symbol = currentOrder->getSubscribedSymbol();
    double price = currentOrder->getSubscribedPrice();
    qDebug() << "开始取消订阅交易对" << symbol << "的价格" << price << "的交易数据";

    bool result = currentOrder->unsubscribe();
    if (result)
    {
        qDebug() << "取消订阅成功";
    }
    else
    {
        qDebug() << "取消订阅失败，可能需要手动重置";
    }

    return result;
}

// 添加处理WebSocket交易数据的方法
void MainWindow::handleTradeDataReceived(const QJsonObject &tradeData)
{
    // 验证数据格式
    if (!tradeData.contains("symbol") || !tradeData.contains("price") || !tradeData.contains("isBuyerMaker"))
    {
        qDebug().noquote() << "收到的交易数据格式不完整:" << tradeData;
        return;
    }

    // 只处理当前交易对的数据
    QString symbol = tradeData["symbol"].toString();
    if (symbol != m_symbol)
    {
        return;
    }

    // 提取价格和买卖方信息
    m_latestPrice = tradeData["price"].toString();
    m_isBuyerMaker = tradeData["isBuyerMaker"].toBool();

    // 更新UI显示
    updatePriceInfoDisplay();

    // 获取订单跟踪器单例
    OrderQueuePositionTracker *queueTracker = OrderQueuePositionTracker::getInstance();

    // 获取订单跟踪器状态
    OrderQueuePositionTracker::OrderStatus trackerStatus = queueTracker->getOrderStatus();

    // 根据订单跟踪器的状态决定是否请求订单簿数据
    if (trackerStatus != OrderQueuePositionTracker::Initial)
    {
        // 获取当前的订单簿深度数据
        static QDateTime lastOrderBookRequestTime = QDateTime::currentDateTime().addSecs(-60);
        QDateTime currentTime = QDateTime::currentDateTime();
        int intervalSeconds = 15; // 默认15秒间隔

        // 根据不同的订单状态决定获取订单簿深度的频率
        if (trackerStatus == OrderQueuePositionTracker::BuyPending)
        {
            intervalSeconds = 10; // 买单挂着时，10秒间隔
        }
        else if (trackerStatus == OrderQueuePositionTracker::Arbitraging)
        {
            intervalSeconds = 5; // 套利中状态，5秒间隔
        }

        // 根据间隔时间决定是否请求新的订单簿数据
        if (lastOrderBookRequestTime.secsTo(currentTime) >= intervalSeconds)
        {
            // 请求新的订单簿数据
            emit requestOrderBookDepth(m_symbol, 20);
            lastOrderBookRequestTime = currentTime;
            // qDebug().noquote() << "请求订单簿深度数据（间隔" << intervalSeconds << "秒）";
        }
    }
}

// 在文件末尾添加checkApiWeightInfo方法的实现
void MainWindow::checkApiWeightInfo()
{
    if (!m_apiWorker)
    {
        qDebug() << "API工作线程未初始化，无法获取API权重信息";
        return;
    }

    try
    {
        // 获取详细API权重信息
        QJsonObject weightInfo = m_apiWorker->getDetailedApiWeightInfo();

        if (weightInfo.isEmpty())
        {
            qDebug() << "未获取到API权重信息";
            return;
        }

        // 输出所有时间窗口的权重使用百分比
        double weightPercentage1m = weightInfo["weightPercentage1m"].toDouble();
        double weightPercentage5m = weightInfo["weightPercentage5m"].toDouble();
        double weightPercentage1h = weightInfo["weightPercentage1h"].toDouble();
        double weightPercentage1d = weightInfo["weightPercentage1d"].toDouble();

        int currentWeight1m = weightInfo["currentWeight1m"].toInt();

        // 只有当1分钟权重超过阈值时才输出详细信息
        if (currentWeight1m >= m_apiWeightOutputThreshold)
        {
            qDebug() << "API权重使用情况:";
            qDebug() << "  1分钟: " << QString::number(weightPercentage1m, 'f', 2) << "% (" << currentWeight1m << "/"
                     << weightInfo["weightLimit1m"].toInt() << ")";

            // 只有在有5分钟权重数据时才输出
            if (weightInfo.contains("currentWeight5m") && weightInfo["currentWeight5m"].toInt() > 0)
            {
                qDebug() << "  5分钟: " << QString::number(weightPercentage5m, 'f', 2) << "% (" << weightInfo["currentWeight5m"].toInt() << "/"
                         << weightInfo["weightLimit5m"].toInt() << ")";
            }

            // 只有在有1小时权重数据时才输出
            if (weightInfo.contains("currentWeight1h") && weightInfo["currentWeight1h"].toInt() > 0)
            {
                qDebug() << "  1小时: " << QString::number(weightPercentage1h, 'f', 2) << "% (" << weightInfo["currentWeight1h"].toInt() << "/"
                         << weightInfo["weightLimit1h"].toInt() << ")";
            }

            // 只有在有1天权重数据时才输出
            if (weightInfo.contains("currentWeight1d") && weightInfo["currentWeight1d"].toInt() > 0)
            {
                qDebug() << "  1天: " << QString::number(weightPercentage1d, 'f', 2) << "% (" << weightInfo["currentWeight1d"].toInt() << "/"
                         << weightInfo["weightLimit1d"].toInt() << ")";
            }
        }

        // 如果任何一个窗口的权重使用超过80%，输出警告（不受阈值限制）
        if (weightPercentage1m > 80 || weightPercentage5m > 80 || weightPercentage1h > 80 || weightPercentage1d > 80)
        {
            qDebug() << "警告: API权重使用率较高，请注意避免触发限流!";
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "获取API权重信息时发生异常: " << e.what();
    }
    catch (...)
    {
        qDebug() << "获取API权重信息时发生未知异常";
    }
}

// 处理第一次价格下降
void MainWindow::handleFirstPriceDrop()
{
    qDebug().noquote() << "处理第一次价格下降，准备执行止损策略";

    // 获取当前订单对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 检查当前是否处于套利状态
    if (currentOrder->getStatus() != CurrentOrderStatus::ARBITRAGING)
    {
        qDebug().noquote() << "当前不处于套利状态，忽略价格下降事件";
        return;
    }

    // 记录当前止损价格差
    double currentAskPrice = m_askPrice.toDouble();
    double sellOrderPrice = currentOrder->getSellPrice();
    m_currentStopLossValue = sellOrderPrice - currentAskPrice;
    qDebug().noquote() << "记录第一次止损价格差: " << m_currentStopLossValue;

    // 设置第一次止损标记
    m_firstStopLoss = true;
    m_secondStopLoss = false;

    // 修改当前订单状态为止损第一次价格下降
    currentOrder->setOrderStatus(CurrentOrderStatus::STOP_LOSS_FIRST_DROP);

    // 取消当前卖出订单
    QString currentSellOrderId = currentOrder->getSellOrderId();
    if (!currentSellOrderId.isEmpty())
    {
        qDebug().noquote() << "正在取消当前卖出订单: " << currentSellOrderId;
        emit requestCancelOrder(m_symbol, currentSellOrderId);
    }

    // 不再直接调用executeFirstDropStopLoss()，等待订单取消成功后再执行
}

// 处理第二次价格下降
void MainWindow::handleSecondPriceDrop()
{
    qDebug().noquote() << "处理第二次价格下降，准备执行强制止损";

    // 获取当前订单对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 检查当前是否处于第一次止损状态
    if (currentOrder->getStatus() != CurrentOrderStatus::STOP_LOSS_FIRST_DROP)
    {
        qDebug().noquote() << "当前不处于第一次止损状态，忽略第二次价格下降事件";
        return;
    }

    // 设置第二次止损标记，同时清除第一次止损标记
    m_firstStopLoss = false;
    m_secondStopLoss = true;

    // 修改当前订单状态为止损第二次价格下降
    currentOrder->setOrderStatus(CurrentOrderStatus::STOP_LOSS_SECOND_DROP);

    // 停止止损计时器
    if (m_stopLossTimer && m_stopLossTimer->isActive())
    {
        m_stopLossTimer->stop();
        qDebug().noquote() << "止损计时器已停止";
    }

    // 取消当前卖出订单（如果有）
    QString currentSellOrderId = currentOrder->getSellOrderId();
    if (!currentSellOrderId.isEmpty())
    {
        qDebug().noquote() << "正在取消当前卖出订单: " << currentSellOrderId;
        emit requestCancelOrder(m_symbol, currentSellOrderId);
    }
    else
    {
        // 如果没有卖出订单，直接执行强制卖出
        executeSecondDropStopLoss();
    }
}

// 执行第一次价格下降的止损操作
void MainWindow::executeFirstDropStopLoss()
{
    qDebug().noquote() << "执行第一次价格下降的止损操作";

    // 获取当前订单对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 准备以当前卖一价下新的卖单
    QString quantity = currentOrder->getBuyQuantity();

    // 设置止损超时时间为4小时
    int stopLossTimeoutSeconds = 14400; // 4小时 = 14400秒

    // 开始止损计时器
    if (!m_stopLossTimer)
    {
        m_stopLossTimer = new QTimer(this);
        connect(m_stopLossTimer, &QTimer::timeout, this, &MainWindow::onStopLossTimeout);
    }

    if (m_stopLossTimer->isActive())
    {
        m_stopLossTimer->stop();
    }

    m_stopLossTimer->start(stopLossTimeoutSeconds * 1000); // 转换为毫秒
    qDebug().noquote() << "止损计时器已启动，" << stopLossTimeoutSeconds << "秒后将执行强制止损";

    // 以当前卖一价下新的卖单
    m_pendingOrderSymbol = m_symbol;
    m_pendingOrderSide = "SELL";
    m_pendingOrderType = "LIMIT";
    m_pendingOrderQuantity = quantity;
    m_pendingOrderPrice = m_askPrice;

    qDebug().noquote() << "正在以当前卖一价下新的卖单：价格=" << m_askPrice << "，数量=" << quantity;

    // 发送下单请求
    emit requestPlaceOrder(m_pendingOrderSymbol, m_pendingOrderSide, m_pendingOrderType, m_pendingOrderQuantity, m_pendingOrderPrice, QString());

    // 更新状态栏
    ui->statusbar->showMessage("检测到价格下降，正在尝试以当前卖一价止损", 5000);
}

// 执行第二次价格下降的止损操作（强制止损）
void MainWindow::executeSecondDropStopLoss()
{
    qDebug().noquote() << "执行第二次价格下降的强制止损操作";

    // 获取当前订单对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 准备以当前买一价下新的卖单（接受买一价以确保快速成交）
    QString quantity = currentOrder->getBuyQuantity();

    // 计算预计损失
    double buyPrice = currentOrder->getBuyPrice();
    double currentBidPrice = m_bidPrice.toDouble();
    double buyQuantity = quantity.toDouble();

    double investAmount = buyPrice * buyQuantity;
    double sellAmount = currentBidPrice * buyQuantity;
    double loss = sellAmount - investAmount;
    double lossPercent = (loss / investAmount) * 100.0;

    qDebug().noquote() << "预计损失: " << loss << " USDT (" << lossPercent << "%)";

    // 生成唯一的clientOrderId用于跟踪强制止损订单
    QString clientOrderId = "stopLoss_" + QUuid::createUuid().toString(QUuid::WithoutBraces);

    // 保存clientOrderId到CurrentOrder单例
    currentOrder->setPendingStopLossClientOrderId(clientOrderId);
    qDebug().noquote() << "生成强制止损订单clientOrderId: " << clientOrderId;

    // 以当前买一价下新的卖单
    m_pendingOrderSymbol = m_symbol;
    m_pendingOrderSide = "SELL";
    m_pendingOrderType = "LIMIT";
    m_pendingOrderQuantity = quantity;
    m_pendingOrderPrice = m_bidPrice;

    qDebug().noquote() << "正在以当前买一价强制止损：价格=" << m_bidPrice << "，数量=" << quantity << "，clientOrderId=" << clientOrderId;

    // 发送下单请求，添加clientOrderId
    // 注意：需要修改ApiWorker的placeOrder方法，添加对clientOrderId的支持
    emit requestPlaceOrder(m_pendingOrderSymbol, m_pendingOrderSide, m_pendingOrderType, m_pendingOrderQuantity, m_pendingOrderPrice, clientOrderId);

    // 更新状态栏
    QString message = QString("检测到连续价格下降，正在强制止损，预计损失: %1 USDT (%2%)").arg(loss, 0, 'f', 2).arg(lossPercent, 0, 'f', 2);
    ui->statusbar->showMessage(message, 10000);
}

// 止损超时处理
void MainWindow::onStopLossTimeout()
{
    qDebug().noquote() << "止损超时，准备执行强制止损";

    // 获取当前订单对象
    CurrentOrder *currentOrder = CurrentOrder::getInstance();

    // 检查当前是否处于第一次止损状态
    if (currentOrder->getStatus() != CurrentOrderStatus::STOP_LOSS_FIRST_DROP)
    {
        qDebug().noquote() << "当前不处于第一次止损状态，忽略止损超时事件";
        m_stopLossTimer->stop();
        return;
    }

    // 设置第二次止损标记，同时清除第一次止损标记
    m_firstStopLoss = false;
    m_secondStopLoss = true;

    // 修改当前订单状态为止损第二次价格下降
    currentOrder->setOrderStatus(CurrentOrderStatus::STOP_LOSS_SECOND_DROP);

    // 停止止损计时器
    m_stopLossTimer->stop();

    // 取消当前卖出订单（如果有）
    QString currentSellOrderId = currentOrder->getSellOrderId();
    if (!currentSellOrderId.isEmpty())
    {
        qDebug().noquote() << "正在取消当前卖出订单: " << currentSellOrderId;
        emit requestCancelOrder(m_symbol, currentSellOrderId);
    }
    else
    {
        // 如果没有卖出订单，直接执行强制卖出
        executeSecondDropStopLoss();
    }
}

// 重置止损状态
void MainWindow::resetStopLossState()
{
    // 停止止损计时器
    if (m_stopLossTimer && m_stopLossTimer->isActive())
    {
        m_stopLossTimer->stop();
        qDebug().noquote() << "止损计时器已停止";
    }

    // 重置止损标记
    m_firstStopLoss = false;
    m_secondStopLoss = false;

    // 重置止损价格差
    m_currentStopLossValue = 0.0;

    // 清空上一次的卖一价记录
    m_lastAskPrice.clear();
}

// 实现initUI方法
void MainWindow::initUI()
{
    qDebug().noquote() << "初始化UI...";

    // 初始化各个UI组件
    initMenu();
    initHistoryTable();

    // 设置初始状态
    setupInitialUI();

    // 初始化音频播放器
    initSoundPlayers();

    qDebug().noquote() << "UI初始化完成";
}

// 检查事件是否已处理过
bool MainWindow::isEventProcessed(const QString &eventKey)
{
    if (m_processedOrderEvents.contains(eventKey))
    {
        qDebug().noquote() << "事件已处理过，忽略重复事件：" << eventKey;
        return true;
    }
    m_processedOrderEvents.insert(eventKey);
    return false;
}

// 清理已处理事件集合
void MainWindow::cleanupProcessedEvents()
{
    if (m_processedOrderEvents.size() > 1000)
    {
        qDebug().noquote() << "清理已处理事件集合，当前大小：" << m_processedOrderEvents.size();
        m_processedOrderEvents.clear();
    }
}

// 订阅特定价格的交易数据
void MainWindow::subscribeToTrades(const QString &symbol, double price)
{
    qDebug().noquote() << "订阅" << symbol << "交易对的价格" << price << "的交易数据";

    // 使用现有的subscribeToPriceTrade方法
    bool result = subscribeToPriceTrade(symbol, price);

    if (result)
    {
        qDebug().noquote() << "成功订阅交易数据";
    }
    else
    {
        qDebug().noquote() << "订阅交易数据失败，可能影响队列跟踪功能";
    }
}
