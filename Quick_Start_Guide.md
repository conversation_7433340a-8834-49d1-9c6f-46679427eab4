# Qt项目快速启动指南

## 🚀 一键构建并运行

### 最简单的方式
- **按 Ctrl+R** → 自动构建并运行Debug版本 ⭐
- **按 Ctrl+B** → 自动构建并运行Debug版本 ⭐

### 选择版本构建并运行
1. **Ctrl+Shift+P** → 输入 "Tasks: Run Task"
2. 选择任务：
   - `Qt: Build & Run Debug` - 构建并运行Debug版本
   - `Qt: Build & Run Release` - 构建并运行Release版本

## 🏃‍♂️ 仅运行（不构建）

如果已经构建过，只想运行应用程序：

### 使用VSCode任务
1. **Ctrl+Shift+P** → 输入 "Tasks: Run Task"
2. 选择：
   - `Qt: Run Debug` - 运行Debug版本
   - `Qt: Run Release` - 运行Release版本

### 使用命令行
```bash
# 运行Debug版本
powershell -ExecutionPolicy Bypass -File run_only.ps1 debug

# 运行Release版本
powershell -ExecutionPolicy Bypass -File run_only.ps1 release
```

### 直接运行可执行文件
```bash
# Debug版本
"Binance\Debug\Binance.exe"

# Release版本
"Binance\Release\Binance.exe"
```

## 🧹 清理构建

### 清理所有构建文件
```bash
powershell -ExecutionPolicy Bypass -File qt_build_and_run.ps1 clean
```

### 使用VSCode任务
1. **Ctrl+Shift+P** → 输入 "Tasks: Run Task"
2. 选择：
   - `Qt: Clean All` - 清理所有构建文件
   - `Qt: Clean Debug` - 清理Debug构建文件
   - `Qt: Clean Release` - 清理Release构建文件

## 🔧 调试

- **按 F5** → 启动调试模式（Debug版本）

## 📋 完整工作流程

### 日常开发
1. **Ctrl+R** → 构建并运行Debug版本
2. 修改代码
3. **Ctrl+R** → 重新构建并运行
4. 如需调试：**F5** → 启动调试

### 发布测试
1. **Ctrl+Shift+P** → "Tasks: Run Task" → `Qt: Build & Run Release`
2. 测试Release版本功能

### 问题排查
1. 如果构建失败：**Ctrl+Shift+P** → "Tasks: Run Task" → `Qt: Clean All`
2. 重新构建：**Ctrl+R**

## 🎯 快捷键总结

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| **Ctrl+R** | 构建并运行Debug | ⭐ 日常开发首选 |
| **Ctrl+B** | 构建并运行Debug | ⭐ 日常开发首选 |
| **F5** | 启动调试 | 调试模式 |
| **Ctrl+Shift+P** | 命令面板 | 选择更多任务 |

## 📁 文件说明

- `qt_build_and_run.ps1` - 构建并运行脚本
- `run_only.ps1` - 仅运行脚本
- `switch_computer.bat` - 电脑配置切换
- `computer_config.json` - 配置文件

## ⚠️ 常见问题

### 问题1：应用程序没有启动
**解决方案**：
1. 检查构建是否成功
2. 使用 `Qt: Run Debug` 任务单独运行
3. 检查可执行文件是否存在

### 问题2：路径错误
**解决方案**：
1. 运行 `switch_computer.bat` 切换配置
2. 清理构建：`Qt: Clean All`
3. 重新构建：**Ctrl+R**

### 问题3：构建失败
**解决方案**：
1. 清理构建文件
2. 检查Qt安装路径
3. 重启VSCode

---

**现在您可以使用 Ctrl+R 一键构建并运行Qt应用程序了！** 🎉
