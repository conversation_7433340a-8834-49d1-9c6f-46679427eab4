#include "orderqueuepositiontracker.h"
#include <QDebug>

// 初始化静态单例实例指针
OrderQueuePositionTracker *OrderQueuePositionTracker::m_instance = nullptr;

OrderQueuePositionTracker::OrderQueuePositionTracker(QObject *parent)
    : QObject(parent), m_buyPrice(0.0), m_buyQuantity(0.0), m_buyAmount(0.0), m_initialTotalAmount(0.0), m_currentTotalAmount(0.0), m_filledAmount(0.0),
      m_positionPercent(0.0), m_isInitialized(false), m_sellPrice(0.0), m_sellQuantity(0.0), m_sellAmount(0.0), m_sellInitialTotalAmount(0.0),
      m_sellCurrentTotalAmount(0.0), m_sellFilledAmount(0.0), m_sellPositionPercent(0.0), m_sellIsInitialized(false), m_buyOrderId(""), m_sellOrderId(""),
      m_buyOrderTime(""), m_sellOrderTime(""), m_orderStatus(Initial)
{
    qDebug().noquote() << "OrderQueuePositionTracker构造函数被调用";
}

OrderQueuePositionTracker::~OrderQueuePositionTracker()
{
    qDebug().noquote() << "OrderQueuePositionTracker析构函数被调用";
}

OrderQueuePositionTracker *OrderQueuePositionTracker::getInstance()
{
    if (!m_instance) {
        m_instance = new OrderQueuePositionTracker();
    }
    return m_instance;
}

void OrderQueuePositionTracker::releaseInstance()
{
    if (m_instance) {
        delete m_instance;
        m_instance = nullptr;
    }
}

void OrderQueuePositionTracker::reset()
{
    qDebug().noquote() << "重置订单队列位置跟踪器";

    // 重置买单相关
    m_isInitialized = false;
    m_symbol = "";
    m_buyPrice = 0.0;
    m_buyQuantity = 0.0;
    m_initialTotalAmount = 0.0;
    m_currentTotalAmount = 0.0;
    m_filledAmount = 0.0;
    m_positionPercent = 0.0;

    // 重置卖单相关
    m_sellIsInitialized = false;
    m_sellPrice = 0.0;
    m_sellQuantity = 0.0;
    m_sellAmount = 0.0;
    m_sellInitialTotalAmount = 0.0;
    m_sellCurrentTotalAmount = 0.0;
    m_sellFilledAmount = 0.0;
    m_sellPositionPercent = 0.0;

    // 重置订单信息
    m_buyOrderId = "";
    m_sellOrderId = "";
    m_buyOrderTime = "";
    m_sellOrderTime = "";
    m_orderStatus = Initial;

    // 发送信号，表示位置已重置
    emit positionUpdated(0.0, 0.0);
    emit sellPositionUpdated(0.0, 0.0);
    emit orderStatusChanged(Initial);
}

double OrderQueuePositionTracker::getCurrentPositionPercent() const
{
    return m_positionPercent;
}

double OrderQueuePositionTracker::getFilledAmount() const
{
    return m_filledAmount;
}

double OrderQueuePositionTracker::getInitialTotalAmount() const
{
    return m_initialTotalAmount;
}

double OrderQueuePositionTracker::getCurrentTotalAmount() const
{
    return m_currentTotalAmount;
}

double OrderQueuePositionTracker::getMyOrderAmount() const
{
    return m_buyAmount;
}

OrderQueuePositionTracker::OrderStatus OrderQueuePositionTracker::getOrderStatus() const
{
    return m_orderStatus;
}

void OrderQueuePositionTracker::setOrderStatus(OrderStatus status)
{
    if (m_orderStatus != status) {
        m_orderStatus = status;
        qDebug().noquote() << "订单状态变更为: " << status;
        emit orderStatusChanged(status);
    }
}

void OrderQueuePositionTracker::setBuyOrderId(const QString &orderId)
{
    m_buyOrderId = orderId;
}

void OrderQueuePositionTracker::setSellOrderId(const QString &orderId)
{
    m_sellOrderId = orderId;
}

QString OrderQueuePositionTracker::getBuyOrderId() const
{
    return m_buyOrderId;
}

QString OrderQueuePositionTracker::getSellOrderId() const
{
    return m_sellOrderId;
}

void OrderQueuePositionTracker::setBuyOrderTime(const QString &time)
{
    m_buyOrderTime = time;
}

void OrderQueuePositionTracker::setSellOrderTime(const QString &time)
{
    m_sellOrderTime = time;
}

QString OrderQueuePositionTracker::getBuyOrderTime() const
{
    return m_buyOrderTime;
}

QString OrderQueuePositionTracker::getSellOrderTime() const
{
    return m_sellOrderTime;
}

void OrderQueuePositionTracker::setMyBuyOrder(const QString &symbol, double price, double quantity)
{
    qDebug().noquote() << "设置买单信息: 交易对=" << symbol << ", 价格=" << price << ", 数量=" << quantity;

    m_symbol = symbol;
    m_buyPrice = price;
    m_buyQuantity = quantity;
    m_buyAmount = price * quantity;

    // 重置跟踪状态
    m_initialTotalAmount = 0.0;
    m_currentTotalAmount = 0.0;
    m_filledAmount = 0.0;
    m_positionPercent = 0.0;
    m_isInitialized = false;

    // 更新订单状态为买入挂单
    setOrderStatus(BuyPending);

    qDebug().noquote() << "买单总金额: " << m_buyAmount << " USDT";
}

void OrderQueuePositionTracker::setMySellOrder(const QString &symbol, double price, double quantity)
{
    qDebug().noquote() << "设置卖单信息: 交易对=" << symbol << ", 价格=" << price << ", 数量=" << quantity;

    // 如果交易对不同，先更新交易对
    if (m_symbol != symbol) {
        m_symbol = symbol;
    }

    m_sellPrice = price;
    m_sellQuantity = quantity;
    m_sellAmount = price * quantity;

    // 重置卖单跟踪状态
    m_sellInitialTotalAmount = 0.0;
    m_sellCurrentTotalAmount = 0.0;
    m_sellFilledAmount = 0.0;
    m_sellPositionPercent = 0.0;
    m_sellIsInitialized = false;

    // 不再自动设置状态为套利中
    // setOrderStatus(Arbitraging);

    qDebug().noquote() << "卖单总金额: " << m_sellAmount << " USDT";
}

void OrderQueuePositionTracker::handleLatestTradeUpdated(const QJsonObject &tradeInfo)
{
    bool isBuyerMaker = tradeInfo["isBuyerMaker"].toBool();
    QString id = QString::number(tradeInfo["id"].toVariant().toLongLong());
    // qDebug() << "===========" << m_isInitialized << m_sellIsInitialized;
    // qDebug() << isBuyerMaker << m_buyOrderId << m_sellOrderId;

    if (isBuyerMaker) {
        if (m_buyOrderId.isEmpty()) {
            setBuyOrderId(id);
        }
    } else {
        if (m_sellOrderId.isEmpty()) {
            setSellOrderId(id);
        }
    }

    // 确保有买单或卖单信息
    bool hasBuyOrder = (m_buyPrice > 0.0 && m_buyQuantity > 0.0 && !m_buyOrderId.isEmpty());
    bool hasSellOrder = (m_sellPrice > 0.0 && m_sellQuantity > 0.0 && !m_sellOrderId.isEmpty());
    // qDebug() << hasBuyOrder << m_buyPrice << m_buyQuantity << m_buyOrderId;
    // qDebug() << hasSellOrder << m_sellPrice << m_sellQuantity << m_sellOrderId;

    if (!hasBuyOrder && !hasSellOrder) {
        return;
    }

    // 从交易信息中提取价格、数量和是否是买方挂单
    if (tradeInfo.contains("price") && tradeInfo.contains("qty") && tradeInfo.contains("isBuyerMaker")) {
        double tradePrice = formatPriceToFixed4Decimals(tradeInfo["price"].toString().toDouble());
        double tradeQty = tradeInfo["qty"].toString().toDouble();
        bool isBuyerMaker = tradeInfo["isBuyerMaker"].toBool();
        double tmpBuyPrice = formatPriceToFixed4Decimals(m_buyPrice);
        double tmpSellPrice = formatPriceToFixed4Decimals(m_sellPrice);
        // qDebug() << "当前价格:" << tradePrice << "，买入挂单价格" << tmpBuyPrice << "，卖出挂单价格" << tmpSellPrice;
        // 买入挂单状态，只处理买入价格的交易
        if (m_orderStatus == BuyPending && hasBuyOrder && m_isInitialized && (tradePrice == tmpBuyPrice)) {
            // qDebug() << "处理买入价格的交易：" << tradePrice << "," << tmpBuyPrice << ", isBuyerMaker=" << isBuyerMaker;
            processBuyPriceTradeExecution(tradePrice, tradeQty, isBuyerMaker);
        }

        // 套利中状态或第一次止损状态，处理卖出价格的交易
        if ((m_orderStatus == Arbitraging || m_orderStatus == StopLossFirstDrop) && hasSellOrder && m_sellIsInitialized && (tradePrice == tmpSellPrice)) {
            qDebug() << "处理卖出价格的交易：" << tradePrice << "," << tmpSellPrice << ", isBuyerMaker=" << isBuyerMaker;
            processSellPriceTradeExecution(tradePrice, tradeQty, isBuyerMaker);
        }
    }
}

void OrderQueuePositionTracker::processBuyPriceTradeExecution(double price, double quantity, bool isBuyerMaker)
{
    // 计算此次交易的金额
    double tradeAmount = price * quantity;

    if (isBuyerMaker) {
        // 累计已成交金额
        m_filledAmount += tradeAmount;
        // 注释掉减少当前队列总金额的代码，保持当前队列总金额不变
        // m_currentTotalAmount -= tradeAmount;

        // if (m_currentTotalAmount < 0) {
        //     m_currentTotalAmount = 0;
        // }

        // 计算当前位置百分比
        if (m_initialTotalAmount > 0) {
            m_positionPercent = m_filledAmount / m_initialTotalAmount;

            // 确保百分比在有效范围内
            if (m_positionPercent < 0.0)
                m_positionPercent = 0.0;
            if (m_positionPercent > 1.0)
                m_positionPercent = 1.0;

            // qDebug().noquote() << "买单队列位置更新: "
            //                    << "成交价=" << price << ", 成交量=" << quantity << ", 成交金额=" << tradeAmount << ", 位置百分比=" << (m_positionPercent *
            //                    100)
            //                    << "%"
            //                    << ", 累计成交金额=" << m_filledAmount << ", 当前队列总金额=" << m_currentTotalAmount;

            emit positionUpdated(m_positionPercent, m_filledAmount);

            // 买单成交后会继续处于买入挂单状态，除非明确设置为套利中状态
        }
    }
}

void OrderQueuePositionTracker::processSellPriceTradeExecution(double price, double quantity, bool isBuyerMaker)
{
    // 计算此次交易的金额
    double tradeAmount = price * quantity;

    if (!isBuyerMaker) {
        // 累计已成交金额
        m_sellFilledAmount += tradeAmount;
        // 注释掉减少当前队列总金额的代码，保持当前队列总金额不变
        // m_sellCurrentTotalAmount -= tradeAmount;

        // if (m_sellCurrentTotalAmount < 0) {
        //     m_sellCurrentTotalAmount = 0;
        // }

        // 计算当前位置百分比
        if (m_sellInitialTotalAmount > 0) {
            m_sellPositionPercent = m_sellFilledAmount / m_sellInitialTotalAmount;

            // 确保百分比在有效范围内
            if (m_sellPositionPercent < 0.0)
                m_sellPositionPercent = 0.0;
            if (m_sellPositionPercent > 1.0)
                m_sellPositionPercent = 1.0;

            // qDebug().noquote() << "卖单队列位置更新: "
            //                    << "成交价=" << price << ", 成交量=" << quantity << ", 成交金额=" << tradeAmount
            //                    << ", 位置百分比=" << (m_sellPositionPercent * 100) << "%"
            //                    << ", 累计成交金额=" << m_sellFilledAmount << ", 当前队列总金额=" << m_sellCurrentTotalAmount;

            emit sellPositionUpdated(m_sellPositionPercent, m_sellFilledAmount);

            // // 如果卖单位置已经到达我们的订单，套利完成，重置状态
            // if (m_sellPositionPercent >= 0.99) {
            //     qDebug().noquote() << "卖单已接近完全成交，套利完成，重置状态";
            //     reset();
            // }
        }
    }
}

void OrderQueuePositionTracker::handleOrderBookDepthUpdated(const QJsonObject &depthData)
{
    // 只有在需要初始化时才处理深度数据
    bool needBuyInit = (m_buyPrice > 0.0 && m_buyQuantity > 0.0 && !m_isInitialized);
    bool needSellInit = (m_sellPrice > 0.0 && m_sellQuantity > 0.0 && !m_sellIsInitialized);

    // 总是处理深度数据来刷新UI显示
    bool alwaysRefresh = true;

    if (!needBuyInit && !needSellInit && !alwaysRefresh) {
        return;
    }

    // 处理买单队列初始化或更新
    if ((needBuyInit || (m_isInitialized && alwaysRefresh)) && depthData.contains("bids") && depthData["bids"].isArray()) {
        QJsonArray bids = depthData["bids"].toArray();

        // 计算当前买一价的总量
        double bidVolume = extractBidVolumeAtPrice(bids, m_buyPrice);

        if (bidVolume > 0) {
            if (!m_isInitialized) {
                // 首次初始化
                m_initialTotalAmount = bidVolume;
                m_currentTotalAmount = bidVolume;
                m_filledAmount = 0.0;
                m_isInitialized = true;
                m_positionPercent = 0.0;

                qDebug().noquote() << "初始化买单队列: 初始队列总金额=" << m_initialTotalAmount << " USDT";
            } /*else {
                // 刷新现有值
                double previousTotalAmount = m_currentTotalAmount;
                m_currentTotalAmount = bidVolume;

                // 如果当前队列总金额小于上一次的总金额，表示有订单成交
                if (m_currentTotalAmount < previousTotalAmount) {
                    // 累计已成交金额（仅累加减少的部分）
                    double newlyFilledAmount = previousTotalAmount - m_currentTotalAmount;
                    m_filledAmount += newlyFilledAmount;

                    qDebug().noquote() << "检测到买单队列新成交: " << newlyFilledAmount << " USDT";
                }

                // 重新计算位置百分比
                if (m_initialTotalAmount > 0) {
                    m_positionPercent = m_filledAmount / m_initialTotalAmount;

                    // 确保百分比在有效范围内
                    if (m_positionPercent < 0.0)
                        m_positionPercent = 0.0;
                    if (m_positionPercent > 1.0)
                        m_positionPercent = 1.0;

                    qDebug().noquote() << "刷新买单队列位置: 位置百分比=" << (m_positionPercent * 100) << "%, 累计成交金额=" << m_filledAmount
                                       << ", 当前队列总金额=" << m_currentTotalAmount;
                }
            }*/

            // emit positionUpdated(m_positionPercent, m_filledAmount);
        }
    }

    // 处理卖单队列初始化或更新
    if ((needSellInit || (m_sellIsInitialized && alwaysRefresh)) && depthData.contains("asks") && depthData["asks"].isArray()) {
        QJsonArray asks = depthData["asks"].toArray();

        // 计算当前卖一价的总量
        double askVolume = extractAskVolumeAtPrice(asks, m_sellPrice);

        if (askVolume > 0) {
            if (!m_sellIsInitialized) {
                // 首次初始化
                m_sellInitialTotalAmount = askVolume;
                m_sellCurrentTotalAmount = askVolume;
                m_sellFilledAmount = 0.0;
                m_sellIsInitialized = true;
                m_sellPositionPercent = 0.0;

                qDebug().noquote() << "初始化卖单队列: 初始队列总金额=" << m_sellInitialTotalAmount << " USDT";
            }
        }
    }
}

double OrderQueuePositionTracker::extractBidVolumeAtPrice(const QJsonArray &bids, double price) const
{
    double totalVolume = 0.0;
    double targetPrice = formatPriceToFixed4Decimals(price);

    // qDebug().noquote() << "提取买单总量: 目标价格=" << targetPrice << ", 买单数量=" << bids.size();

    // 遍历所有买单层级，找到与目标价格匹配的层级
    for (const QJsonValue &bidValue : bids) {
        QJsonArray bid = bidValue.toArray();
        if (bid.size() >= 2) {
            double bidPrice = formatPriceToFixed4Decimals(bid[0].toString().toDouble());
            double bidQuantity = bid[1].toString().toDouble();

            // 严格匹配价格
            if (bidPrice == targetPrice) {
                // 计算金额 = 价格 * 数量
                totalVolume = bidPrice * bidQuantity;
                // qDebug().noquote() << "找到匹配的买单价格层级: 价格=" << bidPrice << ", 数量=" << bidQuantity << ", 金额=" << totalVolume;
                break;
            }
        }
    }

    // 确保总金额有效
    if (totalVolume <= 0) {
        qDebug().noquote() << "警告: 没有找到与价格" << targetPrice << "匹配的买单层级，返回0";
        return 0;
    }

    // qDebug().noquote() << "买单总量计算结果: " << totalVolume << " USDT";
    return totalVolume;
}

double OrderQueuePositionTracker::extractAskVolumeAtPrice(const QJsonArray &asks, double price) const
{
    double totalVolume = 0.0;
    double targetPrice = formatPriceToFixed4Decimals(price);

    // qDebug().noquote() << "提取卖单总量: 目标价格=" << targetPrice << ", 卖单数量=" << asks.size();

    // 遍历所有卖单层级，找到与目标价格匹配的层级
    for (const QJsonValue &askValue : asks) {
        QJsonArray ask = askValue.toArray();
        if (ask.size() >= 2) {
            double askPrice = formatPriceToFixed4Decimals(ask[0].toString().toDouble());
            double askQuantity = ask[1].toString().toDouble();

            // 严格匹配价格
            if (askPrice == targetPrice) {
                // 计算金额 = 价格 * 数量
                totalVolume = askPrice * askQuantity;
                // qDebug().noquote() << "找到匹配的卖单价格层级: 价格=" << askPrice << ", 数量=" << askQuantity << ", 金额=" << totalVolume;
                break;
            }
        }
    }

    // 确保总金额有效
    if (totalVolume <= 0) {
        qDebug().noquote() << "警告: 没有找到与价格" << targetPrice << "匹配的卖单层级，返回0";
        return 0;
    }

    // qDebug().noquote() << "卖单总量计算结果: " << totalVolume << " USDT";
    return totalVolume;
}

double OrderQueuePositionTracker::getSellPositionPercent() const
{
    return m_sellPositionPercent;
}

double OrderQueuePositionTracker::getSellFilledAmount() const
{
    return m_sellFilledAmount;
}

double OrderQueuePositionTracker::getSellInitialTotalAmount() const
{
    return m_sellInitialTotalAmount;
}

double OrderQueuePositionTracker::getSellCurrentTotalAmount() const
{
    return m_sellCurrentTotalAmount;
}

double OrderQueuePositionTracker::getMySellOrderAmount() const
{
    return m_sellAmount;
}
// 将价格格式化为4位小数字符串，然后再转回double
// 不进行四舍五入，直接截取
double OrderQueuePositionTracker::formatPriceToFixed4Decimals(double price) const
{
    // 先转为带足够位数的字符串（这里用10位确保精度）
    QString priceStr = QString::number(price, 'f', 10);

    // 找到小数点位置
    int dotPos = priceStr.indexOf('.');
    if (dotPos == -1) {
        // 没有小数点，直接返回整数
        return price;
    }

    // 截取到小数点后4位（如果有那么多位的话）
    int endPos = dotPos + 5; // 小数点+4位小数
    if (endPos > priceStr.length()) {
        // 如果小数位不足4位，使用原字符串
        endPos = priceStr.length();
    }
    priceStr = priceStr.left(endPos);

    // 转回double
    return priceStr.toDouble();
}
