#ifndef DATABASEMANAGER_H
#define DATABASEMANAGER_H

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QDateTime>
#include <QVariantMap>
#include <QList>
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QFile>
#include <QTextStream>

// 查询筛选条件结构体
struct QueryFilter
{
    bool useStartTime = false;
    QDateTime startTimeFrom;
    QDateTime startTimeTo;

    bool useEndTime = false;
    QDateTime endTimeFrom;
    QDateTime endTimeTo;

    bool useOrderId = false;
    QString orderId;

    bool useStatus = false;
    QString status;

    bool useAmount = false;
    double minAmount = 0.0;
    double maxAmount = 0.0;
};

class DatabaseManager : public QObject
{
    Q_OBJECT

public:
    explicit DatabaseManager(QObject *parent = nullptr);
    ~DatabaseManager();

    // 数据库初始化
    bool initDatabase();

    // 插入历史记录
    bool insertHistoryRecord(const QString &orderId,
                             const QDateTime &startTime,
                             const QDateTime &arbitrageStartTime,
                             const QDateTime &arbitrageEndTime,
                             double buyPrice, double sellPrice,
                             double amount, double profit,
                             const QString &status);

    // 查询历史记录（带筛选条件）
    QList<QVariantMap> queryHistoryRecords(const QueryFilter &filter);

    // 获取所有记录数量
    int getTotalRecordsCount();

    // 清空所有记录
    bool clearAllRecords();

    // 检查数据库连接状态
    bool isConnected() const;

private:
    // JSON备用方案方法
    bool initJsonDatabase();
    bool insertHistoryRecordJson(const QString &orderId,
                                 const QDateTime &startTime,
                                 const QDateTime &arbitrageStartTime,
                                 const QDateTime &arbitrageEndTime,
                                 double buyPrice, double sellPrice,
                                 double amount, double profit,
                                 const QString &status);
    QList<QVariantMap> queryHistoryRecordsJson(const QueryFilter &filter);
    bool loadJsonData();
    bool saveJsonData();
    bool matchesFilter(const QVariantMap &record, const QueryFilter &filter);

private:
    // 创建数据库表
    bool createTables();

    // 构建查询SQL语句
    QString buildQuerySql(const QueryFilter &filter, QVariantMap &bindValues);

private:
    QSqlDatabase m_database;
    QString m_databasePath;
    static const QString DATABASE_NAME;
    static const QString CONNECTION_NAME;

    // JSON备用方案成员变量
    bool m_useSqlite;
    QString m_jsonFilePath;
    QJsonArray m_jsonData;
};

#endif // DATABASEMANAGER_H
