######################################################################
# Automatically generated by qmake (3.1) Sun May 25 23:30:36 2025
######################################################################

TEMPLATE = app
TARGET = Binance
INCLUDEPATH += .

# You can make your code fail to compile if you use deprecated APIs.
# In order to do so, uncomment the following line.
# Please consult the documentation of the deprecated API in order to know
# how to port your code away from it.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

# Input
HEADERS += accountinfo.h \
           apimanager.h \
           apiworker.h \
           binanceuserstream.h \
           currentorder.h \
           loginwindow.h \
           mainwindow.h \
           marketstrengthanalyzer.h \
           orderqueuepositiontracker.h \
           utils.h \
           websocketclient.h \
           build/Binance_autogen/include_Debug/ui_loginwindow.h \
           build/Binance_autogen/include_Debug/ui_mainwindow.h \
           build/Binance_autogen/include_Release/ui_loginwindow.h \
           build/Binance_autogen/include_Release/ui_mainwindow.h \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/include/ui_loginwindow.h \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/include/ui_mainwindow.h \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/CMakeFiles/ShowIncludes/foo.h \
           /build/Binance_autogen/include_Debug/ui_loginwindow.h \
           /build/Binance_autogen/include_Debug/ui_mainwindow.h \
           /build/Binance_autogen/include_Debug/EWIEGA46WW/moc_accountinfo.cpp \
           /build/Binance_autogen/include_Debug/EWIEGA46WW/moc_apimanager.cpp \
           /build/Binance_autogen/include_Debug/EWIEGA46WW/moc_apiworker.cpp \
           /build/Binance_autogen/include_Debug/EWIEGA46WW/moc_currentorder.cpp \
           /build/Binance_autogen/include_Debug/EWIEGA46WW/moc_loginwindow.cpp \
           /build/Binance_autogen/include_Debug/EWIEGA46WW/moc_mainwindow.cpp \
           /build/Binance_autogen/include_Release/EWIEGA46WW/moc_marketstrengthanalyzer.cpp \
           /build/Binance_autogen/include_Release/EWIEGA46WW/moc_orderqueuepositiontracker.cpp \
           /build/Binance_autogen/include_Debug/EWIEGA46WW/moc_websocketclient.cpp \
           /build/Binance_autogen/include_Debug/EWIEGA46WW/moc_order.cpp \
           /build/Binance_autogen/include_Debug/EWIEGA46WW/moc_ordermanager.cpp \
           /build/Binance_autogen/include_Debug/EWIEGA46WW/moc_timeoutchecker.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/EWIEGA46WW/moc_accountinfo.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/EWIEGA46WW/moc_apimanager.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/EWIEGA46WW/moc_apiworker.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/EWIEGA46WW/moc_binanceuserstream.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/EWIEGA46WW/moc_currentorder.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/EWIEGA46WW/moc_loginwindow.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/EWIEGA46WW/moc_mainwindow.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/EWIEGA46WW/moc_marketstrengthanalyzer.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/EWIEGA46WW/moc_orderqueuepositiontracker.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/EWIEGA46WW/moc_websocketclient.cpp
FORMS += loginwindow.ui mainwindow.ui
SOURCES += accountinfo.cpp \
           apimanager.cpp \
           apiworker.cpp \
           binanceuserstream.cpp \
           currentorder.cpp \
           loginwindow.cpp \
           main.cpp \
           mainwindow.cpp \
           marketstrengthanalyzer.cpp \
           orderqueuepositiontracker.cpp \
           utils.cpp \
           websocketclient.cpp \
           Binance_autogen/mocs_compilation_Debug.cpp \
           build/Binance_autogen/mocs_compilation_Debug.cpp \
           build/Binance_autogen/mocs_compilation_Release.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/mocs_compilation.cpp \
           CMakeFiles/3.30.5/CompilerIdCXX/CMakeCXXCompilerId.cpp \
           build/CMakeFiles/3.31.0-rc3/CompilerIdCXX/CMakeCXXCompilerId.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/Binance_autogen/EWIEGA46WW/qrc_res.cpp \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/CMakeFiles/ShowIncludes/main.c \
           build/Desktop_Qt_6_5_3_MSVC2019_64bit-Release/CMakeFiles/3.31.0-rc3/CompilerIdCXX/CMakeCXXCompilerId.cpp
RESOURCES += res.qrc
