# 挂单队列实时订阅设计方案

## 1. 现有队列更新流程梳理

### 1.1 入口函数
当前挂单队列百分比的更新入口函数为：
- `MainWindow::handleLatestPriceUpdated(const QJsonObject &priceInfo)`
  - 该函数由`ApiWorker`的`latestPriceUpdated`信号触发。
  - 内部会调用`OrderQueuePositionTracker::handleLatestTradeUpdated(priceInfo)`，用于处理最新成交的trade数据。
  - 根据订单跟踪器状态，定时请求订单簿深度数据（通过HTTP REST API），并调用`OrderQueuePositionTracker::handleOrderBookDepthUpdated`进行队列初始化和刷新。

### 1.2 现有流程总结
- 目前是通过定时HTTP拉取订单簿深度数据+每笔trade推送（WebSocket）来更新队列进度。
- 订单簿深度数据用于初始化和校准，trade数据用于实时推进队列进度。
- 订单簿深度的请求是定时的，不是实时的。
- 目前没有对某个价格的订单簿变动做专门的实时订阅。

## 2. 新的实时订阅设计目标
- 下单时，自动订阅挂单价格的订单簿变动（如@depth、@trade等WebSocket频道），实现对该价格下所有成交的实时跟踪。
- 停止套利或套利完成时，自动取消该价格的订单簿订阅，防止订阅过多。
- 通过订阅，能够第一时间获取到挂单价格的每一笔成交，极大提升队列进度判断的准确性。

## 3. 设计思路

### 3.1 订阅时机
- **买单下单成功后**，即设置`OrderQueuePositionTracker::setMyBuyOrder`时，自动发起对该价格的订单簿变动/成交明细的WebSocket订阅。
- **卖单下单成功后**，即设置`OrderQueuePositionTracker::setMySellOrder`时，自动发起对该价格的订单簿变动/成交明细的WebSocket订阅。

### 3.2 取消订阅时机
- **套利完成**（卖单成交）、**手动结束套利**、**撤单**、**恢复初始状态**时，自动取消之前的价格订阅。
- 只要订单对象回到初始状态，必须确保所有相关的订单簿订阅都被取消。

### 3.3 订阅与取消的实现方式
- 由`ApiWorker`或`WebSocketClient`提供`subscribeToPriceTrades(symbol, price)`和`unsubscribeFromPriceTrades(symbol, price)`接口。
- 订阅时，拼接币安WebSocket的`<symbol>@trade`或`<symbol>@depth`频道，收到后在本地过滤出目标价格的成交。
- 也可以考虑用`<symbol>@aggTrade`频道，减少带宽。
- 订阅和取消的管理由`OrderQueuePositionTracker`或`MainWindow`统一调度，避免重复订阅。
- 订阅的价格和symbol要与当前挂单对象保持一致。

### 3.4 订阅返回信息的处理
- WebSocket收到trade/depth/aggTrade消息后，统一由`ApiWorker`发信号（如`priceTradeUpdated(symbol, price, quantity, ... )`）。
- `OrderQueuePositionTracker`监听这些信号，只处理与当前挂单价格完全一致的成交。
- 这样可以做到每一笔成交都能实时推进队列进度。

### 3.5 订阅管理细节
- 需要记录当前已订阅的symbol+price，防止重复订阅。
- 取消订阅时要确保彻底移除监听，防止内存泄漏。
- 若同一时刻只允许一个挂单对象，订阅管理逻辑会比较简单。

## 4. 设计流程图
