^D:\STUDYANDWORK\QTPROJECTS\BINANCE\CMAKEFILES\BE6C9C83973461D6B5AA38B0DD99CCE1\AUTOUIC_(CONFIG).STAMP.RULE
setlocal
cd D:\StudyAndWork\QtProjects\Binance
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\STUDYANDWORK\QTPROJECTS\BINANCE\CMAKEFILES\F5657C271CFDD39D1B823746F427FCE9\QRC_RES.CPP.RULE
setlocal
cd D:\StudyAndWork\QtProjects\Binance
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
D:\StudyAndWork\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autorcc D:/StudyAndWork/QtProjects/Binance/CMakeFiles/Binance_autogen.dir/AutoRcc_res_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\STUDYANDWORK\QTPROJECTS\BINANCE\CMAKELISTS.TXT
setlocal
D:\StudyAndWork\Qt\Tools\CMake_64\bin\cmake.exe -SD:/StudyAndWork/QtProjects/Binance -BD:/StudyAndWork/QtProjects/Binance --check-stamp-file D:/StudyAndWork/QtProjects/Binance/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
