#include "accountinfo.h"
#include <QDebug>

AccountInfo::AccountInfo(QObject *parent) : QObject(parent)
{
    // 初始化账户信息
    m_usdtBalance = "0";
    m_usdcBalance = "0";
    m_usdtFreeBalance = "0";
    m_usdcFreeBalance = "0";
    m_usdtLockedBalance = "0";
    m_usdcLockedBalance = "0";
}

void AccountInfo::updateAccountInfo(const QJsonObject &accountInfo)
{
    // 更新账户信息
    m_accountInfo = accountInfo;

    // 清空资产信息映射表
    m_assets.clear();

    // 解析资产信息
    if (m_accountInfo.contains("balances") && m_accountInfo["balances"].isArray()) {
        QJsonArray balances = m_accountInfo["balances"].toArray();

        for (const QJsonValue &value : balances) {
            if (value.isObject()) {
                QJsonObject assetObj = value.toObject();

                AssetInfo assetInfo;
                assetInfo.asset = assetObj["asset"].toString();
                assetInfo.free = assetObj["free"].toString();
                assetInfo.locked = assetObj["locked"].toString();

                // 只处理USDT和USDC资产
                if (assetInfo.asset == "USDT" || assetInfo.asset == "USDC") {
                    m_assets[assetInfo.asset] = assetInfo;

                    // 更新USDT和USDC的余额信息
                    if (assetInfo.asset == "USDT") {
                        m_usdtBalance = assetInfo.total();
                        m_usdtFreeBalance = assetInfo.free;
                        m_usdtLockedBalance = assetInfo.locked;
                    } else if (assetInfo.asset == "USDC") {
                        m_usdcBalance = assetInfo.total();
                        m_usdcFreeBalance = assetInfo.free;
                        m_usdcLockedBalance = assetInfo.locked;
                    }
                }
            }
        }
    }

    // 发送账户信息更新信号
    emit accountInfoUpdated();

    // 发送USDT余额更新信号
    emit usdtBalanceUpdated(getUSDTBalance());

    // 发送USDC余额更新信号
    emit usdcBalanceUpdated(getUSDCBalance());

    // 发送USDT可用余额更新信号
    emit usdtFreeBalanceUpdated(getUSDTFreeBalance());

    // 发送USDC可用余额更新信号
    emit usdcFreeBalanceUpdated(getUSDCFreeBalance());

    // 发送USDT锁定余额更新信号
    emit usdtLockedBalanceUpdated(getUSDTLockedBalance());

    // 发送USDC锁定余额更新信号
    emit usdcLockedBalanceUpdated(getUSDCLockedBalance());
}

void AccountInfo::updateAccountInfoForWebsocket(double usdc, double usdc_locked, double usdt, double usdt_locked)
{
    m_usdtBalance = QString::number(usdt + usdt_locked, 'f', 4);
    m_usdtFreeBalance = QString::number(usdt, 'f', 4);
    m_usdtLockedBalance = QString::number(usdt_locked, 'f', 4);

    m_usdcBalance = QString::number(usdc + usdc_locked, 'f', 4);
    m_usdcFreeBalance = QString::number(usdc, 'f', 4);
    m_usdcLockedBalance = QString::number(usdc_locked, 'f', 4);

    // 发送账户信息更新信号
    emit accountInfoUpdated();

    // 发送USDT余额更新信号
    emit usdtBalanceUpdated(getUSDTBalance());

    // 发送USDC余额更新信号
    emit usdcBalanceUpdated(getUSDCBalance());

    // 发送USDT可用余额更新信号
    emit usdtFreeBalanceUpdated(getUSDTFreeBalance());

    // 发送USDC可用余额更新信号
    emit usdcFreeBalanceUpdated(getUSDCFreeBalance());

    // 发送USDT锁定余额更新信号
    emit usdtLockedBalanceUpdated(getUSDTLockedBalance());

    // 发送USDC锁定余额更新信号
    emit usdcLockedBalanceUpdated(getUSDCLockedBalance());
}

AssetInfo AccountInfo::getAssetInfo(const QString &asset) const
{
    // 获取资产信息
    if (m_assets.contains(asset)) {
        return m_assets[asset];
    }

    // 如果资产不存在，返回空资产信息
    AssetInfo emptyAssetInfo;
    emptyAssetInfo.asset = asset;
    emptyAssetInfo.free = "0";
    emptyAssetInfo.locked = "0";

    return emptyAssetInfo;
}

QMap<QString, AssetInfo> AccountInfo::getAllAssets() const
{
    // 获取所有资产信息
    return m_assets;
}

QString AccountInfo::getUSDTBalance() const
{
    // 获取USDT总余额
    return m_usdtBalance;
}

QString AccountInfo::getUSDCBalance() const
{
    // 获取USDC总余额
    return m_usdcBalance;
}

QString AccountInfo::getUSDTFreeBalance() const
{
    // 获取USDT可用余额
    return m_usdtFreeBalance;
}

QString AccountInfo::getUSDCFreeBalance() const
{
    // 获取USDC可用余额
    return m_usdcFreeBalance;
}

QString AccountInfo::getUSDTLockedBalance() const
{
    // 获取USDT锁定余额
    return m_usdtLockedBalance;
}

QString AccountInfo::getUSDCLockedBalance() const
{
    // 获取USDC锁定余额
    return m_usdcLockedBalance;
}
