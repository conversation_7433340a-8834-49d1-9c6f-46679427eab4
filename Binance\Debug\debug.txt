2025年06月22日22:20:45       SSL支持可用，OpenSSL版本： "OpenSSL 3.4.0 22 Oct 2024"
2025年06月22日22:20:45       注册线程，当前线程数: 1
2025年06月22日22:20:45       ApiWorker已创建，ApiManager将在工作线程中创建
2025年06月22日22:20:45       正在工作线程 QThread(0x1bc86ac3350) 中初始化ApiManager
2025年06月22日22:20:45       WebSocket自动重连已 启用
2025年06月22日22:20:45       WebSocket心跳间隔设置为 20000 毫秒
2025年06月22日22:20:45       WebSocket重连间隔设置为 5000 毫秒
2025年06月22日22:20:45       WebSocket最大重连尝试次数设置为 10
2025年06月22日22:20:45       ApiManager已在工作线程 QThread(0x1bc86ac3350) 中初始化完成
2025年06月22日22:20:47       完整请求URL: "https://api.binance.com/api/v3/account?timestamp=*************&signature=1aedcf61afcd8e5b66535464493afceeccf03685b288c89307b78916fe49dec7"
2025年06月22日22:20:47       验证API密钥请求网络错误: QNetworkReply::UnknownContentError - "Error transferring https://api.binance.com/api/v3/account?timestamp=*************&signature=1aedcf61afcd8e5b66535464493afceeccf03685b288c89307b78916fe49dec7 - server replied: "
2025年06月22日22:20:47       错误响应内容: "{\n  \"code\": 0,\n  \"msg\": \"Service unavailable from a restricted location according to 'b. Eligibility' in https://www.binance.com/en/terms. Please contact customer service if you believe you received this message in error.\"\n}"
2025年06月22日22:20:47       验证API密钥失败: "Error transferring https://api.binance.com/api/v3/account?timestamp=*************&signature=1aedcf61afcd8e5b66535464493afceeccf03685b288c89307b78916fe49dec7 - server replied: "
2025年06月22日22:20:47       HTTP状态码: 451
2025年06月22日22:20:47       网络请求错误:  "Error transferring https://api.binance.com/api/v3/account?timestamp=*************&signature=1aedcf61afcd8e5b66535464493afceeccf03685b288c89307b78916fe49dec7 - server replied: "
2025年06月22日22:20:48       收到Close事件，对象类型: "QWidgetWindow"
2025年06月22日22:20:48       收到Close事件，对象类型: "QMessageBox"
2025年06月22日22:20:48       忽略消息框或对话框的关闭事件
2025年06月22日22:20:49       收到Close事件，对象类型: "QWidgetWindow"
2025年06月22日22:20:49       收到Close事件，对象类型: "LoginWindow"
2025年06月22日22:20:49       应用程序主窗口即将关闭，执行清理操作...
2025年06月22日22:20:49       应用程序正常关闭
2025年06月22日22:20:49       ApiManager对象已安排删除
2025年06月22日22:20:49       ApiWorker析构函数执行完毕
2025年06月22日22:20:49       应用程序主窗口即将关闭，执行清理操作...
2025年06月22日22:20:49       应用程序正常关闭
2025年06月22日22:20:49       应用程序即将退出，执行最终清理...
2025年06月22日22:20:49       应用程序即将退出，执行清理...
2025年06月22日22:20:49       正在取消所有进行中的网络请求...
2025年06月22日22:20:49       所有网络请求已取消
2025年06月22日22:20:49       强制关闭所有网络连接...
2025年06月22日22:20:49       所有网络连接已安排关闭
2025年06月22日22:20:49       等待网络连接关闭完成...
2025年06月22日22:20:49       强制关闭所有网络连接...
2025年06月22日22:20:49       所有网络连接已安排关闭
2025年06月22日22:20:49       强制终止所有线程...
