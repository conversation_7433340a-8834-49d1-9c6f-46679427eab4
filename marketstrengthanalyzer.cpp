#include "marketstrengthanalyzer.h"
#include <QDebug>

// 初始化静态单例实例指针
MarketStrengthAnalyzer *MarketStrengthAnalyzer::m_instance = nullptr;

MarketStrengthAnalyzer::MarketStrengthAnalyzer(QObject *parent) : QObject(parent), m_currentTrend(0), m_previousTrend(0)
{
    qDebug().noquote() << "MarketStrengthAnalyzer构造函数被调用";

    // 初始化当前市场强度数据
    m_currentStrengthData.bidAskRatio = 1.0;
    m_currentStrengthData.bidVolume = 0.0;
    m_currentStrengthData.askVolume = 0.0;
    m_currentStrengthData.bidPrice = 0.0;
    m_currentStrengthData.askPrice = 0.0;
    m_currentStrengthData.timestamp = QDateTime::currentDateTime();
}

MarketStrengthAnalyzer::~MarketStrengthAnalyzer()
{
    qDebug().noquote() << "MarketStrengthAnalyzer析构函数被调用";
    m_historyStrengthData.clear();
}

MarketStrengthAnalyzer *MarketStrengthAnalyzer::getInstance()
{
    if (!m_instance) {
        m_instance = new MarketStrengthAnalyzer();
    }
    return m_instance;
}

void MarketStrengthAnalyzer::releaseInstance()
{
    if (m_instance) {
        delete m_instance;
        m_instance = nullptr;
    }
}

MarketStrengthData MarketStrengthAnalyzer::getCurrentStrengthData() const
{
    return m_currentStrengthData;
}

QList<MarketStrengthData> MarketStrengthAnalyzer::getHistoryStrengthData(int count) const
{
    // 返回最近的count条历史数据
    if (count <= 0 || count > m_historyStrengthData.size()) {
        return m_historyStrengthData;
    }
    return m_historyStrengthData.mid(0, count);
}

int MarketStrengthAnalyzer::getCurrentTrend() const
{
    return m_currentTrend;
}

QString MarketStrengthAnalyzer::getStrengthDescription() const
{
    // 获取当前强度描述
    return m_currentStrengthData.strengthLevel();
}

QString MarketStrengthAnalyzer::getPriceTrendPrediction() const
{
    // 基于当前强度和历史数据给出简单的价格趋势预测
    QString basePrediction = m_currentTrend > 0 ? "短期内价格可能上涨" : (m_currentTrend < 0 ? "短期内价格可能下跌" : "短期内价格可能维持横盘");

    // 如果历史数据足够，分析趋势稳定性
    if (m_historyStrengthData.size() >= 5) {
        int stableCount = 0;
        bool isStable = true;
        int direction = m_historyStrengthData[0].isBullish() ? 1 : -1;

        // 检查最近5条记录趋势是否一致
        for (int i = 0; i < 5 && i < m_historyStrengthData.size(); i++) {
            int currentDirection = m_historyStrengthData[i].isBullish() ? 1 : -1;
            if (currentDirection == direction) {
                stableCount++;
            }
        }

        // 根据稳定性添加描述
        if (stableCount >= 4) {
            basePrediction += "，趋势较为稳定";
        } else if (stableCount <= 2) {
            basePrediction += "，但趋势不稳定，可能出现反转";
        }
    }

    return basePrediction;
}

void MarketStrengthAnalyzer::handleOrderBookDepthUpdated(const QJsonObject &depthData)
{
    // 检查数据有效性
    if (!depthData.contains("bids") || !depthData.contains("asks") || !depthData["bids"].isArray() || !depthData["asks"].isArray()) {
        qDebug().noquote() << "订单簿深度数据无效";
        return;
    }

    QJsonArray bids = depthData["bids"].toArray();
    QJsonArray asks = depthData["asks"].toArray();

    // 如果买单或卖单数据为空，返回
    if (bids.isEmpty() || asks.isEmpty()) {
        qDebug().noquote() << "买单或卖单数据为空";
        return;
    }

    // 保存上一次市场强度数据到历史记录
    m_historyStrengthData.prepend(m_currentStrengthData);

    // 限制历史数据大小
    while (m_historyStrengthData.size() > MAX_HISTORY_SIZE) {
        m_historyStrengthData.removeLast();
    }

    // 获取买一价和卖一价的总额
    double bidPrice = 0.0;
    double askPrice = 0.0;
    double bidVolume = calculateBidVolume(bids, bidPrice);
    double askVolume = calculateAskVolume(asks, askPrice);

    // 更新当前市场强度数据
    m_currentStrengthData.bidVolume = bidVolume;
    m_currentStrengthData.askVolume = askVolume;
    m_currentStrengthData.bidPrice = bidPrice;
    m_currentStrengthData.askPrice = askPrice;
    m_currentStrengthData.timestamp = QDateTime::currentDateTime();

    // 计算买卖比率
    if (askVolume > 0) {
        m_currentStrengthData.bidAskRatio = bidVolume / askVolume;
    } else {
        m_currentStrengthData.bidAskRatio = bidVolume > 0 ? 999.0 : 1.0;
    }

    // 保存之前的趋势
    m_previousTrend = m_currentTrend;

    // 更新当前趋势
    if (m_currentStrengthData.bidAskRatio > 1.05) {
        m_currentTrend = 1; // 看涨
    } else if (m_currentStrengthData.bidAskRatio < 0.95) {
        m_currentTrend = -1; // 看跌
    } else {
        m_currentTrend = 0; // 中性
    }

    // 输出调试信息
    // qDebug().noquote() << "市场强度更新: 买一总额=" << bidVolume
    //                   << ", 卖一总额=" << askVolume
    //                   << ", 买卖比率=" << m_currentStrengthData.bidAskRatio
    //                   << ", 强度级别=" << m_currentStrengthData.strengthLevel();

    // 发送市场强度更新信号
    emit strengthUpdated(m_currentStrengthData);

    // 如果趋势发生变化，发送趋势变化信号
    if (m_currentTrend != m_previousTrend && (m_currentTrend != 0 || m_previousTrend != 0)) {
        // qDebug().noquote() << "市场趋势变化: 从" <<
        //                   (m_previousTrend > 0 ? "看涨" : (m_previousTrend < 0 ? "看跌" : "中性")) <<
        //                   "变为" <<
        //                   (m_currentTrend > 0 ? "看涨" : (m_currentTrend < 0 ? "看跌" : "中性"));

        emit trendChanged(m_previousTrend, m_currentTrend);
    }
}

void MarketStrengthAnalyzer::reset()
{
    // 清空历史数据
    m_historyStrengthData.clear();

    // 重置当前市场强度数据
    m_currentStrengthData.bidAskRatio = 1.0;
    m_currentStrengthData.bidVolume = 0.0;
    m_currentStrengthData.askVolume = 0.0;
    m_currentStrengthData.bidPrice = 0.0;
    m_currentStrengthData.askPrice = 0.0;
    m_currentStrengthData.timestamp = QDateTime::currentDateTime();

    // 重置趋势
    m_currentTrend = 0;
    m_previousTrend = 0;
}

void MarketStrengthAnalyzer::analyzeHistoryData()
{
    // 这个函数可以根据历史数据进行更复杂的分析
    // 当前版本暂时不实现复杂分析
}

double MarketStrengthAnalyzer::calculateBidVolume(const QJsonArray &bids, double &price) const
{
    // 获取买一价的价格和总额
    if (bids.isEmpty() || !bids[0].isArray() || bids[0].toArray().size() < 2) {
        return 0.0;
    }

    QJsonArray bidData = bids[0].toArray();
    price = bidData[0].toString().toDouble();
    double quantity = bidData[1].toString().toDouble();

    return price * quantity;
}

double MarketStrengthAnalyzer::calculateAskVolume(const QJsonArray &asks, double &price) const
{
    // 获取卖一价的价格和总额
    if (asks.isEmpty() || !asks[0].isArray() || asks[0].toArray().size() < 2) {
        return 0.0;
    }

    QJsonArray askData = asks[0].toArray();
    price = askData[0].toString().toDouble();
    double quantity = askData[1].toString().toDouble();

    return price * quantity;
}

// 实现获取买单强度
double MarketStrengthAnalyzer::getBuyStrength() const
{
    return m_currentStrengthData.bidVolume;
}

// 实现获取卖单强度
double MarketStrengthAnalyzer::getSellStrength() const
{
    return m_currentStrengthData.askVolume;
}

// 实现获取买卖强度比率
double MarketStrengthAnalyzer::getBuySellRatio() const
{
    return m_currentStrengthData.bidAskRatio;
}
