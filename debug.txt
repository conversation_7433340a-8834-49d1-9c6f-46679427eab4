2025年07月10日14:51:48       SSL支持可用，OpenSSL版本： "OpenSSL 3.0.15 3 Sep 2024"
2025年07月10日14:51:48       注册线程，当前线程数: 1
2025年07月10日14:51:48       ApiWorker已创建，ApiManager将在工作线程中创建
2025年07月10日14:51:48       正在工作线程 QThread(0x1f88ee35130) 中初始化ApiManager
2025年07月10日14:51:49       WebSocket自动重连已 启用
2025年07月10日14:51:49       WebSocket心跳间隔设置为 20000 毫秒
2025年07月10日14:51:49       WebSocket重连间隔设置为 5000 毫秒
2025年07月10日14:51:49       WebSocket最大重连尝试次数设置为 10
2025年07月10日14:51:49       ApiManager已在工作线程 QThread(0x1f88ee35130) 中初始化完成
2025年07月10日15:27:28       收到Close事件，对象类型: "QWidgetWindow"
2025年07月10日15:27:28       收到Close事件，对象类型: "LoginWindow"
2025年07月10日15:27:28       应用程序主窗口即将关闭，执行清理操作...
2025年07月10日15:27:28       应用程序正常关闭
2025年07月10日15:27:28       ApiManager对象已安排删除
2025年07月10日15:27:28       ApiWorker析构函数执行完毕
2025年07月10日15:27:28       应用程序主窗口即将关闭，执行清理操作...
2025年07月10日15:27:28       应用程序正常关闭
2025年07月10日15:27:28       应用程序即将退出，执行最终清理...
2025年07月10日15:27:28       应用程序即将退出，执行清理...
2025年07月10日15:27:28       正在取消所有进行中的网络请求...
2025年07月10日15:27:28       所有网络请求已取消
2025年07月10日15:27:28       强制关闭所有网络连接...
2025年07月10日15:27:28       所有网络连接已安排关闭
2025年07月10日15:27:29       等待网络连接关闭完成...
2025年07月10日15:27:29       强制关闭所有网络连接...
2025年07月10日15:27:29       所有网络连接已安排关闭
2025年07月10日15:27:29       强制终止所有线程...
2025年07月10日15:27:29       收到Close事件，对象类型: "QWidgetWindow"
2025年07月10日15:27:29       收到Close事件，对象类型: "LoginWindow"
2025年07月10日15:27:29       应用程序主窗口即将关闭，执行清理操作...
2025年07月10日15:27:29       应用程序正常关闭
2025年07月10日15:27:29       ApiManager对象已安排删除
2025年07月10日15:27:29       ApiWorker析构函数执行完毕
2025年07月10日15:27:29       应用程序主窗口即将关闭，执行清理操作...
2025年07月10日15:27:29       应用程序正常关闭
2025年07月10日15:27:29       应用程序即将退出，执行最终清理...
2025年07月10日15:27:29       应用程序即将退出，执行清理...
2025年07月10日15:27:29       正在取消所有进行中的网络请求...
2025年07月10日15:27:29       所有网络请求已取消
2025年07月10日15:27:29       强制关闭所有网络连接...
2025年07月10日15:27:29       所有网络连接已安排关闭
2025年07月10日15:27:30       等待网络连接关闭完成...
2025年07月10日15:27:30       强制关闭所有网络连接...
2025年07月10日15:27:30       所有网络连接已安排关闭
2025年07月10日15:27:30       强制终止所有线程...
