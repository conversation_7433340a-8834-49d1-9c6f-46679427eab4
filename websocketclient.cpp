#include "websocketclient.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDebug>
#include <QDateTime>
#include <QRandomGenerator>

WebSocketClient::WebSocketClient(QObject *parent)
    : QObject(parent), m_webSocket(nullptr), m_heartbeatTimer(nullptr), m_reconnectTimer(nullptr), m_autoReconnect(true),
      // 修改心跳间隔为20秒，这与币安服务器的要求保持一致（币安每20秒发送一次ping，要求60秒内收到响应）
      m_heartbeatInterval(20000), m_reconnectInterval(10000), m_inactivityTimeout(120000), m_maxReconnectAttempts(5), m_reconnectAttempts(0),
      m_isReconnecting(false)
{
    m_webSocket = new QWebSocket();
    m_heartbeatTimer = new QTimer(this);
    m_reconnectTimer = new QTimer(this);

    // 连接WebSocket信号
    connect(m_webSocket, &QWebSocket::connected, this, &WebSocketClient::onConnected);
    connect(m_webSocket, &QWebSocket::disconnected, this, &WebSocketClient::onDisconnected);
    connect(m_webSocket, &QWebSocket::textMessageReceived, this, &WebSocketClient::onTextMessageReceived);
    connect(m_webSocket, QOverload<QAbstractSocket::SocketError>::of(&QWebSocket::error), this, &WebSocketClient::onError);
    connect(m_webSocket, &QWebSocket::stateChanged, this, &WebSocketClient::onStateChanged);

    // 添加对ping帧的显式处理 - 这里直接使用QWebSocket的pong方法响应
    connect(m_webSocket, &QWebSocket::pong, this, [this](quint64 elapsedTime, const QByteArray &payload)
            {
        m_lastMessageTime = QDateTime::currentDateTime();

        // 发送延迟信号到UI
        emit sg_sendMs(elapsedTime); });

    // 直接连接QWebSocket的binaryMessageReceived信号，用于处理可能的二进制ping帧
    connect(m_webSocket, &QWebSocket::binaryMessageReceived, this, [this](const QByteArray &message)
            {
        // qDebug().noquote() << "收到WebSocket二进制消息，大小: " << message.size() << "字节";

        // 更新最后消息时间
        m_lastMessageTime = QDateTime::currentDateTime(); });

    // 连接定时器信号
    connect(m_heartbeatTimer, &QTimer::timeout, this, &WebSocketClient::onHeartbeatTimer);
    connect(m_reconnectTimer, &QTimer::timeout, this, &WebSocketClient::onReconnectTimer);

    // 记录当前时间为最后消息时间
    m_lastMessageTime = QDateTime::currentDateTime();

    // 初始化锁，防止同时多个操作修改WebSocket状态
    m_isConnecting = false;
}

WebSocketClient::~WebSocketClient()
{
    // 停止所有定时器
    if (m_heartbeatTimer)
    {
        m_heartbeatTimer->stop();
    }

    if (m_reconnectTimer)
    {
        m_reconnectTimer->stop();
    }

    // 关闭WebSocket连接
    if (m_webSocket)
    {
        // 断开所有信号连接，防止后续操作触发回调
        disconnect(m_webSocket, nullptr, this, nullptr);

        // 如果WebSocket处于连接状态，关闭连接
        if (m_webSocket->state() != QAbstractSocket::UnconnectedState)
        {
            m_webSocket->abort();
        }

        // 释放WebSocket对象
        delete m_webSocket;
        m_webSocket = nullptr;
    }

    // 释放定时器对象
    if (m_heartbeatTimer)
    {
        delete m_heartbeatTimer;
        m_heartbeatTimer = nullptr;
    }

    if (m_reconnectTimer)
    {
        delete m_reconnectTimer;
        m_reconnectTimer = nullptr;
    }
}

void WebSocketClient::open(const QUrl &url)
{
    // 检查是否正在连接中，避免并发连接操作
    if (m_isConnecting)
    {
        qDebug() << "WebSocket已经在连接过程中，忽略这次连接请求: " << url.toString();
        return;
    }

    // 检查URL是否与当前URL相同，且连接状态为已连接，则无需重连
    if (m_webSocket && m_url == url && m_webSocket->state() == QAbstractSocket::ConnectedState)
    {
        qDebug() << "WebSocket已连接到相同URL，无需重新连接: " << url.toString();
        return;
    }

    // 调整URL，确保正确的格式
    QUrl adjustedUrl = url;

    // 设置连接锁
    m_isConnecting = true;

    // 保存URL用于重连
    m_url = adjustedUrl;

    // 设置WebSocket选项
    QNetworkRequest request(adjustedUrl);
    request.setRawHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

    // 启用长连接支持和压缩
    request.setRawHeader("Connection", "Upgrade");
    request.setRawHeader("Upgrade", "websocket");
    request.setRawHeader("Accept-Encoding", "gzip, deflate, br");

    qDebug() << "正在连接WebSocket:" << adjustedUrl.toString();

    // 如果WebSocket已存在但不是未连接状态，先关闭它
    if (m_webSocket && m_webSocket->state() != QAbstractSocket::UnconnectedState)
    {
        qDebug().noquote() << "WebSocket当前状态: " << m_webSocket->state() << "，尝试先关闭它";

        // 使用abort强制关闭连接
        m_webSocket->abort();

        // 给WebSocket一些时间完成关闭
        QTimer::singleShot(3000, this, [this, adjustedUrl, request]()
                           {
            // 检查WebSocket状态
            if (m_webSocket->state() != QAbstractSocket::UnconnectedState) {
                qDebug().noquote() << "WebSocket仍未完全关闭，状态: " << m_webSocket->state() << "，尝试强制重置";

                // 断开信号连接
                m_webSocket->disconnect();

                // 删除并重新创建WebSocket对象
                delete m_webSocket;
                m_webSocket = new QWebSocket();

                // 重新连接信号
                connect(m_webSocket, &QWebSocket::connected, this, &WebSocketClient::onConnected);
                connect(m_webSocket, &QWebSocket::disconnected, this, &WebSocketClient::onDisconnected);
                connect(m_webSocket, &QWebSocket::textMessageReceived, this, &WebSocketClient::onTextMessageReceived);
                connect(m_webSocket, QOverload<QAbstractSocket::SocketError>::of(&QWebSocket::error), this, &WebSocketClient::onError);
                connect(m_webSocket, &QWebSocket::stateChanged, this, &WebSocketClient::onStateChanged);
                connect(m_webSocket, &QWebSocket::pong, this, [this](quint64 elapsedTime, const QByteArray &payload) {
                    m_lastMessageTime = QDateTime::currentDateTime();

                    // 发送延迟信号到UI
                    emit sg_sendMs(elapsedTime);
                });
                connect(m_webSocket, &QWebSocket::binaryMessageReceived, this, [this](const QByteArray &message) {
                    // qDebug().noquote() << "收到WebSocket二进制消息，大小: " << message.size() << "字节";
                    m_lastMessageTime = QDateTime::currentDateTime();
                });
            }

            // 重置重连计数器
            resetReconnectCounter();

            // 打开WebSocket连接，使用请求头选项
            qDebug().noquote() << "开始打开WebSocket连接: " << adjustedUrl.toString();
            m_webSocket->open(request);

            // 释放连接锁，但延迟一点时间，避免并发连接
            QTimer::singleShot(500, this, [this]() { m_isConnecting = false; }); });
    }
    else
    {
        // 重置重连计数器
        resetReconnectCounter();

        // 打开WebSocket连接，使用请求头选项
        qDebug().noquote() << "开始打开WebSocket连接: " << adjustedUrl.toString();
        m_webSocket->open(request);

        // 释放连接锁，但延迟一点时间，避免并发连接
        QTimer::singleShot(500, this, [this]()
                           { m_isConnecting = false; });
    }
}

void WebSocketClient::close()
{
    // 停止心跳和重连
    stopHeartbeat();
    m_reconnectTimer->stop();
    m_isReconnecting = false;

    if (m_webSocket)
    {
        qDebug() << "正在关闭WebSocket连接";
        m_webSocket->close();
    }
}

QAbstractSocket::SocketState WebSocketClient::state() const
{
    return m_webSocket ? m_webSocket->state() : QAbstractSocket::UnconnectedState;
}

bool WebSocketClient::isValid() const
{
    return m_webSocket && m_webSocket->isValid();
}

QString WebSocketClient::errorString() const
{
    if (m_webSocket)
    {
        return m_webSocket->errorString();
    }
    return "WebSocket未初始化";
}

QUrl WebSocketClient::requestUrl() const
{
    return m_url;
}

void WebSocketClient::sendTextMessage(const QString &message)
{
    if (m_webSocket && m_webSocket->state() == QAbstractSocket::ConnectedState)
    {
        m_webSocket->sendTextMessage(message);
    }
    else
    {
        qWarning() << "WebSocket未连接，无法发送消息";
    }
}

void WebSocketClient::abort()
{
    // 停止心跳和重连
    stopHeartbeat();
    m_reconnectTimer->stop();
    m_isReconnecting = false;

    if (m_webSocket)
    {
        qDebug() << "正在中止WebSocket连接";
        m_webSocket->abort();
    }
}

void WebSocketClient::setAutoReconnect(bool autoReconnect)
{
    m_autoReconnect = autoReconnect;
    qDebug() << "WebSocket自动重连已" << (autoReconnect ? "启用" : "禁用");
}

void WebSocketClient::setHeartbeatInterval(int interval)
{
    m_heartbeatInterval = interval;
    qDebug() << "WebSocket心跳间隔设置为" << interval << "毫秒";

    // 如果心跳定时器正在运行，更新间隔
    if (m_heartbeatTimer->isActive())
    {
        m_heartbeatTimer->setInterval(interval);
    }
}

void WebSocketClient::setReconnectInterval(int interval)
{
    m_reconnectInterval = interval;
    qDebug() << "WebSocket重连间隔设置为" << interval << "毫秒";
}

void WebSocketClient::setMaxReconnectAttempts(int maxAttempts)
{
    m_maxReconnectAttempts = maxAttempts;
    qDebug() << "WebSocket最大重连尝试次数设置为" << maxAttempts;
}

void WebSocketClient::setInactivityTimeout(int timeout)
{
    m_inactivityTimeout = timeout;
    qDebug() << "WebSocket无响应超时时间设置为" << timeout << "毫秒";
}

void WebSocketClient::sendHeartbeat()
{
    // 发送WebSocket协议级Ping控制帧（opcode 0x9）
    if (m_webSocket && m_webSocket->state() == QAbstractSocket::ConnectedState)
    {
        // 使用WebSocket协议级别的ping帧，这是标准的WebSocket心跳机制
        // 币安要求使用标准WebSocket Ping控制帧，而非JSON文本消息
        QByteArray pingPayload = QByteArray::number(QDateTime::currentMSecsSinceEpoch());
        m_webSocket->ping(pingPayload);
        // qDebug().noquote() << "发送WebSocket协议级ping帧，payload: " << pingPayload;
        emit heartbeatSent();
    }
    else
    {
        qWarning().noquote() << "尝试发送心跳，但WebSocket不处于已连接状态，当前状态: " << (m_webSocket ? m_webSocket->state() : -1);
    }
}

void WebSocketClient::onConnected()
{
    qDebug().noquote() << "WebSocket连接成功";

    // 更新状态
    m_isConnecting = false;
    m_isReconnecting = false;

    // 重置重连尝试计数
    resetReconnectCounter();

    // 停止任何正在进行的重连定时器
    if (m_reconnectTimer->isActive())
    {
        m_reconnectTimer->stop();
    }

    // 记录当前时间作为最后消息接收时间
    m_lastMessageTime = QDateTime::currentDateTime();

    // 启动心跳机制
    startHeartbeat();

    // 如果是重连成功，发出重连成功的信号
    emit reconnected();

    // 发出连接成功信号
    emit connected();

    // 在连接成功后，恢复之前的订阅
    updateWebSocketSubscriptions();
}

void WebSocketClient::onDisconnected()
{
    qDebug().noquote() << "WebSocket连接断开，URL: " << m_webSocket->requestUrl().toString()
                       << "，上次活动时间: " << m_lastMessageTime.toString("yyyy-MM-dd hh:mm:ss.zzz")
                       << "，当前订阅的流: " << m_subscribedTradeStreams.values();

    // 发出断开连接信号
    emit disconnected();

    // 停止心跳
    stopHeartbeat();

    // 如果不是主动断开连接，且启用了自动重连，则进行重连
    if (m_autoReconnect && !m_isReconnecting)
    {
        m_isReconnecting = true;

        // 计算重连延迟时间
        int timeSinceLastMsg = m_lastMessageTime.msecsTo(QDateTime::currentDateTime());

        // 如果距离上次收到消息的时间太短（小于1秒），可能是由于频繁断开重连
        // 这种情况下增加延迟，避免过于频繁的重连尝试
        if (timeSinceLastMsg < 1000)
        {
            qDebug().noquote() << "距离上次消息很短: " << timeSinceLastMsg << " ms，延迟5秒再重连";
            QTimer::singleShot(5000, this, &WebSocketClient::reconnect);
        }
        else
        {
            // 否则使用指数退避策略计算延迟
            int backoffDelay = calculateBackoffDelay();
            qDebug().noquote() << "计划在 " << backoffDelay << " 毫秒后重连";
            QTimer::singleShot(backoffDelay, this, &WebSocketClient::reconnect);
        }
    }
    else
    {
        qDebug().noquote() << "WebSocket断开连接，但未启用自动重连或已在重连过程中";
    }
}

void WebSocketClient::onTextMessageReceived(const QString &message)
{
    // 更新最后接收消息的时间
    m_lastMessageTime = QDateTime::currentDateTime();

    // 记录收到的每条消息，对于心跳响应消息单独记录
    bool isPingResponse = false;

    // 先尝试解析为JSON，检查是否是心跳响应
    QJsonParseError parseError;
    QJsonDocument jsonDoc = QJsonDocument::fromJson(message.toUtf8(), &parseError);
    QJsonObject obj = jsonDoc.object();
    QString eventType = obj.value("e").toString();

    if (eventType == "executionReport")
    {
        QString status = obj.value("X").toString(); // 订单状态
        QString symbol = obj.value("s").toString();
        QString orderId = QString::number(obj.value("i").toVariant().toLongLong());
        double price = obj.value("p").toString().toDouble();    // 最新成交价格
        double quantity = obj.value("q").toString().toDouble(); // 最新成交数量
        qDebug() << "executionReport1111:" << status << symbol << orderId << price << quantity;
    }
    if (parseError.error == QJsonParseError::NoError && jsonDoc.isObject())
    {
        QJsonObject jsonObj = jsonDoc.object();

        // 检查是否是币安服务器发送的ping消息：{"method":"ping","id":xxx}
        if (jsonObj.contains("method") && jsonObj["method"].toString().toLower() == "ping")
        {
            qDebug().noquote() << "收到WebSocket JSON格式ping消息: " << message;
            isPingResponse = true;

            // 构建pong响应
            QJsonObject pongObj;
            pongObj["method"] = "pong";
            // 如果服务器ping消息中有id，在pong中使用相同的id
            if (jsonObj.contains("id"))
            {
                pongObj["id"] = jsonObj["id"];
            }

            QJsonDocument pongDoc(pongObj);
            QString pongMessage = pongDoc.toJson(QJsonDocument::Compact);

            // 发送pong响应
            sendTextMessage(pongMessage);
            qDebug().noquote() << "发送WebSocket JSON格式pong响应: " << pongMessage;

            // 更新最后消息时间
            m_lastMessageTime = QDateTime::currentDateTime();
        }
        // 检查是否是标准心跳响应：{"id": xxx, "result": null}
        else if (jsonObj.contains("id") && jsonObj.contains("result") && jsonObj["result"].isNull())
        {
            isPingResponse = true;
            qDebug().noquote() << "收到WebSocket心跳响应: " << message;
            emit heartbeatReceived();
        }
        // 检查是否是订阅确认消息
        else if (jsonObj.contains("id") && jsonObj.contains("result") && jsonObj["result"].isArray())
        {
            isPingResponse = false;
            qDebug().noquote() << "收到WebSocket订阅确认: " << message;
        }
        // 检查是否是订单更新消息
        else if (jsonObj.contains("e") && (jsonObj["e"].toString() == "executionReport" || jsonObj["e"].toString() == "outboundAccountPosition"))
        {
            // 解析订单更新
            parseOrderUpdate(jsonObj);
        }
        // 检查是否是交易数据消息
        else if (jsonObj.contains("e") && jsonObj["e"].toString() == "trade")
        {
            // 解析交易数据
            parseTradeData(jsonObj);
        }
        // 检查是否是bookTicker数据消息
        else if (jsonObj.contains("s") && jsonObj.contains("b") && jsonObj.contains("a"))
        {
            // 解析bookTicker数据
            parseBookTickerData(jsonObj);
        }
        else
        {
            // 其他未知类型的JSON消息，记录但不做特殊处理
            qDebug().noquote() << "收到WebSocket未知类型JSON消息: " << message.left(200) << (message.length() > 200 ? "..." : "");
        }
    }
    else
    {
        // 解析JSON失败，可能是二进制消息或其他格式
        qDebug().noquote() << "收到WebSocket非JSON消息或解析失败: " << message.left(100) << (message.length() > 100 ? "..." : "");
    }

    // 发出文本消息接收信号
    if (!isPingResponse)
    {
        emit textMessageReceived(message);
    }
}

void WebSocketClient::onError(QAbstractSocket::SocketError error)
{
    qWarning() << "WebSocket错误:" << error << m_webSocket->errorString();
    emit this->error(error);

    // 对于某些错误，可能需要重连
    if (m_autoReconnect && !m_isReconnecting)
    {
        switch (error)
        {
        case QAbstractSocket::RemoteHostClosedError:
        case QAbstractSocket::NetworkError:
        case QAbstractSocket::TemporaryError:
            qDebug() << "遇到临时错误，准备重连";
            m_isReconnecting = true;
            reconnect();
            break;
        default:
            // 其他错误可能是致命的，不自动重连
            break;
        }
    }
}

void WebSocketClient::onStateChanged(QAbstractSocket::SocketState state)
{
    qDebug() << "WebSocket状态变化:" << state;
    emit connectionStateChanged(state);
}

void WebSocketClient::onHeartbeatTimer()
{
    // 只在连接状态下发送心跳
    if (m_webSocket && m_webSocket->state() == QAbstractSocket::ConnectedState)
    {
        // 使用WebSocket协议级别的ping帧，而非JSON文本消息
        // 这是币安WebSocket服务器要求的标准方式
        QByteArray pingPayload = QByteArray::number(QDateTime::currentMSecsSinceEpoch());
        m_webSocket->ping(pingPayload);

        // 发出心跳发送信号
        emit heartbeatSent();
    }

    // 检查上次收到消息的时间，判断连接是否超时
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 timeSinceLastMessage = m_lastMessageTime.msecsTo(currentTime);

    // 使用设置的无响应超时时间判断连接是否断开
    // 如果超过设定的无响应时间未收到任何消息，则认为连接可能已断开，需要重连
    if (timeSinceLastMessage > m_inactivityTimeout && m_webSocket->state() == QAbstractSocket::ConnectedState)
    {
        qWarning().noquote() << "长时间未收到WebSocket消息: " << timeSinceLastMessage << "ms, 超过设定阈值: " << m_inactivityTimeout
                             << "ms, 可能连接已断开，尝试重连";
        logWebSocketState("心跳超时");

        // 尝试关闭并重新连接
        if (m_autoReconnect && !m_isReconnecting)
        {
            m_isReconnecting = true;
            m_webSocket->abort();

            // 增加延迟时间，确保连接完全关闭后再重连
            QTimer::singleShot(5000, this, &WebSocketClient::reconnect);
        }
    }
}

void WebSocketClient::onReconnectTimer()
{
    // 如果WebSocket已经连接，就不需要重连
    if (m_webSocket->state() == QAbstractSocket::ConnectedState)
    {
        qDebug().noquote() << "WebSocket已连接，取消重连操作";
        m_reconnectTimer->stop();
        m_isReconnecting = false;
        return;
    }

    // 重连定时器触发，尝试重连
    if (m_reconnectAttempts < m_maxReconnectAttempts)
    {
        m_reconnectAttempts++;

        // 发出重连尝试信号
        emit reconnectAttempt(m_reconnectAttempts, m_maxReconnectAttempts);

        // 检查当前WebSocket状态，只在未连接状态下执行重连
        if (m_webSocket->state() != QAbstractSocket::UnconnectedState)
        {
            qDebug().noquote() << "尝试重连但WebSocket当前状态不是未连接，状态: " << m_webSocket->state() << "，先中止现有连接";
            m_webSocket->abort();

            // 短延迟后再次尝试
            QTimer::singleShot(2000, this, [this]()
                               {
                if (m_webSocket->state() == QAbstractSocket::UnconnectedState) {
                    qDebug().noquote() << "WebSocket已关闭，现在开始连接";
                    m_webSocket->open(m_url);
                } else {
                    qDebug().noquote() << "WebSocket仍未关闭，放弃本次重连尝试";
                    // 增加重连间隔，避免频繁尝试
                    m_reconnectTimer->setInterval(m_reconnectInterval * 2);
                    m_reconnectTimer->start();
                } });
            return;
        }

        // 打开连接
        qDebug().noquote() << "尝试重连WebSocket (尝试 " << m_reconnectAttempts << " / " << m_maxReconnectAttempts << ")，URL: " << m_url.toString();
        m_webSocket->open(m_url);
    }
    else
    {
        qWarning() << "WebSocket重连达到最大尝试次数 (" << m_maxReconnectAttempts << ")，放弃重连";
        m_reconnectTimer->stop();
        m_isReconnecting = false;

        // 重置连接锁，确保未来可以重新连接
        m_isConnecting = false;

        // 清空订阅列表
        if (!m_subscribedTradeStreams.isEmpty())
        {
            qDebug().noquote() << "由于重连失败，清空订阅列表: " << m_subscribedTradeStreams.values();
            m_subscribedTradeStreams.clear();
        }
    }
}

void WebSocketClient::reconnect()
{
    // 确保重连定时器没有在运行
    m_reconnectTimer->stop();

    // 如果当前已经连接，不需要重连
    if (m_webSocket && m_webSocket->state() == QAbstractSocket::ConnectedState)
    {
        qDebug().noquote() << "WebSocket已连接，不需要重连";
        m_isReconnecting = false;
        return;
    }

    // 如果当前连接未完全关闭，先关闭它
    if (m_webSocket && m_webSocket->state() != QAbstractSocket::UnconnectedState)
    {
        qDebug().noquote() << "重连前检测到WebSocket尚未完全关闭，状态: " << m_webSocket->state() << "，先中止连接";

        // 强制中止连接
        m_webSocket->abort();

        // 确保连接锁被释放
        m_isConnecting = false;

        // 短暂延迟，确保连接完全关闭
        QTimer::singleShot(3000, this, [this]()
                           {
            // 设置连接锁，防止并发连接
            m_isConnecting = true;

            // 再次检查状态
            if (m_webSocket->state() == QAbstractSocket::UnconnectedState) {
                // 状态正确，可以重新连接
                qDebug().noquote() << "WebSocket已完全关闭，开始重新连接";

                // 计算指数退避时间
                int backoffDelay = calculateBackoffDelay();
                qDebug().noquote() << "使用指数退避策略，下次重连间隔:" << backoffDelay << "毫秒";

                // 启动重连定时器
                m_reconnectTimer->setInterval(backoffDelay);
                m_reconnectTimer->start();

                // 在短时间后释放连接锁
                QTimer::singleShot(500, this, [this]() { m_isConnecting = false; });
            } else {
                // 状态仍不正确，执行更强力的重置
                qDebug().noquote() << "WebSocket仍未完全关闭，状态: " << m_webSocket->state() << "，执行强制重置";

                // 断开所有信号连接
                m_webSocket->disconnect();

                // 删除并重新创建WebSocket对象
                delete m_webSocket;
                m_webSocket = new QWebSocket();

                // 重新连接信号
                connect(m_webSocket, &QWebSocket::connected, this, &WebSocketClient::onConnected);
                connect(m_webSocket, &QWebSocket::disconnected, this, &WebSocketClient::onDisconnected);
                connect(m_webSocket, &QWebSocket::textMessageReceived, this, &WebSocketClient::onTextMessageReceived);
                connect(m_webSocket, QOverload<QAbstractSocket::SocketError>::of(&QWebSocket::error), this, &WebSocketClient::onError);
                connect(m_webSocket, &QWebSocket::stateChanged, this, &WebSocketClient::onStateChanged);
                connect(m_webSocket, &QWebSocket::pong, this, [this](quint64 elapsedTime, const QByteArray &payload) {
                    m_lastMessageTime = QDateTime::currentDateTime();

                    // 发送延迟信号到UI
                    emit sg_sendMs(elapsedTime);
                });
                connect(m_webSocket, &QWebSocket::binaryMessageReceived, this, [this](const QByteArray &message) {
                    qDebug().noquote() << "收到WebSocket二进制消息，大小: " << message.size() << "字节";
                    m_lastMessageTime = QDateTime::currentDateTime();
                });

                // 状态重置后进行连接
                qDebug().noquote() << "WebSocket对象已重置，立即开始连接";
                m_isConnecting = false; // 释放锁，允许新连接

                // 增加延迟再次尝试连接
                QTimer::singleShot(2000, this, [this]() {
                    // 计算指数退避时间
                    int backoffDelay = calculateBackoffDelay();
                    qDebug().noquote() << "使用指数退避策略，下次重连间隔:" << backoffDelay << "毫秒";

                    // 启动重连定时器
                    m_reconnectTimer->setInterval(backoffDelay);
                    m_reconnectTimer->start();
                });
            } });
        return;
    }

    // 启动重连定时器进行后续尝试，使用指数退避策略
    int backoffDelay = calculateBackoffDelay();
    qDebug().noquote() << "使用指数退避策略，下次重连间隔:" << backoffDelay << "毫秒";

    m_reconnectTimer->setInterval(backoffDelay);
    m_reconnectTimer->start();

    // 在短时间后释放连接锁
    QTimer::singleShot(500, this, [this]()
                       { m_isConnecting = false; });
}

// 计算指数退避延迟
int WebSocketClient::calculateBackoffDelay()
{
    // 基础延迟（毫秒）
    int baseDelay = 1000;

    // 指数因子（2的重连次数次方，但限制最大指数为8）
    int exponentialFactor = 1 << qMin(m_reconnectAttempts, 8);

    // 随机抖动（0-1000毫秒）以避免多客户端同时重连
    int jitter = QRandomGenerator::global()->bounded(1000);

    // 计算延迟时间
    int delay = baseDelay * exponentialFactor + jitter;

    // 限制最大延迟时间（不超过5分钟）
    return qMin(delay, 300000);
}

void WebSocketClient::startHeartbeat()
{
    // 先确保心跳定时器存在
    if (!m_heartbeatTimer)
    {
        qWarning() << "心跳定时器未初始化，无法启动";
        return;
    }

    // 如果已经在运行，先停止再重新启动，确保间隔正确
    if (m_heartbeatTimer->isActive())
    {
        m_heartbeatTimer->stop();
    }

    // 设置合理的心跳间隔：每20秒发送一次WebSocket协议级Ping
    // 币安要求在60秒内必须收到Pong响应，否则断开连接
    m_heartbeatInterval = 20000; // 设置为20秒

    // 启动心跳定时器
    m_heartbeatTimer->start(m_heartbeatInterval);
}

void WebSocketClient::stopHeartbeat()
{
    if (m_heartbeatTimer->isActive())
    {
        m_heartbeatTimer->stop();
    }
}

void WebSocketClient::resetReconnectCounter()
{
    m_reconnectAttempts = 0;
}

void WebSocketClient::parseOrderUpdate(const QJsonObject &data)
{
    // 检查是否是订单更新消息
    if (data.contains("e") && (data["e"].toString() == "executionReport" || data["e"].toString() == "outboundAccountPosition"))
    {
        // 这是一个订单更新消息
        qDebug() << "收到WebSocket订单更新消息:" << data["e"].toString();
        emit orderUpdateReceived(data);
    }
}

// 订阅交易流
void WebSocketClient::subscribeTradeStream(const QString &symbol)
{
    QString streamName = symbol.toLower() + "@trade";
    if (!m_subscribedTradeStreams.contains(streamName))
    {
        m_subscribedTradeStreams.insert(streamName);
        qDebug() << "订阅交易流:" << streamName;
        updateWebSocketSubscriptions();

        // 发送订阅状态变化信号
        emit subscriptionChanged(symbol, true);
    }
}

// 取消订阅交易流
void WebSocketClient::unsubscribeTradeStream(const QString &symbol)
{
    // 创建取消订阅消息
    QString lowerSymbol = symbol.toLower();
    QString stream = lowerSymbol + "@trade";

    // 从订阅集合中移除
    if (m_subscribedTradeStreams.contains(stream))
    {
        m_subscribedTradeStreams.remove(stream);

        // 更新WebSocket订阅
        updateWebSocketSubscriptions();

        qDebug() << "已取消订阅交易流:" << stream;
        emit subscriptionChanged(symbol, false);
    }
}

// 获取当前已订阅的交易流
QStringList WebSocketClient::getSubscribedTradeStreams() const
{
    return m_subscribedTradeStreams.values();
}

// 订阅bookTicker流
void WebSocketClient::subscribeBookTickerStream(const QString &symbol)
{
    QString streamName = symbol.toLower() + "@bookTicker";
    if (!m_subscribedBookTickerStreams.contains(streamName))
    {
        m_subscribedBookTickerStreams.insert(streamName);
        qDebug() << "订阅bookTicker流:" << streamName;
        updateWebSocketSubscriptions();
    }
}

// 取消订阅bookTicker流
void WebSocketClient::unsubscribeBookTickerStream(const QString &symbol)
{
    // 创建取消订阅消息
    QString lowerSymbol = symbol.toLower();
    QString stream = lowerSymbol + "@bookTicker";

    // 从订阅集合中移除
    if (m_subscribedBookTickerStreams.contains(stream))
    {
        m_subscribedBookTickerStreams.remove(stream);

        // 更新WebSocket订阅
        updateWebSocketSubscriptions();

        qDebug() << "已取消订阅bookTicker流:" << stream;
    }
}

// 解析bookTicker数据
void WebSocketClient::parseBookTickerData(const QJsonObject &data)
{
    // 构建bookTicker数据对象
    QJsonObject bookTickerData;

    // 复制原始数据的关键字段
    bookTickerData["symbol"] = data["s"].toString();
    bookTickerData["bidPrice"] = data["b"].toString();
    bookTickerData["bidQty"] = data["B"].toString();
    bookTickerData["askPrice"] = data["a"].toString();
    bookTickerData["askQty"] = data["A"].toString();

    // 记录日志
    QString symbol = bookTickerData["symbol"].toString();
    QString bidPrice = bookTickerData["bidPrice"].toString();
    QString askPrice = bookTickerData["askPrice"].toString();

    // qDebug() << "接收到bookTicker流数据: 交易对=" << symbol << ", 买一价=" << bidPrice << ", 卖一价=" << askPrice;

    // 发送bookTicker数据信号
    emit bookTickerDataReceived(bookTickerData);
}

// 修改updateWebSocketSubscriptions方法，支持bookTicker流
void WebSocketClient::updateWebSocketSubscriptions()
{
    // 如果没有需要恢复的订阅，直接返回
    if (m_subscribedTradeStreams.isEmpty() && m_subscribedBookTickerStreams.isEmpty())
    {
        qDebug().noquote() << "没有需要恢复的订阅";
        return;
    }

    // 确保WebSocket处于连接状态
    if (m_webSocket->state() != QAbstractSocket::ConnectedState)
    {
        qDebug().noquote() << "WebSocket未连接，无法更新订阅";
        return;
    }

    // 等待短暂时间确保连接稳定
    QTimer::singleShot(1000, this, [this]()
                       {
        // 再次检查WebSocket状态，确保仍然连接
        if (m_webSocket->state() != QAbstractSocket::ConnectedState) {
            qDebug().noquote() << "延迟订阅时WebSocket已断开，取消订阅操作";
            return;
        }

        // 合并所有需要订阅的流
        QStringList streams;
        streams.append(m_subscribedTradeStreams.values());
        streams.append(m_subscribedBookTickerStreams.values());

        // 如果没有需要订阅的流，直接返回
        if (streams.isEmpty()) {
            return;
        }

        qDebug().noquote() << "恢复订阅: " << streams;

        // 构建币安WebSocket订阅消息
        QJsonObject subscribeMsg;
        subscribeMsg["id"] = QDateTime::currentMSecsSinceEpoch();
        subscribeMsg["method"] = "SUBSCRIBE";
        subscribeMsg["params"] = QJsonArray::fromStringList(streams);

        // 将对象转换为JSON字符串
        QJsonDocument doc(subscribeMsg);
        QString message = doc.toJson(QJsonDocument::Compact);

        // 发送订阅请求
        m_webSocket->sendTextMessage(message);
        qDebug().noquote() << "发送WebSocket订阅消息: " << message; });
}

// 解析交易数据
void WebSocketClient::parseTradeData(const QJsonObject &data)
{
    // 检查是否是交易数据
    if (data.contains("e") && data["e"].toString() == "trade")
    {
        // 构建交易数据对象
        QJsonObject tradeData;
        tradeData["symbol"] = data["s"].toString();
        tradeData["id"] = data["t"].toVariant().toLongLong();
        tradeData["price"] = data["p"].toString();
        tradeData["qty"] = data["q"].toString();
        tradeData["time"] = data["T"].toVariant().toLongLong();
        tradeData["isBuyerMaker"] = data["m"].toBool();
        tradeData["isBestMatch"] = data["M"].toBool();

        // 记录每笔交易，无论价格如何，用于诊断
        QString symbol = tradeData["symbol"].toString();
        double price = tradeData["price"].toDouble();
        double qty = tradeData["qty"].toDouble();
        bool isBuyerMaker = tradeData["isBuyerMaker"].toBool();

        // qDebug() << "接收到交易流数据: 交易对=" << symbol << ", 价格=" << price << ", 数量=" << qty << ", 买家是挂单方=" << isBuyerMaker;

        // 发送交易数据信号
        emit tradeDataReceived(tradeData);
    }
}

// 重新连接WebSocket槽
void WebSocketClient::reconnectWebSocket()
{
    if (!m_url.isValid())
    {
        qDebug() << "无效的WebSocket URL，无法重新连接";
        return;
    }

    // 检查是否正在连接中，避免并发连接操作
    if (m_isConnecting)
    {
        qDebug() << "WebSocket已经在连接过程中，忽略这次重连请求";
        return;
    }

    qDebug() << "收到重连WebSocket请求...当前状态: " << m_webSocket->state();

    // 设置连接锁
    m_isConnecting = true;

    // 如果正在连接或已经连接成功，不要重新连接
    if (m_webSocket->state() == QAbstractSocket::ConnectingState)
    {
        qDebug() << "WebSocket正在连接中，忽略重连请求";
        m_isConnecting = false;
        return;
    }

    if (m_webSocket->state() == QAbstractSocket::ConnectedState)
    {
        qDebug() << "WebSocket已经连接，不需要重连";
        m_isConnecting = false;
        // 如果有订阅需求，可以直接更新订阅
        if (!m_subscribedTradeStreams.isEmpty())
        {
            QTimer::singleShot(100, this, [this]()
                               { updateWebSocketSubscriptions(); });
        }
        return;
    }

    // 如果已经在连接状态，先断开
    if (m_webSocket && m_webSocket->state() != QAbstractSocket::UnconnectedState)
    {
        qDebug() << "WebSocket当前状态不是未连接，先中止当前连接";
        m_webSocket->abort();
        QTimer::singleShot(100, this, [this]() { // 短暂延迟，确保关闭完成
            m_isConnecting = false;              // 重置连接锁
            reconnectWebSocket();                // 重新尝试连接
        });
        return;
    }

    // 重置重连计数器
    resetReconnectCounter();

    // 避免重连定时器被触发
    if (m_reconnectTimer->isActive())
    {
        m_reconnectTimer->stop();
    }

    // 打开WebSocket连接
    qDebug() << "执行重新连接WebSocket:" << m_url.toString();
    m_webSocket->open(m_url);
}

// 添加新方法：记录WebSocket状态
void WebSocketClient::logWebSocketState(const QString &context)
{
    QString stateStr;
    switch (m_webSocket->state())
    {
    case QAbstractSocket::UnconnectedState:
        stateStr = "未连接";
        break;
    case QAbstractSocket::HostLookupState:
        stateStr = "主机查找中";
        break;
    case QAbstractSocket::ConnectingState:
        stateStr = "连接中";
        break;
    case QAbstractSocket::ConnectedState:
        stateStr = "已连接";
        break;
    case QAbstractSocket::BoundState:
        stateStr = "已绑定";
        break;
    case QAbstractSocket::ListeningState:
        stateStr = "监听中";
        break;
    case QAbstractSocket::ClosingState:
        stateStr = "关闭中";
        break;
    default:
        stateStr = "未知状态";
        break;
    }

    qDebug().noquote() << context << " - WebSocket状态:" << stateStr << ", 订阅流数量:" << m_subscribedTradeStreams.size()
                       << ", 上次消息时间:" << m_lastMessageTime.toString("yyyy-MM-dd hh:mm:ss.zzz");
}

void WebSocketClient::connectUserStreamWebSocket(QString url)
{
    connect(m_webSocket, &QWebSocket::connected, []()
            { qDebug() << "[WebSocket] 已连接到 Binance 用户流"; });
    m_webSocket->open(QUrl(url));
    qDebug() << "用户数据流:" << url;
}
