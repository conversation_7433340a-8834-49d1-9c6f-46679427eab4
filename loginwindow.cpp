#include "loginwindow.h"
#include "ui_loginwindow.h"
#include "utils.h"
#include <QMessageBox>
#include <QDebug>
#include <QNetworkAccessManager>
#include <QWebSocket>
#include <QNetworkReply>
#include <QCloseEvent>
#include <QThread>
#include <QCoreApplication>

// 声明外部函数，用于注册线程和强制关闭网络连接
extern void registerAppThread(QThread *thread);
extern void forceCloseAllNetworkConnections();

LoginWindow::LoginWindow(QWidget *parent) : QMainWindow(parent), ui(new Ui::LoginWindow), m_mainWindow(nullptr)
{
    ui->setupUi(this);

    // 设置窗口标题
    setWindowTitle("币安量化交易系统 - 登录");

    // 创建API工作线程
    m_apiThread = new QThread(this);

    // 注册线程到全局线程列表
    registerAppThread(m_apiThread);

    // 创建API工作对象
    m_apiWorker = new ApiWorker();

    // 连接信号和槽
    connect(m_apiWorker, &ApiWorker::apiKeysValidated, this, &LoginWindow::handleApiKeysValidated);

    // 将API工作对象移动到工作线程
    m_apiWorker->moveToThread(m_apiThread);

    // 启动API工作线程
    m_apiThread->start();

    // 在工作线程中初始化ApiManager
    QMetaObject::invokeMethod(m_apiWorker, "initApiManager", Qt::QueuedConnection);

    // 等待线程启动和ApiManager初始化完成
    QThread::msleep(500);

    // 加载保存的API密钥
    loadSavedApiKeys();
}

LoginWindow::~LoginWindow()
{
    // 如果工作线程还在运行，先退出
    if (m_apiThread && m_apiThread->isRunning()) {
        m_apiThread->quit();
        m_apiThread->wait();
    }

    // 删除API工作对象
    if (m_apiWorker) {
        delete m_apiWorker;
        m_apiWorker = nullptr;
    }

    // 删除工作线程
    if (m_apiThread) {
        delete m_apiThread;
        m_apiThread = nullptr;
    }

    delete ui;
}

void LoginWindow::on_loginButton_clicked()
{
    // 获取API密钥
    QString apiKey = ui->apiKeyLineEdit->text().trimmed();
    QString secretKey = ui->secretKeyLineEdit->text().trimmed();

    // 检查API密钥是否为空
    if (apiKey.isEmpty() || secretKey.isEmpty()) {
        QMessageBox::warning(this, "错误", "API Key和Secret Key不能为空！");
        return;
    }

    // 禁用登录按钮，防止重复点击
    ui->loginButton->setEnabled(false);
    ui->loginButton->setText("登录中...");

    // 设置API密钥
    m_apiWorker->setApiKeys(apiKey, secretKey);

    // 验证API密钥
    QMetaObject::invokeMethod(m_apiWorker, "validateApiKeys", Qt::QueuedConnection);
}

void LoginWindow::on_resetButton_clicked()
{
    // 清空输入框
    ui->apiKeyLineEdit->clear();
    ui->secretKeyLineEdit->clear();
    ui->rememberCheckBox->setChecked(false);
}

void LoginWindow::on_exitButton_clicked()
{
    // 首先断开所有信号连接，防止后续操作触发任何回调
    if (m_apiWorker) {
        disconnect(m_apiWorker, nullptr, this, nullptr);
        disconnect(this, nullptr, m_apiWorker, nullptr);
    }

    // 取消所有进行中的网络请求
    QList<QNetworkReply *> allReplies = QApplication::instance()->findChildren<QNetworkReply *>();
    for (QNetworkReply *reply : allReplies) {
        if (reply->isRunning()) {
            reply->abort(); // 中断请求
        }
    }

    // 强制关闭所有网络连接
    forceCloseAllNetworkConnections();

    // 给网络连接一些时间来完成关闭
    QThread::msleep(200);

    // 停止API线程
    if (m_apiThread && m_apiThread->isRunning()) {
        m_apiThread->quit();

        // 等待线程退出
        if (!m_apiThread->wait(3000)) { // 等待3秒
            m_apiThread->terminate();
            m_apiThread->wait(); // 等待线程结束
        }
    }

    // 删除API工作对象
    if (m_apiWorker) {
        delete m_apiWorker;
        m_apiWorker = nullptr;
    }

    // 删除API线程
    if (m_apiThread) {
        delete m_apiThread;
        m_apiThread = nullptr;
    }

    // 删除主窗口（如果存在）
    if (m_mainWindow) {
        m_mainWindow->close();
        delete m_mainWindow;
        m_mainWindow = nullptr;
    }

    // 确保所有事件都被处理
    QCoreApplication::processEvents();

    // 退出应用程序
    QApplication::quit();
}

void LoginWindow::handleApiKeysValidated(bool valid, const QString &errorMessage)
{
    // 恢复登录按钮状态
    ui->loginButton->setEnabled(true);
    ui->loginButton->setText("登录");

    if (valid) {
        // 验证成功
        // 保存API密钥
        saveApiKeys(ui->apiKeyLineEdit->text().trimmed(), ui->secretKeyLineEdit->text().trimmed(), ui->rememberCheckBox->isChecked());

        // 断开与ApiWorker的连接，防止重复触发
        disconnect(m_apiWorker, &ApiWorker::apiKeysValidated, this, &LoginWindow::handleApiKeysValidated);

        // 创建主窗口
        m_mainWindow = new MainWindow();

        // 传递API工作对象给主窗口
        m_mainWindow->setApiWorker(m_apiWorker);

        // 显示主窗口
        m_mainWindow->show();

        // 隐藏登录窗口
        hide();
    } else {
        // 验证失败
        QMessageBox::critical(this, "错误", "API密钥验证失败：" + errorMessage);
    }
}

void LoginWindow::loadSavedApiKeys()
{
    // 加载保存的API密钥
    QString apiKey, secretKey;
    bool remember = Utils::loadApiKeys(apiKey, secretKey);

    if (remember) {
        // 填充API密钥输入框
        ui->apiKeyLineEdit->setText(apiKey);
        ui->secretKeyLineEdit->setText(secretKey);
        ui->rememberCheckBox->setChecked(true);
    }
}

void LoginWindow::saveApiKeys(const QString &apiKey, const QString &secretKey, bool remember)
{
    // 保存API密钥
    Utils::saveApiKeys(apiKey, secretKey, remember);
}

void LoginWindow::closeEvent(QCloseEvent *event)
{
    // 停止API线程
    if (m_apiThread) {
        if (m_apiThread->isRunning()) {
            m_apiThread->quit();

            // 等待线程退出
            if (!m_apiThread->wait(3000)) { // 等待3秒
                m_apiThread->terminate();
                m_apiThread->wait(); // 等待线程结束
            }
        }
    }

    // 删除API工作对象
    if (m_apiWorker) {
        delete m_apiWorker;
        m_apiWorker = nullptr;
    }

    // 删除API线程
    if (m_apiThread) {
        delete m_apiThread;
        m_apiThread = nullptr;
    }

    // 删除主窗口
    if (m_mainWindow) {
        delete m_mainWindow;
        m_mainWindow = nullptr;
    }

    // 继续关闭事件处理
    event->accept();
}
