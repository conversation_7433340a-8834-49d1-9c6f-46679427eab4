# 设置OpenSSL库的PowerShell脚本

# 创建ssl目录
$sslDir = Join-Path -Path $PSScriptRoot -ChildPath "ssl"
if (-not (Test-Path -Path $sslDir)) {
    New-Item -Path $sslDir -ItemType Directory
}

# 下载OpenSSL DLL文件
$openSslUrl = "https://slproweb.com/download/Win64OpenSSL_Light-3_1_4.exe"
$installerPath = Join-Path -Path $env:TEMP -ChildPath "Win64OpenSSL_Light.exe"

Write-Host "正在下载OpenSSL安装程序..."
Invoke-WebRequest -Uri $openSslUrl -OutFile $installerPath

# 安装OpenSSL
Write-Host "正在安装OpenSSL..."
Start-Process -FilePath $installerPath -ArgumentList "/silent /verysilent /sp- /suppressmsgboxes" -Wait

# 复制DLL文件到ssl目录
$openSslInstallDir = "C:\Program Files\OpenSSL-Win64"
$libEay32Path = Join-Path -Path $openSslInstallDir -ChildPath "bin\libcrypto-3-x64.dll"
$ssleay32Path = Join-Path -Path $openSslInstallDir -ChildPath "bin\libssl-3-x64.dll"

if (Test-Path -Path $libEay32Path) {
    Copy-Item -Path $libEay32Path -Destination $sslDir
    Write-Host "已复制 libcrypto-3-x64.dll 到 ssl 目录"
} else {
    Write-Host "找不到 libcrypto-3-x64.dll"
}

if (Test-Path -Path $ssleay32Path) {
    Copy-Item -Path $ssleay32Path -Destination $sslDir
    Write-Host "已复制 libssl-3-x64.dll 到 ssl 目录"
} else {
    Write-Host "找不到 libssl-3-x64.dll"
}

# 复制DLL文件到构建目录
$buildDir = Join-Path -Path $PSScriptRoot -ChildPath "build\Debug"
if (-not (Test-Path -Path $buildDir)) {
    New-Item -Path $buildDir -ItemType Directory -Force
}

if (Test-Path -Path $libEay32Path) {
    Copy-Item -Path $libEay32Path -Destination $buildDir
    Write-Host "已复制 libcrypto-3-x64.dll 到构建目录"
}

if (Test-Path -Path $ssleay32Path) {
    Copy-Item -Path $ssleay32Path -Destination $buildDir
    Write-Host "已复制 libssl-3-x64.dll 到构建目录"
}

Write-Host "OpenSSL设置完成!" 