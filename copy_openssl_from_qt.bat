@echo off
echo 从Qt安装目录复制OpenSSL库文件

REM 设置Qt安装目录，请根据实际情况修改
set QT_DIR=D:\StudyAndWork\Qt\5.15.2\msvc2019_64

REM 创建ssl目录
if not exist ssl mkdir ssl

REM 复制OpenSSL DLL文件
echo 正在复制OpenSSL DLL文件...
copy "%QT_DIR%\bin\libssl-1_1-x64.dll" ssl\
copy "%QT_DIR%\bin\libcrypto-1_1-x64.dll" ssl\

REM 复制到构建目录
if exist build\Debug (
    echo 正在复制到Debug构建目录...
    copy "%QT_DIR%\bin\libssl-1_1-x64.dll" build\Debug\
    copy "%QT_DIR%\bin\libcrypto-1_1-x64.dll" build\Debug\
)

if exist build\Release (
    echo 正在复制到Release构建目录...
    copy "%QT_DIR%\bin\libssl-1_1-x64.dll" build\Release\
    copy "%QT_DIR%\bin\libcrypto-1_1-x64.dll" build\Release\
)

echo 完成！
pause 