#ifndef APIMANAGER_H
#define APIMANAGER_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QNetworkRequest>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QUrlQuery>
#include <QTimer>
#include <QThread>
#include <QWebSocket>
#include <QMap>
#include <QVariant>
#include "utils.h"
#include "websocketclient.h"

// API请求类型枚举
enum class RequestType
{
    VALIDATE_API_KEYS,
    GET_ACCOUNT_INFO,
    GET_TICKER_PRICE,
    GET_LATEST_PRICE,
    PLACE_ORDER,
    CANCEL_ORDER,
    GET_ORDER_STATUS,
    GET_OPEN_ORDERS,
    CREATE_LISTEN_KEY,
    KEEP_ALIVE_LISTEN_KEY,
    CLOSE_LISTEN_KEY,
    GET_ORDER_BOOK_DEPTH // 新增：获取订单簿深度数据
};

// API请求结果结构体
struct ApiResponse
{
    bool success;
    QJsonDocument data;
    QString errorMessage;
    int errorCode;
    RequestType requestType;
};

// 前向声明
class ApiWorker;

// API管理器类
class ApiManager : public QObject
{
    Q_OBJECT

public:
    explicit ApiManager(QObject *parent = nullptr);
    ~ApiManager();

    // 设置API密钥
    void setApiKeys(const QString &apiKey, const QString &secretKey);

    // 获取WebSocketClient实例
    WebSocketClient *getWebSocketClient() const { return m_webSocketClient; }

    // 获取API权重信息
    QJsonObject getApiWeightInfo() const;

    // 获取当前权重
    int getCurrentWeight() const { return m_currentWeight; }

    // 获取权重限制
    int getWeightLimit() const { return m_weightLimit; }

    // 获取订单数量
    int getOrderCount() const { return m_orderCount; }

    // 获取详细API权重信息
    QJsonObject getDetailedApiWeightInfo() const;

    // 获取5分钟权重
    int getCurrentWeight5m() const { return m_currentWeight5m; }

    // 获取5分钟权重限制
    int getWeightLimit5m() const { return m_weightLimit5m; }

    // 获取1小时权重
    int getCurrentWeight1h() const { return m_currentWeight1h; }

    // 获取1小时权重限制
    int getWeightLimit1h() const { return m_weightLimit1h; }

    // 获取1天权重
    int getCurrentWeight1d() const { return m_currentWeight1d; }

    // 获取1天权重限制
    int getWeightLimit1d() const { return m_weightLimit1d; }

    // 设置API权重输出阈值
    void setApiWeightOutputThreshold(int threshold) { m_apiWeightOutputThreshold = threshold; }

public slots:
    // 验证API密钥
    void validateApiKeys();

    // 获取账户信息
    void getAccountInfo();

    // 获取最佳挂单价格 - 通过REST API (已存在的方法，后续将逐步被WebSocket替代)
    void getBestOrderBookTicker(const QString &symbol);

    // 订阅BookTicker流 - 通过WebSocket
    void subscribeBookTickerStream(const QString &symbol);

    // 取消订阅BookTicker流 - 通过WebSocket
    void unsubscribeBookTickerStream(const QString &symbol);

    // 获取最新成交价格
    void getLatestPrice(const QString &symbol);

    // 下单
    void placeOrder(const QString &symbol, const QString &side, const QString &type, const QString &quantity, const QString &price = "",
                    const QString &relatedOrderId = QString());

    // 取消订单
    void cancelOrder(const QString &symbol, const QString &orderId);

    // 获取订单状态
    void getOrderStatus(const QString &symbol, const QString &orderId);

    // 获取所有挂单
    void getOpenOrders(const QString &symbol);

    // 启动WebSocket连接，接收实时订单更新
    void startUserDataStream();

    // 停止WebSocket连接
    void stopUserDataStream();

    // 发送API请求
    void sendRequest(const QUrl &url, const QUrlQuery &query, RequestType requestType, const QByteArray &method = "GET", const QByteArray &postData = "",
                     bool needSignature = true);

    // 创建listenKey，用于WebSocket连接
    void createListenKey();

    // 延长listenKey有效期
    void keepAliveListenKey();

    // 获取订单簿深度数据
    void getOrderBookDepth(const QString &symbol, int limit = 20);

signals:
    // API请求完成信号
    void requestFinished(const ApiResponse &response);

    // 验证API密钥结果信号
    void apiKeysValidated(bool valid, const QString &errorMessage = "");

    // 账户信息更新信号
    void accountInfoUpdated(const QJsonObject &accountInfo);
    void accountInfoUpdateForWebsocket(double usdc, double usdc_locked, double usdt, double usdt_locked, QString updateTime);
    // 最佳挂单价格更新信号
    void bestOrderBookTickerUpdated(const QJsonObject &ticker);

    // 最新成交价格更新信号
    void latestPriceUpdated(const QJsonObject &priceInfo);

    // 下单结果信号
    void orderPlaced(bool success, const QJsonObject &orderInfo, const QString &errorMessage = "");

    // 取消订单结果信号
    void orderCancelled(bool success, const QJsonObject &orderInfo, const QString &errorMessage = "");

    // 订单状态更新信号
    void orderStatusUpdated(QString orderId, QString symbol, QString side, QString status, qint64 updateTime);

    // 所有挂单信息信号
    void openOrdersReceived(bool success, const QJsonArray &orders, const QString &errorMessage = "");

    // 实时订单更新信号
    void orderUpdateReceived(const QJsonObject &orderUpdate);

    // 订单簿深度数据更新信号
    void orderBookDepthUpdated(const QJsonObject &depthData);

    void sg_sendMs(quint64 ms);

private slots:
    // 处理网络请求完成
    void handleNetworkReply(QNetworkReply *reply);

    // WebSocket相关槽
    void onWebSocketConnected();
    void onWebSocketDisconnected();
    void onWebSocketError(QAbstractSocket::SocketError error);
    // 处理WebSocket接收到的文本消息
    void onWebSocketTextMessageReceived(const QString &message);
    void onWebSocketReconnectAttempt(int attempt, int maxAttempts);
    void onWebSocketReconnected();
    void onWebSocketHeartbeatSent();
    void onWebSocketHeartbeatReceived();
    void onWebSocketBookTickerReceived(const QJsonObject &bookTickerData);

    // 保活定时器超时槽
    void onKeepAliveTimerTimeout();

private:
    // API密钥
    QString m_apiKey;
    QString m_secretKey;

    // API权重信息
    int m_currentWeight = 0;       // 当前已使用的权重 (1分钟)
    int m_weightLimit = 1200;      // 默认权重限制为1200/分钟
    int m_currentWeight5m = 0;     // 5分钟权重
    int m_weightLimit5m = 6000;    // 默认5分钟权重限制
    int m_currentWeight1h = 0;     // 1小时权重
    int m_weightLimit1h = 60000;   // 默认1小时权重限制
    int m_currentWeight1d = 0;     // 1天权重
    int m_weightLimit1d = 1000000; // 默认1天权重限制
    int m_orderCount = 0;          // 订单计数

    // API权重输出阈值（默认300）
    int m_apiWeightOutputThreshold = 300;

    // 网络管理器
    QNetworkAccessManager *m_networkManager;

    // WebSocket客户端
    WebSocketClient *m_webSocketClient;

    // listenKey，用于WebSocket连接
    QString m_listenKey;

    // 保活定时器
    QTimer *m_keepAliveTimer;

    // WebSocket连接状态
    bool m_isWebSocketConnected;

    // 请求上下文，用于存储请求相关的临时数据
    QMap<QString, QVariant> m_requestContext;

    // 生成签名
    QByteArray generateSignature(const QUrlQuery &query);

    // 创建带签名的URL查询
    QUrlQuery createSignedQuery(const QUrlQuery &query);

    // 处理响应
    void handleResponse(QNetworkReply *reply, ApiResponse &response);

    // 处理WebSocket消息
    void handleWebSocketMessage(const QString &message);

    // 连接用户数据WebSocket
    void connectUserDataWebSocket();

    // API URL常量
    const QString API_BASE_URL = "https://api.binance.com";
    const QString API_KEY_HEADER = "X-MBX-APIKEY";
    // 修正WebSocket URL，去掉末尾的斜杠
    const QString WS_BASE_URL = "wss://stream.binance.com:9443/ws";
};

#endif // APIMANAGER_H
