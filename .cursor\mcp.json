{"mcpServers": {"filesystem": {"command": "cmd", "args": ["/c", "node", "D:\\StudyAndWork\\Nodejs\\node_global\\node_modules\\@modelcontextprotocol\\server-filesystem\\dist\\index.js", "C:\\Users\\<USER>\\Desktop", "D:\\QT\\GlobalStruct", "D:\\StudyAndWork\\HarmonyMassMession\\DataAnalysisHZH", "D:\\StudyAndWork\\QtProjects\\CustomControls\\LxChart", "D:\\StudyAndWork\\QtProjects\\LxQWT", "D:\\QT\\GlobalStruct", "D:\\StudyAndWork\\HarmonyMassMession", "D:\\StudyAndWork\\QtProjects\\HZHDataReader", "D:\\StudyAndWork\\QtProjects\\HZHDataAnalysis", "D:\\StudyAndWork\\QtProjects\\Binance\\build\\Binance"], "enabled": true}}}