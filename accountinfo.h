#ifndef ACCOUNTINFO_H
#define ACCOUNTINFO_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include <QString>
#include <QMap>

// 资产信息结构体
struct AssetInfo
{
    QString asset;  // 资产名称
    QString free;   // 可用余额
    QString locked; // 锁定余额

    // 获取总余额（可用+锁定）
    QString total() const
    {
        bool ok1, ok2;
        double freeValue = free.toDouble(&ok1);
        double lockedValue = locked.toDouble(&ok2);

        if (ok1 && ok2) {
            return QString::number(freeValue + lockedValue, 'f', 8);
        }

        return "0";
    }
};

// 账户信息类
class AccountInfo : public QObject
{
    Q_OBJECT

public:
    explicit AccountInfo(QObject *parent = nullptr);

    // 更新账户信息
    void updateAccountInfo(const QJsonObject &accountInfo);
    void updateAccountInfoForWebsocket(double usdc, double usdc_locked, double usdt, double usdt_locked);

    // 获取资产信息
    AssetInfo getAssetInfo(const QString &asset) const;

    // 获取所有资产信息
    QMap<QString, AssetInfo> getAllAssets() const;

    // 获取USDT余额
    QString getUSDTBalance() const;

    // 获取USDC余额
    QString getUSDCBalance() const;

    // 获取USDT可用余额
    QString getUSDTFreeBalance() const;

    // 获取USDC可用余额
    QString getUSDCFreeBalance() const;

    // 获取USDT锁定余额
    QString getUSDTLockedBalance() const;

    // 获取USDC锁定余额
    QString getUSDCLockedBalance() const;

signals:
    // 账户信息更新信号
    void accountInfoUpdated();

    // USDT余额更新信号
    void usdtBalanceUpdated(const QString &balance);

    // USDC余额更新信号
    void usdcBalanceUpdated(const QString &balance);

    // USDT可用余额更新信号
    void usdtFreeBalanceUpdated(const QString &balance);

    // USDC可用余额更新信号
    void usdcFreeBalanceUpdated(const QString &balance);

    // USDT锁定余额更新信号
    void usdtLockedBalanceUpdated(const QString &balance);

    // USDC锁定余额更新信号
    void usdcLockedBalanceUpdated(const QString &balance);

private:
    // 账户信息
    QJsonObject m_accountInfo;

    // 资产信息映射表
    QMap<QString, AssetInfo> m_assets;

    // USDT总余额
    QString m_usdtBalance;

    // USDC总余额
    QString m_usdcBalance;

    // USDT可用余额
    QString m_usdtFreeBalance;

    // USDC可用余额
    QString m_usdcFreeBalance;

    // USDT锁定余额
    QString m_usdtLockedBalance;

    // USDC锁定余额
    QString m_usdcLockedBalance;
};

#endif // ACCOUNTINFO_H
