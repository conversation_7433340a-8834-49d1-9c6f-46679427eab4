#ifndef WEBSOCKETCLIENT_H
#define WEBSOCKETCLIENT_H

#include <QObject>
#include <QWebSocket>
#include <QJsonObject>
#include <QJsonDocument>
#include <QTimer>
#include <QSet>
#include <QStringList>

class WebSocketClient : public QObject
{
    Q_OBJECT

public:
    explicit WebSocketClient(QObject *parent = nullptr);
    ~WebSocketClient();

    // 打开WebSocket连接
    void open(const QUrl &url);

    // 关闭WebSocket连接
    void close();

    // 获取WebSocket状态
    QAbstractSocket::SocketState state() const;

    // 检查WebSocket是否有效
    bool isValid() const;

    // 获取错误字符串
    QString errorString() const;

    // 获取请求URL
    QUrl requestUrl() const;

    // 发送文本消息
    void sendTextMessage(const QString &message);

    // 终止连接
    void abort();

    // 设置自动重连
    void setAutoReconnect(bool autoReconnect);

    // 设置心跳间隔（毫秒）
    void setHeartbeatInterval(int interval);

    // 设置重连间隔（毫秒）
    void setReconnectInterval(int interval);

    // 设置重连尝试次数上限
    void setMaxReconnectAttempts(int maxAttempts);

    // 设置无响应超时时间（毫秒）
    void setInactivityTimeout(int timeout);

    // 手动发送心跳包
    void sendHeartbeat();

    // 获取当前已订阅的交易流
    QStringList getSubscribedTradeStreams() const;

    // 连接订阅用户信息流
    void connectUserStreamWebSocket(QString url);
signals:
    // 连接成功信号
    void connected();

    // 断开连接信号
    void disconnected();

    // 接收到文本消息信号
    void textMessageReceived(const QString &message);

    // 连接错误信号
    void error(QAbstractSocket::SocketError error);

    // 订单更新信号
    void orderUpdateReceived(const QJsonObject &orderUpdate);

    // 连接状态更新信号
    void connectionStateChanged(QAbstractSocket::SocketState state);

    // 重连尝试信号
    void reconnectAttempt(int attempt, int maxAttempts);

    // 重连成功信号
    void reconnected();

    // 心跳发送信号
    void heartbeatSent();

    // 交易数据更新信号
    void tradeDataReceived(const QJsonObject &tradeData);

    // 订阅状态变化信号
    void subscriptionChanged(const QString &symbol, bool isSubscribed);

    // 新增心跳响应接收信号
    void heartbeatReceived();

    // bookTicker数据更新信号
    void bookTickerDataReceived(const QJsonObject &bookTickerData);

    void sg_sendMs(quint64 ms);

public slots:
    // 订阅交易流槽
    void subscribeTradeStream(const QString &symbol);

    // 取消订阅交易流槽
    void unsubscribeTradeStream(const QString &symbol);

    // 订阅bookTicker流槽
    void subscribeBookTickerStream(const QString &symbol);

    // 取消订阅bookTicker流槽
    void unsubscribeBookTickerStream(const QString &symbol);

    // 重新连接WebSocket槽
    void reconnectWebSocket();

private slots:
    // 处理连接成功
    void onConnected();

    // 处理断开连接
    void onDisconnected();

    // 处理接收到的文本消息
    void onTextMessageReceived(const QString &message);

    // 处理连接错误
    void onError(QAbstractSocket::SocketError error);

    // 处理连接状态变化
    void onStateChanged(QAbstractSocket::SocketState state);

    // 处理心跳定时器
    void onHeartbeatTimer();

    // 处理重连定时器
    void onReconnectTimer();

private:
    // WebSocket对象
    QWebSocket *m_webSocket;

    // 心跳定时器
    QTimer *m_heartbeatTimer;

    // 重连定时器
    QTimer *m_reconnectTimer;

    // 原始连接URL
    QUrl m_url;

    // 是否启用自动重连
    bool m_autoReconnect;

    // 心跳间隔（毫秒）
    int m_heartbeatInterval;

    // 重连间隔（毫秒）
    int m_reconnectInterval;

    // 无响应超时时间（毫秒）
    int m_inactivityTimeout;

    // 最大重连尝试次数
    int m_maxReconnectAttempts;

    // 当前重连尝试次数
    int m_reconnectAttempts;

    // 是否正在进行重连
    bool m_isReconnecting;

    // 是否正在连接中（保护锁，防止并发连接操作）
    bool m_isConnecting;

    // 上次收到消息的时间
    QDateTime m_lastMessageTime;

    // 存储当前订阅的交易流
    QSet<QString> m_subscribedTradeStreams;

    // 存储当前订阅的bookTicker流
    QSet<QString> m_subscribedBookTickerStreams;

    // 更新WebSocket连接的订阅
    void updateWebSocketSubscriptions();

    // 重新建立连接
    void reconnect();

    // 启动心跳机制
    void startHeartbeat();

    // 停止心跳机制
    void stopHeartbeat();

    // 重置重连计数器
    void resetReconnectCounter();

    // 解析订单更新
    void parseOrderUpdate(const QJsonObject &data);

    // 解析交易数据
    void parseTradeData(const QJsonObject &data);

    // 解析bookTicker数据
    void parseBookTickerData(const QJsonObject &data);

    // 记录WebSocket状态
    void logWebSocketState(const QString &context);

    // 计算指数退避延迟
    int calculateBackoffDelay();
};

#endif // WEBSOCKETCLIENT_H
