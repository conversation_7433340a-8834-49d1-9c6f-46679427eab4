#ifndef CURRENTORDER_H
#define CURRENTORDER_H

#include <QObject>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonArray>
#include <QVariant>
#include <QDebug>
#include <QSet>
#include "apiworker.h"

// 前向声明
class ApiWorker;
class WebSocketClient;

// 订单状态枚举
enum class CurrentOrderStatus
{
    INITIAL,              // 初始状态
    BUY_PENDING,          // 买入挂单中
    ARBITRAGING,          // 套利中
    STOP_LOSS_FIRST_DROP, // 止损中 - 第一次价格下降
    STOP_LOSS_SECOND_DROP // 止损中 - 第二次价格下降
};

// 为了向后兼容，添加常量定义
class CurrentOrder : public QObject
{
    Q_OBJECT

public:
    // 订单状态常量，用于向后兼容
    static const CurrentOrderStatus INITIAL = CurrentOrderStatus::INITIAL;
    static const CurrentOrderStatus BUY_PENDING = CurrentOrderStatus::BUY_PENDING;
    static const CurrentOrderStatus ARBITRAGING = CurrentOrderStatus::ARBITRAGING;
    static const CurrentOrderStatus STOP_LOSS_FIRST_DROP = CurrentOrderStatus::STOP_LOSS_FIRST_DROP;
    static const CurrentOrderStatus STOP_LOSS_SECOND_DROP = CurrentOrderStatus::STOP_LOSS_SECOND_DROP;

    // 获取单例实例
    static CurrentOrder *getInstance();
    // 释放单例实例
    static void releaseInstance();

    // 获取买入订单ID
    QString getBuyOrderId() const { return m_buyOrderId; }

    // 获取卖出订单ID
    QString getSellOrderId() const { return m_sellOrderId; }

    // 设置卖出订单ID
    void setSellOrderId(const QString &orderId) { m_sellOrderId = orderId; }

    // 获取订单状态
    CurrentOrderStatus getStatus() const { return m_status; }

    // 向后兼容的订单状态获取函数
    CurrentOrderStatus getOrderStatus() const { return m_status; }

    // 获取状态文本
    QString getStatusText() const;

    // 获取状态颜色
    QString getStatusColor() const;

    // 获取买入价格
    double getBuyPrice() const { return m_buyPrice; }

    // 向后兼容的买入价格获取函数
    double getBuyOrderPrice() const { return m_buyPrice; }

    // 获取卖出价格
    double getSellPrice() const { return m_sellPrice; }

    // 获取原始套利卖单价格
    double getOriginalSellPrice() const { return m_originalSellPrice; }

    // 设置原始套利卖单价格
    void setOriginalSellPrice(double price) { m_originalSellPrice = price; }

    // 获取买入数量
    QString getBuyQuantity() const { return m_buyQuantity; }

    // 获取卖出数量
    QString getSellQuantity() const { return m_sellQuantity; }

    // 获取买入订单时间
    QDateTime getBuyOrderTime() const { return m_buyOrderTime; }

    // 获取卖出订单时间
    QDateTime getSellOrderTime() const { return m_sellOrderTime; }

    // 获取卖单成交时间
    QDateTime getSellOrderFillTime() const { return m_sellOrderFillTime; }

    // 获取待处理的强制止损订单的clientOrderId
    QString getPendingStopLossClientOrderId() const { return m_pendingStopLossClientOrderId; }

    // 设置待处理的强制止损订单的clientOrderId
    void setPendingStopLossClientOrderId(const QString &clientOrderId)
    {
        m_pendingStopLossClientOrderId = clientOrderId;
    }

    // 清除待处理的强制止损订单的clientOrderId
    void clearPendingStopLossClientOrderId()
    {
        m_pendingStopLossClientOrderId.clear();
    }

    // 设置买入订单信息
    void setBuyOrderInfo(const QJsonObject &orderInfo);

    // 设置卖出订单信息
    void setSellOrderInfo(const QJsonObject &orderInfo);

    // 设置卖单成交时间
    void setSellOrderFillTime(const QDateTime &time) { m_sellOrderFillTime = time; }

    // 取消订单
    bool cancelCurrentOrder();

    // 恢复初始状态
    void resetToInitial();

    // 判断是否处于初始状态
    bool isInitialState() const { return m_status == CurrentOrderStatus::INITIAL; }

    // 计算套利利润
    double calculateProfit() const;

    // 获取交易对
    QString getSymbol() const { return m_symbol; }

    // 设置API工作对象
    void setApiWorker(ApiWorker *apiWorker) { m_apiWorker = apiWorker; }

    // 获取币安工作对象
    ApiWorker *getApiWorker() const { return m_apiWorker; }

    // 同步订单状态与币安平台真实订单状态
    void syncOrderStatusWithBinance(const QJsonArray &openOrders);

    // 设置订单状态（向后兼容函数）
    void setOrderStatus(CurrentOrderStatus status) { setStatus(status); }

    // 设置WebSocket客户端
    void setWebSocketClient(WebSocketClient *webSocketClient);

    // 订阅特定价格的交易数据
    bool subscribe(const QString &symbol, double price);

    // 取消订阅
    bool unsubscribe();

    // 获取当前订阅的交易对
    QString getSubscribedSymbol() const { return m_subscribedSymbol; }

    // 获取当前订阅的价格
    double getSubscribedPrice() const { return m_subscribedPrice; }

    // 是否已订阅
    bool isSubscribed() const { return m_isSubscribed; }

    // 获取WebSocketClient
    WebSocketClient *getWebSocketClient() const { return m_webSocketClient; }

signals:
    // 状态变化信号
    void statusChanged(CurrentOrderStatus status);

    // 买单成交信号
    void buyOrderFilled(const QString &orderId, double price, const QString &quantity);

    // 卖单成交信号
    void sellOrderFilled(const QString &orderId, double price, const QString &quantity);

    // 套利完成信号
    void arbitrageCompleted(const QString &buyOrderId, const QString &sellOrderId, double buyPrice, double sellPrice, double amount, double profit, bool isStopLoss = false);

    // 订单取消信号
    void orderCancelled(const QString &orderId);

    // 订阅状态变化信号
    void subscriptionStatusChanged(bool isSubscribed);

    // 请求订阅交易流信号
    void requestSubscribeTradeStream(const QString &symbol);

    // 请求取消订阅交易流信号
    void requestUnsubscribeTradeStream(const QString &symbol);

    // 请求重新连接WebSocket信号
    void requestReconnectWebSocket();

public slots:
    // 处理买单成交
    void handleBuyOrderFilled(bool success, QString orderStatus, QString orderId, const QString &errorMessage);

    // 处理卖单成交
    void handleSellOrderFilled(bool success, QString orderStatus, QString orderId, const QString &errorMessage);

    // 处理订单取消结果
    void handleOrderCancelled(bool success, QString orderId, const QString &errorMessage);

    // 处理开放订单列表
    void handleOpenOrdersReceived(bool success, const QJsonArray &orders, const QString &errorMessage);

    // 处理WebSocket订阅状态变化
    void handleWebSocketSubscriptionChanged(const QString &symbol, bool isSubscribed);

    // 处理WebSocket连接恢复
    void onWebSocketConnected();

private slots:

private:
    // 私有构造函数，防止外部创建实例
    explicit CurrentOrder(QObject *parent = nullptr);
    // 私有析构函数，防止外部删除实例
    ~CurrentOrder();

    // 禁止拷贝构造和赋值操作
    CurrentOrder(const CurrentOrder &) = delete;
    CurrentOrder &operator=(const CurrentOrder &) = delete;

    // 设置状态
    void setStatus(CurrentOrderStatus status);

    // 格式化订单ID，确保使用标准整数格式而非科学计数法
    QString formatOrderId(const QVariant &orderIdVariant) const;

    // 比较两个订单ID是否相同（考虑格式差异）
    bool isSameOrderId(const QString &id1, const QString &id2) const;

    // 单例实例
    static CurrentOrder *m_instance;

    // 币安API工作对象
    ApiWorker *m_apiWorker;

    // WebSocket客户端
    WebSocketClient *m_webSocketClient;

public:
    // 当前状态
    CurrentOrderStatus m_status;

    // 买入订单ID
    QString m_buyOrderId;

    // 卖出订单ID
    QString m_sellOrderId;

    // 交易对
    QString m_symbol;

    // 买入价格
    double m_buyPrice;

    // 卖出价格
    double m_sellPrice;

    // 原始套利卖单价格（用于价格回升检测）
    double m_originalSellPrice;

    // 买入数量
    QString m_buyQuantity;

    // 卖出数量
    QString m_sellQuantity;

    // 买入订单时间
    QDateTime m_buyOrderTime;

    // 卖出订单时间
    QDateTime m_sellOrderTime;

    // 卖单成交时间
    QDateTime m_sellOrderFillTime;

    // 订单金额（买入总额）
    double m_orderAmount;

    // 待处理的强制止损订单的clientOrderId
    QString m_pendingStopLossClientOrderId;

private:
    // 当前订阅的交易对
    QString m_subscribedSymbol;

    // 当前订阅的价格
    double m_subscribedPrice;

    // 当前订阅的精度（允许的价格偏差）
    double m_pricePrecision;

    // 是否已订阅
    bool m_isSubscribed;

    // 已处理过的订单事件集合
    QSet<QString> m_processedEvents;

    // 检查事件是否已处理过
    bool isEventProcessed(const QString &eventKey);

    // 在适当的时候清理集合
    void cleanupProcessedEvents();
};

#endif // CURRENTORDER_H
