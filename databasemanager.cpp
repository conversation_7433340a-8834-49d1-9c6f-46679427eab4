#include "databasemanager.h"

const QString DatabaseManager::DATABASE_NAME = "history.db";
const QString DatabaseManager::CONNECTION_NAME = "HistoryConnection";

DatabaseManager::DatabaseManager(QObject *parent)
    : QObject(parent), m_useSqlite(false)
{
    // 设置数据库文件路径
    QString dataDir = QDir::currentPath() + "/data";
    QDir dir;
    if (!dir.exists(dataDir))
    {
        dir.mkpath(dataDir);
    }
    m_databasePath = dataDir + "/" + DATABASE_NAME;
    m_jsonFilePath = dataDir + "/history.json";
}

DatabaseManager::~DatabaseManager()
{
    if (m_database.isOpen())
    {
        m_database.close();
    }
    QSqlDatabase::removeDatabase(CONNECTION_NAME);
}

bool DatabaseManager::initDatabase()
{
    // 检查可用的数据库驱动
    QStringList drivers = QSqlDatabase::drivers();
    qDebug() << "可用的数据库驱动:" << drivers;

    if (!drivers.contains("QSQLITE"))
    {
        qDebug() << "[错误] QSQLITE驱动不可用";
        qDebug() << "请检查Qt安装是否包含SQL模块";
        return false;
    }

    // 创建数据库连接
    m_database = QSqlDatabase::addDatabase("QSQLITE", CONNECTION_NAME);
    m_database.setDatabaseName(m_databasePath);

    if (!m_database.open())
    {
        qDebug() << "数据库连接失败:" << m_database.lastError().text();
        return false;
    }

    qDebug() << "数据库连接成功:" << m_databasePath;

    // 创建表
    return createTables();
}

bool DatabaseManager::createTables()
{
    QSqlQuery query(m_database);

    QString createTableSql = R"(
        CREATE TABLE IF NOT EXISTS history_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id TEXT NOT NULL,
            start_time TEXT NOT NULL,
            arbitrage_start_time TEXT,
            arbitrage_end_time TEXT,
            buy_price REAL,
            sell_price REAL,
            amount REAL,
            profit REAL,
            status TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    )";

    if (!query.exec(createTableSql))
    {
        qDebug() << "创建表失败:" << query.lastError().text();
        return false;
    }

    qDebug() << "数据库表创建成功";
    return true;
}

bool DatabaseManager::insertHistoryRecord(const QString &orderId,
                                          const QDateTime &startTime,
                                          const QDateTime &arbitrageStartTime,
                                          const QDateTime &arbitrageEndTime,
                                          double buyPrice, double sellPrice,
                                          double amount, double profit,
                                          const QString &status)
{
    if (!m_database.isOpen())
    {
        qDebug() << "数据库未连接，无法插入记录";
        return false;
    }

    QSqlQuery query(m_database);

    QString insertSql = R"(
        INSERT INTO history_records 
        (order_id, start_time, arbitrage_start_time, arbitrage_end_time, 
         buy_price, sell_price, amount, profit, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    )";

    query.prepare(insertSql);
    query.addBindValue(orderId);
    query.addBindValue(startTime.toString(Qt::ISODate));
    query.addBindValue(arbitrageStartTime.toString(Qt::ISODate));
    query.addBindValue(arbitrageEndTime.toString(Qt::ISODate));
    query.addBindValue(buyPrice);
    query.addBindValue(sellPrice);
    query.addBindValue(amount);
    query.addBindValue(profit);
    query.addBindValue(status);

    if (!query.exec())
    {
        qDebug() << "插入历史记录失败:" << query.lastError().text();
        return false;
    }

    qDebug() << "历史记录插入成功，订单ID:" << orderId;
    return true;
}

QList<QVariantMap> DatabaseManager::queryHistoryRecords(const QueryFilter &filter)
{
    QList<QVariantMap> results;

    if (!m_database.isOpen())
    {
        qDebug() << "数据库未连接，无法查询记录";
        return results;
    }

    QVariantMap bindValues;
    QString sql = buildQuerySql(filter, bindValues);

    QSqlQuery query(m_database);
    query.prepare(sql);

    // 绑定参数
    for (auto it = bindValues.begin(); it != bindValues.end(); ++it)
    {
        query.bindValue(it.key(), it.value());
    }

    if (!query.exec())
    {
        qDebug() << "查询历史记录失败:" << query.lastError().text();
        return results;
    }

    while (query.next())
    {
        QVariantMap record;
        record["id"] = query.value("id");
        record["order_id"] = query.value("order_id");
        record["start_time"] = query.value("start_time");
        record["arbitrage_start_time"] = query.value("arbitrage_start_time");
        record["arbitrage_end_time"] = query.value("arbitrage_end_time");
        record["buy_price"] = query.value("buy_price");
        record["sell_price"] = query.value("sell_price");
        record["amount"] = query.value("amount");
        record["profit"] = query.value("profit");
        record["status"] = query.value("status");
        record["created_at"] = query.value("created_at");

        results.append(record);
    }

    qDebug() << "查询到" << results.size() << "条历史记录";
    return results;
}

int DatabaseManager::getTotalRecordsCount()
{
    if (!m_database.isOpen())
    {
        return 0;
    }

    QSqlQuery query(m_database);
    if (query.exec("SELECT COUNT(*) FROM history_records"))
    {
        if (query.next())
        {
            return query.value(0).toInt();
        }
    }

    return 0;
}

bool DatabaseManager::clearAllRecords()
{
    if (!m_database.isOpen())
    {
        return false;
    }

    QSqlQuery query(m_database);
    if (!query.exec("DELETE FROM history_records"))
    {
        qDebug() << "清空记录失败:" << query.lastError().text();
        return false;
    }

    qDebug() << "所有历史记录已清空";
    return true;
}

bool DatabaseManager::isConnected() const
{
    return m_database.isOpen();
}

QString DatabaseManager::buildQuerySql(const QueryFilter &filter, QVariantMap &bindValues)
{
    QString sql = "SELECT * FROM history_records WHERE 1=1";

    // 开始时间筛选
    if (filter.useStartTime)
    {
        if (filter.startTimeTo.isValid())
        {
            sql += " AND start_time BETWEEN :startTimeFrom AND :startTimeTo";
            bindValues[":startTimeFrom"] = filter.startTimeFrom.toString(Qt::ISODate);
            bindValues[":startTimeTo"] = filter.startTimeTo.toString(Qt::ISODate);
        }
        else
        {
            sql += " AND start_time >= :startTimeFrom";
            bindValues[":startTimeFrom"] = filter.startTimeFrom.toString(Qt::ISODate);
        }
    }

    // 结束时间筛选
    if (filter.useEndTime)
    {
        if (filter.endTimeTo.isValid())
        {
            sql += " AND arbitrage_end_time BETWEEN :endTimeFrom AND :endTimeTo";
            bindValues[":endTimeFrom"] = filter.endTimeFrom.toString(Qt::ISODate);
            bindValues[":endTimeTo"] = filter.endTimeTo.toString(Qt::ISODate);
        }
        else
        {
            sql += " AND arbitrage_end_time >= :endTimeFrom";
            bindValues[":endTimeFrom"] = filter.endTimeFrom.toString(Qt::ISODate);
        }
    }

    // 订单ID筛选（支持模糊匹配）
    if (filter.useOrderId && !filter.orderId.isEmpty())
    {
        sql += " AND order_id LIKE :orderId";
        bindValues[":orderId"] = "%" + filter.orderId + "%";
    }

    // 状态筛选
    if (filter.useStatus && !filter.status.isEmpty())
    {
        sql += " AND status = :status";
        bindValues[":status"] = filter.status;
    }

    // 交易额筛选
    if (filter.useAmount)
    {
        if (filter.maxAmount > filter.minAmount)
        {
            sql += " AND amount BETWEEN :minAmount AND :maxAmount";
            bindValues[":minAmount"] = filter.minAmount;
            bindValues[":maxAmount"] = filter.maxAmount;
        }
        else
        {
            sql += " AND amount >= :minAmount";
            bindValues[":minAmount"] = filter.minAmount;
        }
    }

    // 按时间倒序排列
    sql += " ORDER BY start_time DESC";

    return sql;
}
