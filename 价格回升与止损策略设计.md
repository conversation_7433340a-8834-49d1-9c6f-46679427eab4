# 价格回升与止损策略设计文档

## 📋 概述

本文档记录了针对币安套利系统的价格回升检测和延迟止损策略的设计思路，为未来基于QLib价格预测的智能止损策略提供参考。

## 🔍 当前问题分析

### 现有价格回升检测的问题

1. **逻辑矛盾**：止损单挂单后，任何价格回升都会立即触发止损单成交
   ```
   买入价格：0.9997
   止损价格：0.9997 (保本价格)
   价格回升到0.9997+ → 止损单立即成交 → 无法取消重新下单
   ```

2. **状态混乱**：在止损单已成交时仍尝试执行价格回升恢复逻辑
   ```
   止损单FILLED → 误判为价格回升 → 尝试取消已成交订单 → 系统错误
   ```

3. **时机错误**：价格回升检测应该在下止损单前，而不是下单后

### 有价值的场景

唯一有意义的价格回升检测时机：
```
检测到止损条件 → 取消原卖单 → 准备下止损单 → 此时检测价格回升 → 决定是否下止损单
```

## 💡 延迟止损策略设计

### 核心思想

在检测到止损条件后，不立即执行止损，而是等待一段时间观察价格走势，给价格回升更多机会。

### 策略流程

```mermaid
flowchart TD
    A[检测到止损条件] --> B[启动延迟止损定时器]
    B --> C{等待期间价格回升?}
    C -->|是| D[恢复正常套利]
    C -->|否| E[延迟时间到达]
    E --> F[根据当前买一价执行止损]
    F --> G[第一次止损: 当前买一价]
    G --> H{价格继续下跌?}
    H -->|是| I[第二次止损: 当前买一价-0.0001]
    H -->|否| J[等待成交]
```

### 参数配置

| 参数 | 默认值 | 说明 |
|------|--------|------|
| 延迟时间 | 30分钟 | 等待价格回升的时间窗口 |
| 最大亏损 | 0.0003 | 允许的最大亏损限制 |
| 紧急止损阈值 | 0.0002 | 超过此亏损立即止损 |

### 分级延迟策略

根据价格下跌幅度采用不同的等待时间：

```cpp
if (价格下跌幅度 < 0.0001) {
    等待时间 = 30分钟;  // 小幅下跌，等待时间长
} else if (价格下跌幅度 < 0.0002) {
    等待时间 = 15分钟;  // 中等下跌，等待时间中等
} else {
    等待时间 = 5分钟;   // 大幅下跌，快速止损
}
```

## 🔧 技术实现方案

### 1. 状态枚举扩展

```cpp
enum DelayedStopLossStatus {
    NORMAL_ARBITRAGE,        // 正常套利
    WAITING_FOR_RECOVERY,    // 等待价格回升
    DELAYED_STOP_LOSS_READY, // 延迟止损准备执行
    EMERGENCY_STOP_LOSS      // 紧急止损
};
```

### 2. 配置参数

```cpp
class DelayedStopLossConfig {
public:
    bool enableDelayedStopLoss = true;
    int delayMinutes = 30;
    double maxAllowedLoss = 0.0003;
    double emergencyStopLossThreshold = 0.0002;
    
    // 分级延迟配置
    struct DelayLevel {
        double priceDropThreshold;
        int waitMinutes;
    };
    
    QVector<DelayLevel> delayLevels = {
        {0.0001, 30},  // 小幅下跌等30分钟
        {0.0002, 15},  // 中等下跌等15分钟
        {0.0003, 5}    // 大幅下跌等5分钟
    };
};
```

### 3. 核心实现逻辑

```cpp
void MainWindow::handleStopLossCondition() {
    if (!m_config.enableDelayedStopLoss) {
        // 传统立即止损
        executeTraditionalStopLoss();
        return;
    }
    
    double priceDropAmount = calculatePriceDrop();
    int waitTime = calculateWaitTime(priceDropAmount);
    
    qDebug() << "启动延迟止损，等待" << waitTime << "分钟";
    
    // 设置状态
    currentOrder->setStatus(WAITING_FOR_RECOVERY);
    
    // 启动定时器
    m_delayedStopLossTimer->start(waitTime * 60 * 1000);
    
    // 启动价格监控
    startPriceRecoveryMonitoring();
}

void MainWindow::onDelayedStopLossTimeout() {
    // 30分钟后执行止损
    double currentBidPrice = getCurrentBidPrice();
    double buyPrice = currentOrder->getBuyPrice();
    
    // 计算止损价格，限制最大亏损
    double stopLossPrice = calculateDelayedStopLossPrice(currentBidPrice, buyPrice);
    
    executeDelayedStopLoss(stopLossPrice);
}

double MainWindow::calculateDelayedStopLossPrice(double currentBid, double buyPrice) {
    double maxLoss = m_config.maxAllowedLoss;
    double minStopLossPrice = buyPrice - maxLoss;
    
    // 第一次止损：当前买一价，但不超过最大亏损
    if (currentBid < minStopLossPrice) {
        return minStopLossPrice;
    }
    
    return currentBid;
}
```

### 4. 风险控制

```cpp
void MainWindow::monitorEmergencyStopLoss() {
    double currentLoss = calculateCurrentLoss();
    
    if (currentLoss > m_config.emergencyStopLossThreshold) {
        qDebug() << "触发紧急止损，当前亏损:" << currentLoss;
        
        // 取消延迟定时器
        m_delayedStopLossTimer->stop();
        
        // 立即执行紧急止损
        executeEmergencyStopLoss();
    }
}
```

## 📊 策略对比

| 策略类型 | 优点 | 缺点 | 适用场景 |
|----------|------|------|----------|
| 立即止损 | 风险可控，反应快速 | 可能错过价格回升 | 高波动市场 |
| 延迟止损 | 提高套利成功率 | 增加最大亏损风险 | 相对稳定市场 |
| 智能预测止损 | 基于数据决策 | 需要准确的预测模型 | 有预测能力时 |

## 🎯 未来发展方向

### 基于QLib的智能止损

1. **短期价格预测**：使用QLib进行5-30分钟的价格走势预测
2. **概率决策**：根据预测概率决定是否执行止损
3. **动态调整**：根据预测置信度调整等待时间
4. **机器学习优化**：持续学习优化止损策略参数

### 实现步骤

```python
# QLib价格预测示例
def predict_price_trend(symbol, timeframe='5min'):
    """
    预测未来5-30分钟的价格走势
    返回: {
        'direction': 'up'/'down'/'sideways',
        'confidence': 0.0-1.0,
        'expected_change': 价格变化幅度
    }
    """
    pass

# 集成到Qt应用
def should_execute_stop_loss(current_price, buy_price):
    prediction = predict_price_trend(symbol)
    
    if prediction['direction'] == 'up' and prediction['confidence'] > 0.7:
        return False  # 预测上涨，不执行止损
    elif prediction['direction'] == 'down' and prediction['confidence'] > 0.8:
        return True   # 预测下跌，立即止损
    else:
        return None   # 不确定，使用延迟止损策略
```

## 📝 总结

延迟止损策略是对当前立即止损的有益补充，通过给价格回升更多时间来提高套利成功率。但需要在收益和风险之间找到平衡点。

未来结合QLib的价格预测能力，可以实现更智能的止损决策，从被动等待转向主动预测，进一步优化套利策略的效果。

---

**注意**：本策略设计仅为技术探讨，实际使用时需要充分测试和风险评估。
