# Qt Deployment Script
param(
    [string]$BuildType = "release"
)

Write-Host ""
Write-Host "========================================"
Write-Host "        Qt Deployment Script"
Write-Host "========================================"
Write-Host ""

# Set Qt path
$QtPath = "Z:\StudyAndWork\Qt\6.5.3\msvc2019_64"

# Check if we should use Huawei computer path
if (Test-Path "computer_config.json") {
    $config = Get-Content "computer_config.json" | ConvertFrom-Json
    if ($config.current_config -eq "huawei_computer") {
        $QtPath = "D:\StudyAndWork\Qt\6.5.3\msvc2019_64"
        Write-Host "Using Huawei Computer configuration"
    } else {
        Write-Host "Using Current Computer configuration"
    }
}

Write-Host "Qt Path: $QtPath"

# Determine executable path
$ExePath = "Binance\$BuildType\Binance.exe"
$ExeDir = "Binance\$BuildType"

# Check if executable exists
if (-not (Test-Path $ExePath)) {
    Write-Host "ERROR: Executable not found at: $ExePath" -ForegroundColor Red
    Write-Host "Please build the application first." -ForegroundColor Red
    exit 1
}

# Use windeployqt to deploy Qt libraries
$WinDeployQt = "$QtPath\bin\windeployqt.exe"

if (-not (Test-Path $WinDeployQt)) {
    Write-Host "ERROR: windeployqt.exe not found at: $WinDeployQt" -ForegroundColor Red
    exit 1
}

Write-Host "Deploying Qt libraries for $BuildType version..."
Write-Host "Using windeployqt: $WinDeployQt"

# Run windeployqt
$deployArgs = @(
    "--$BuildType",
    "--compiler-runtime",
    "--force",
    $ExePath
)

Write-Host "Running: $WinDeployQt $($deployArgs -join ' ')"

try {
    & $WinDeployQt @deployArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "OK Qt libraries deployed successfully!" -ForegroundColor Green
        Write-Host "OK Starting $BuildType version..." -ForegroundColor Green
        Write-Host ""
        
        # Launch the application
        Start-Process -FilePath $ExePath
        
        Write-Host "OK Application launched successfully!" -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "ERROR: Qt deployment failed!" -ForegroundColor Red
    }
    
} catch {
    Write-Host ""
    Write-Host "ERROR: Failed to run windeployqt: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================"
Write-Host "Deployment completed!"
Write-Host "========================================"
Write-Host ""
