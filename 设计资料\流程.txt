# 币安量化交易软件流程文档

## 登录流程

1. 用户输入API Key和Secret Key
   - 参数：API Key, Secret Key
   - 操作：将输入的Key保存到应用程序中

2. 验证API Key和Secret Key的有效性
   - API调用：GET /api/v3/account
   - 参数：
     - API Key (放在请求头的X-MBX-APIKEY字段)
     - 时间戳 (timestamp)
     - 签名 (signature)，使用HMAC SHA256算法，Secret Key作为密钥
   - 返回值：
     - 成功：返回账户信息，包括余额等
     - 失败：返回错误信息
   - 解析：
     - 如果返回成功，表示API Key和Secret Key有效
     - 如果返回失败，提示用户API Key或Secret Key无效

3. 登录成功后的操作
   - 保存API Key和Secret Key（如果用户选择了"记住密码"选项）
   - 跳转到主界面
   - 加载账户信息

## 账户余额查询流程

1. 获取账户信息
   - API调用：GET /api/v3/account
   - 参数：
     - API Key (放在请求头的X-MBX-APIKEY字段)
     - 时间戳 (timestamp)
     - 签名 (signature)
   - 返回值：账户信息，包括所有资产的余额
   - 解析：
     - 从返回的数据中提取USDC和USDT的余额信息
     - 将余额信息显示在主界面上

2. 实时更新账户余额
   - 方法1：定时查询
     - 每隔一定时间（如5秒）调用一次账户信息API
     - 更新界面显示的余额信息
   
   - 方法2：使用Websocket用户数据流
     - API调用：POST /api/v3/userDataStream 获取listenKey
     - 建立Websocket连接：wss://stream.binance.com:9443/ws/{listenKey}
     - 接收账户更新事件，实时更新界面显示的余额信息
     - 定期调用PUT /api/v3/userDataStream 保持listenKey有效

## 错误处理流程

1. 网络错误处理
   - 检测网络连接状态
   - 如果网络连接失败，显示错误信息并提供重试选项
   - 在网络恢复后自动重新连接

2. API错误处理
   - 解析API返回的错误代码和错误信息
   - 根据错误类型显示相应的错误提示
   - 对于可恢复的错误，提供重试选项
   - 对于需要用户操作的错误（如API Key过期），提供相应的操作指导

3. 权限错误处理
   - 检查API Key的权限设置
   - 如果权限不足，提示用户需要设置的权限
   - 提供币安API权限设置的指导链接 