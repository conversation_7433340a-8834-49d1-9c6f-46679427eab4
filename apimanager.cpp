#include "apimanager.h"
#include "apiworker.h"
#include <QSslConfiguration>
#include <QDebug>
#include <QTimer>
#include <QEventLoop>
#include <QMetaObject>

// ApiManager类实现
ApiManager::ApiManager(QObject *parent) : QObject(parent)
{
    // 初始化成员变量
    m_isWebSocketConnected = false;

    // 初始化网络访问管理器
    m_networkManager = new QNetworkAccessManager(this);
    connect(m_networkManager, &QNetworkAccessManager::finished, this, &ApiManager::handleNetworkReply);

    // 配置SSL
    QSslConfiguration sslConfig = QSslConfiguration::defaultConfiguration();
    sslConfig.setPeerVerifyMode(QSslSocket::VerifyNone); // 在开发阶段可以禁用对等验证
    QSslConfiguration::setDefaultConfiguration(sslConfig);

    // 连接SSL错误信号
    connect(m_networkManager, &QNetworkAccessManager::sslErrors, this, [](QNetworkReply *reply, const QList<QSslError> &errors)
            {
        qDebug() << "SSL错误:";
        for (const QSslError &error : errors) {
            qDebug() << error.errorString();
        }

        // 在开发阶段，可以忽略SSL错误
        reply->ignoreSslErrors(); });

    // 初始化WebSocketClient
    m_webSocketClient = new WebSocketClient(this);
    connect(m_webSocketClient, &WebSocketClient::sg_sendMs, this, &ApiManager::sg_sendMs);
    connect(m_webSocketClient, &WebSocketClient::connected, this, &ApiManager::onWebSocketConnected);
    connect(m_webSocketClient, &WebSocketClient::disconnected, this, &ApiManager::onWebSocketDisconnected);
    connect(m_webSocketClient, &WebSocketClient::error, this, &ApiManager::onWebSocketError);
    connect(m_webSocketClient, &WebSocketClient::textMessageReceived, this, &ApiManager::onWebSocketTextMessageReceived);
    connect(m_webSocketClient, &WebSocketClient::reconnectAttempt, this, &ApiManager::onWebSocketReconnectAttempt);
    connect(m_webSocketClient, &WebSocketClient::reconnected, this, &ApiManager::onWebSocketReconnected);
    connect(m_webSocketClient, &WebSocketClient::heartbeatSent, this, &ApiManager::onWebSocketHeartbeatSent);
    connect(m_webSocketClient, &WebSocketClient::heartbeatReceived, this, &ApiManager::onWebSocketHeartbeatReceived);
    connect(m_webSocketClient, &WebSocketClient::bookTickerDataReceived, this, &ApiManager::onWebSocketBookTickerReceived);

    // 配置WebSocketClient
    m_webSocketClient->setAutoReconnect(true);
    m_webSocketClient->setHeartbeatInterval(20000); // 设置为20秒，这与币安WebSocket服务器要求一致
    m_webSocketClient->setReconnectInterval(5000);  // 5秒重连一次
    m_webSocketClient->setMaxReconnectAttempts(10); // 最多尝试10次重连

    // 初始化保活定时器
    m_keepAliveTimer = new QTimer(this);
    connect(m_keepAliveTimer, &QTimer::timeout, this, &ApiManager::onKeepAliveTimerTimeout);
}

ApiManager::~ApiManager()
{
    // 断开所有信号连接，避免后续操作触发回调
    disconnect(this, nullptr, nullptr, nullptr);

    // 标记listenKey为空，防止重连
    m_listenKey.clear();
    m_isWebSocketConnected = false;

    // 安全地处理定时器 - 不直接删除对象
    if (m_keepAliveTimer)
    {
        // 断开所有连接
        m_keepAliveTimer->disconnect();
        // 停止定时器，如果它属于当前线程
        if (m_keepAliveTimer->thread() == thread())
        {
            if (m_keepAliveTimer->isActive())
            {
                m_keepAliveTimer->stop();
            }
            // 安排延迟删除，而不是直接删除
            m_keepAliveTimer->deleteLater();
        }
        m_keepAliveTimer = nullptr;
    }

    // 安全地处理WebSocketClient - 不直接删除对象
    if (m_webSocketClient)
    {
        // 断开所有连接
        m_webSocketClient->disconnect();

        // 关闭WebSocket连接
        m_webSocketClient->close();

        // 只有当WebSocketClient属于当前线程时，才尝试操作它
        if (m_webSocketClient->thread() == thread())
        {
            // 安排延迟删除，而不是直接删除
            m_webSocketClient->deleteLater();
        }
        m_webSocketClient = nullptr;
    }

    // 安全地处理网络管理器 - 不直接删除对象
    if (m_networkManager)
    {
        // 取消所有进行中的请求，如果可能的话
        QList<QNetworkReply *> activeReplies = m_networkManager->findChildren<QNetworkReply *>();
        for (QNetworkReply *reply : activeReplies)
        {
            if (reply && reply->isRunning())
            {
                // 断开所有连接
                reply->disconnect();
                // 安排延迟删除，而不是调用abort
                reply->deleteLater();
            }
        }

        // 断开所有连接
        m_networkManager->disconnect();

        // 只有当网络管理器属于当前线程时，才尝试操作它
        if (m_networkManager->thread() == thread())
        {
            // 安排延迟删除，而不是直接删除
            m_networkManager->deleteLater();
        }
        m_networkManager = nullptr;
    }
}

void ApiManager::setApiKeys(const QString &apiKey, const QString &secretKey)
{
    // 设置API密钥
    m_apiKey = apiKey;
    m_secretKey = secretKey;
}

void ApiManager::validateApiKeys()
{
    // 验证API密钥
    QUrlQuery query;
    query.addQueryItem("timestamp", Utils::getTimestamp());

    QUrl url(API_BASE_URL + "/api/v3/account");

    // 创建请求
    QNetworkRequest request;
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");

    // 添加API密钥到请求头
    request.setRawHeader("X-MBX-APIKEY", m_apiKey.toUtf8());

    // 配置SSL
    QSslConfiguration sslConfig = request.sslConfiguration();
    sslConfig.setPeerVerifyMode(QSslSocket::VerifyNone);
    request.setSslConfiguration(sslConfig);

    // 创建带签名的查询
    QUrlQuery signedQuery = createSignedQuery(query);
    QUrl urlWithQuery = url;
    urlWithQuery.setQuery(signedQuery);
    request.setUrl(urlWithQuery);

    qDebug() << "完整请求URL:" << urlWithQuery.toString();

    // 发送请求
    QNetworkReply *reply = m_networkManager->get(request);

    // 设置请求类型和验证标志
    reply->setProperty("requestType", static_cast<int>(RequestType::GET_ACCOUNT_INFO));
    reply->setProperty("isValidating", true); // 标记为验证请求

    // 连接SSL错误信号
    connect(reply, &QNetworkReply::sslErrors, this, [reply](const QList<QSslError> &errors)
            {
        qDebug() << "验证API密钥请求SSL错误:";
        for (const QSslError &error : errors) {
            qDebug() << error.errorString();
        }

        // 在开发阶段，可以忽略SSL错误
        reply->ignoreSslErrors(); });

    // 连接网络错误信号
    connect(reply, &QNetworkReply::errorOccurred, this,
            [reply](QNetworkReply::NetworkError error)
            { qDebug() << "验证API密钥请求网络错误:" << error << "-" << reply->errorString(); });
}

void ApiManager::getAccountInfo()
{
    // 获取账户信息
    QUrlQuery query;
    query.addQueryItem("timestamp", Utils::getTimestamp());

    QUrl url(API_BASE_URL + "/api/v3/account");
    sendRequest(url, query, RequestType::GET_ACCOUNT_INFO, QByteArray("GET"));
}

void ApiManager::getBestOrderBookTicker(const QString &symbol)
{
    // 获取最佳挂单价格
    QUrl url(API_BASE_URL + "/api/v3/ticker/bookTicker");

    QUrlQuery query;
    query.addQueryItem("symbol", symbol);

    // 不需要签名
    sendRequest(url, query, RequestType::GET_TICKER_PRICE, QByteArray("GET"), QByteArray(), false);
}

void ApiManager::getLatestPrice(const QString &symbol)
{
    // 获取最新成交价格
    QUrl url(API_BASE_URL + "/api/v3/trades");

    QUrlQuery query;
    query.addQueryItem("symbol", symbol);
    query.addQueryItem("limit", "1"); // 只获取最新的一笔交易

    // 不需要签名
    sendRequest(url, query, RequestType::GET_LATEST_PRICE, QByteArray("GET"), QByteArray(), false);
}

void ApiManager::placeOrder(const QString &symbol, const QString &side, const QString &type, const QString &quantity, const QString &price,
                            const QString &relatedOrderId)
{
    // 下单
    QUrlQuery query;
    query.addQueryItem("symbol", symbol);
    query.addQueryItem("side", side);
    query.addQueryItem("type", type);
    query.addQueryItem("quantity", quantity);

    if (!price.isEmpty())
    {
        query.addQueryItem("price", price);

        // 对于限价单，需要添加timeInForce参数
        if (type == "LIMIT")
        {
            query.addQueryItem("timeInForce", "GTC"); // GTC = Good Till Cancel
        }
    }

    // 添加自定义客户端订单ID，用于关联买单ID
    if (!relatedOrderId.isEmpty())
    {
        // 使用newOrderRespType=RESULT参数获取更详细的响应
        query.addQueryItem("newOrderRespType", "RESULT");

        // 将关联的买单ID保存在请求上下文中，以便在响应处理时使用
        m_requestContext["relatedOrderId"] = relatedOrderId;

        qDebug() << "下单请求关联买单ID:" << relatedOrderId;
    }

    query.addQueryItem("timestamp", Utils::getTimestamp());

    // 打印请求参数，用于调试
    // qDebug() << "下单请求参数:";
    // qDebug() << "symbol:" << symbol;
    // qDebug() << "side:" << side;
    // qDebug() << "type:" << type;
    // qDebug() << "quantity:" << quantity;
    // qDebug() << "price:" << price;
    if (type == "LIMIT")
    {
        qDebug() << "timeInForce: GTC";
    }
    // qDebug() << "timestamp:" << Utils::getTimestamp();

    QUrl url(API_BASE_URL + "/api/v3/order");
    sendRequest(url, query, RequestType::PLACE_ORDER, QByteArray("POST"));
}

void ApiManager::cancelOrder(const QString &symbol, const QString &orderId)
{
    // 取消订单
    QUrlQuery query;
    query.addQueryItem("symbol", symbol);

    // 特别处理订单ID，确保使用普通整数格式而非科学计数法
    QString processedOrderId = orderId;

    // 检查是否是科学计数法格式（包含'e'或'E'）
    if (orderId.contains('e', Qt::CaseInsensitive))
    {
        bool ok;
        double doubleId = orderId.toDouble(&ok);
        if (ok)
        {
            // 将科学计数法转换为普通整数表示，避免精度损失
            processedOrderId = QString::number(static_cast<qlonglong>(doubleId), 'f', 0);
        }
    }
    else
    {
        // 即使不是科学计数法，也确保是整数格式
        bool ok;
        qlonglong longId = orderId.toLongLong(&ok);
        if (ok)
        {
            processedOrderId = QString::number(longId);
        }
    }

    // 使用处理后的订单ID
    query.addQueryItem("orderId", processedOrderId);
    qDebug() << "取消订单，使用处理后的订单ID: " << processedOrderId << "，原始ID: " << orderId;

    query.addQueryItem("timestamp", Utils::getTimestamp());

    QUrl url(API_BASE_URL + "/api/v3/order");

    // 记录请求开始时间
    QDateTime requestStartTime = QDateTime::currentDateTime();

    // 保存撤单请求的相关信息以便重试
    m_requestContext["cancel_order_symbol"] = symbol;
    m_requestContext["cancel_order_id"] = processedOrderId; // 保存处理后的ID
    m_requestContext["cancel_order_attempt"] = 1;           // 初始尝试次数为1

    sendRequest(url, query, RequestType::CANCEL_ORDER, QByteArray("DELETE"));
}

void ApiManager::getOrderStatus(const QString &symbol, const QString &orderId)
{
    // 获取订单状态
    QUrlQuery query;
    query.addQueryItem("symbol", symbol);

    // 特别处理订单ID，确保使用普通整数格式而非科学计数法
    QString processedOrderId = orderId;

    // 检查是否是科学计数法格式（包含'e'或'E'）
    if (orderId.contains('e', Qt::CaseInsensitive))
    {
        bool ok;
        double doubleId = orderId.toDouble(&ok);
        if (ok)
        {
            // 将科学计数法转换为普通整数表示，避免精度损失
            processedOrderId = QString::number(static_cast<qlonglong>(doubleId), 'f', 0);
        }
    }
    else
    {
        // 即使不是科学计数法，也确保是整数格式
        bool ok;
        qlonglong longId = orderId.toLongLong(&ok);
        if (ok)
        {
            processedOrderId = QString::number(longId);
        }
    }

    // 使用处理后的订单ID
    query.addQueryItem("orderId", processedOrderId);

    query.addQueryItem("timestamp", Utils::getTimestamp());

    QUrl url(API_BASE_URL + "/api/v3/order");
    sendRequest(url, query, RequestType::GET_ORDER_STATUS, QByteArray("GET"));
}

void ApiManager::getOpenOrders(const QString &symbol)
{
    // 获取所有挂单
    QUrlQuery query;
    query.addQueryItem("symbol", symbol);
    query.addQueryItem("timestamp", Utils::getTimestamp());

    QUrl url(API_BASE_URL + "/api/v3/openOrders");
    sendRequest(url, query, RequestType::GET_OPEN_ORDERS, QByteArray("GET"));
}

void ApiManager::getOrderBookDepth(const QString &symbol, int limit)
{
    // 获取订单簿深度数据
    QUrlQuery query;
    query.addQueryItem("symbol", symbol);
    query.addQueryItem("limit", QString::number(limit));

    QUrl url(API_BASE_URL + "/api/v3/depth");
    sendRequest(url, query, RequestType::GET_ORDER_BOOK_DEPTH, QByteArray("GET"), QByteArray(), false);

    // qDebug().noquote() << "正在获取" << symbol << "订单簿深度数据，深度=" << limit;
}

void ApiManager::sendRequest(const QUrl &url, const QUrlQuery &query, RequestType requestType, const QByteArray &httpMethod, const QByteArray &postData,
                             bool needSignature)
{
    // 发送API请求
    QNetworkRequest request;
    request.setUrl(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");

    // 配置SSL
    QSslConfiguration sslConfig = request.sslConfiguration();
    sslConfig.setPeerVerifyMode(QSslSocket::VerifyNone); // 在开发阶段可以禁用对等验证
    request.setSslConfiguration(sslConfig);

    // 添加API密钥到请求头
    if (!m_apiKey.isEmpty())
    {
        request.setRawHeader("X-MBX-APIKEY", m_apiKey.toUtf8());
    }

    QNetworkReply *reply = nullptr;

    if (needSignature)
    {
        // 需要签名的请求
        QUrlQuery signedQuery = createSignedQuery(query);

        // 打印完整的请求URL和签名，用于调试
        if (requestType == RequestType::PLACE_ORDER)
        {
            qDebug() << "下单完整请求:";
            qDebug() << "URL:" << url.toString();
            // qDebug() << "签名查询:" << signedQuery.toString(QUrl::FullyEncoded);
        }

        if (httpMethod == "GET")
        {
            // GET请求
            QUrl urlWithQuery = url;
            urlWithQuery.setQuery(signedQuery);
            request.setUrl(urlWithQuery);
            reply = m_networkManager->get(request);
        }
        else if (httpMethod == "POST")
        {
            // POST请求
            QByteArray postData = signedQuery.toString(QUrl::FullyEncoded).toUtf8();
            // qDebug() << "POST数据:" << postData;
            reply = m_networkManager->post(request, postData);
        }
        else if (httpMethod == "DELETE")
        {
            // DELETE请求
            QUrl urlWithQuery = url;
            urlWithQuery.setQuery(signedQuery);
            request.setUrl(urlWithQuery);
            reply = m_networkManager->deleteResource(request);
        }
        else if (httpMethod == "PUT")
        {
            // PUT请求
            QByteArray putData = signedQuery.toString(QUrl::FullyEncoded).toUtf8();
            reply = m_networkManager->put(request, putData);
        }
    }
    else
    {
        // 不需要签名的请求
        if (httpMethod == "GET")
        {
            // GET请求
            QUrl urlWithQuery = url;
            urlWithQuery.setQuery(query);
            request.setUrl(urlWithQuery);
            reply = m_networkManager->get(request);
        }
        else if (httpMethod == "POST")
        {
            // POST请求，不需要签名
            QUrl urlWithQuery = url;
            if (!query.isEmpty())
            {
                urlWithQuery.setQuery(query);
                request.setUrl(urlWithQuery);
            }
            reply = m_networkManager->post(request, postData);
        }
        else if (httpMethod == "PUT")
        {
            // PUT请求，不需要签名
            QUrl urlWithQuery = url;
            if (!query.isEmpty())
            {
                urlWithQuery.setQuery(query);
                request.setUrl(urlWithQuery);
            }
            reply = m_networkManager->put(request, postData);
        }
    }

    // 保存请求类型，用于后续处理
    if (reply)
    {
        reply->setProperty("requestType", static_cast<int>(requestType));

        // 连接SSL错误信号
        connect(reply, &QNetworkReply::sslErrors, this, [reply](const QList<QSslError> &errors)
                {
            qDebug() << "请求SSL错误:";
            for (const QSslError &error : errors) {
                qDebug() << error.errorString();
            }

            // 在开发阶段，可以忽略SSL错误
            reply->ignoreSslErrors(); });

        // 连接网络错误信号
        connect(reply, &QNetworkReply::errorOccurred, this, [reply, requestType](QNetworkReply::NetworkError error)
                {
            qDebug() << "网络请求错误:" << error << "-" << reply->errorString();
            if (requestType == RequestType::PLACE_ORDER) {
                qDebug() << "下单请求失败:" << reply->errorString();
                qDebug() << "HTTP状态码:" << reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
                qDebug() << "响应内容:" << reply->readAll();
            } });

        // 设置请求超时
        QTimer::singleShot(15000, reply, [reply]()
                           {
            if (!reply->isFinished()) {
                qDebug() << "请求超时，中断请求";
                reply->abort();
            } });
    }
}

QByteArray ApiManager::generateSignature(const QUrlQuery &query)
{
    // 生成签名
    QByteArray queryString = query.toString(QUrl::FullyEncoded).toUtf8();

    // 替换一些特殊字符，确保与币安API要求一致
    queryString.replace("%20", "+");

    // 生成签名
    QByteArray signature = Utils::generateHmacSha256(m_secretKey.toUtf8(), queryString);

    return signature;
}

QUrlQuery ApiManager::createSignedQuery(const QUrlQuery &query)
{
    // 创建带签名的URL查询
    QUrlQuery signedQuery = query;

    // 生成签名
    QByteArray signature = generateSignature(query);

    // 添加签名到查询
    signedQuery.addQueryItem("signature", signature);

    return signedQuery;
}

void ApiManager::handleNetworkReply(QNetworkReply *reply)
{
    // 处理网络请求完成
    ApiResponse response;
    response.success = false;
    response.requestType = static_cast<RequestType>(reply->property("requestType").toInt());

    // 检查是否有错误
    if (reply->error() != QNetworkReply::NoError)
    {
        response.errorMessage = reply->errorString();

        // 读取响应内容，检查是否是时间戳错误
        QByteArray responseData = reply->readAll();
        qDebug() << "错误响应内容:" << responseData;

        // 解析JSON响应
        QJsonParseError parseError;
        QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData, &parseError);

        // 检查是否是时间戳错误
        bool isTimestampError = false;
        if (parseError.error == QJsonParseError::NoError && jsonDoc.isObject())
        {
            QJsonObject errorObj = jsonDoc.object();
            if (errorObj.contains("code") && errorObj["code"].toInt() == -1021)
            {
                // 时间戳错误，记录日志
                qDebug() << "时间戳错误: " << errorObj["msg"].toString();
                isTimestampError = true;

                // 根据请求类型重新发送请求
                if (response.requestType == RequestType::GET_ACCOUNT_INFO)
                {
                    // 延迟一小段时间后重新获取账户信息
                    QTimer::singleShot(100, this, &ApiManager::getAccountInfo);
                    qDebug() << "将在100毫秒后重新获取账户信息";
                }
            }
        }

        // 如果不是时间戳错误，按照原来的逻辑处理
        if (!isTimestampError)
        {
            // 特别处理下单错误
            if (response.requestType == RequestType::PLACE_ORDER)
            {
                emit orderPlaced(false, QJsonObject(), response.errorMessage);

                // 发送网络错误信号，以便主窗口检查订单状态
                QJsonObject errorInfo;
                errorInfo["errorType"] = "NetworkError";
                errorInfo["errorCode"] = static_cast<int>(reply->error());
                errorInfo["errorMessage"] = response.errorMessage;
                errorInfo["requestType"] = "PLACE_ORDER";
                emit orderUpdateReceived(errorInfo);
            }
            else if (response.requestType == RequestType::CANCEL_ORDER)
            {
                // 尝试从错误消息中识别特定错误
                bool shouldRetry = false;

                // 处理"Error transferring"和"server replied:"等网络传输错误
                if (response.errorMessage.contains("Error transferring") || response.errorMessage.contains("server replied:") ||
                    response.errorMessage.contains("Connection closed"))
                {
                    // 检查重试次数
                    int attempts = m_requestContext.value("cancel_order_attempt", 1).toInt();
                    if (attempts < 3)
                    { // 最多重试3次
                        shouldRetry = true;
                        m_requestContext["cancel_order_attempt"] = attempts + 1;

                        // 准备重试撤单请求
                        QString symbol = m_requestContext.value("cancel_order_symbol").toString();
                        QString orderId = m_requestContext.value("cancel_order_id").toString();

                        if (!symbol.isEmpty() && !orderId.isEmpty())
                        {
                            qDebug() << "撤单请求失败，正在进行第" << (attempts + 1) << "次重试";

                            // 延迟500毫秒后重试
                            QTimer::singleShot(500, this, [this, symbol, orderId]()
                                               {
                                // 重新检查订单状态，可能已经被取消或成交
                                getOrderStatus(symbol, orderId);

                                // 1秒后再次尝试取消订单
                                QTimer::singleShot(1000, this, [this, symbol, orderId]() { cancelOrder(symbol, orderId); }); });
                        }
                    }
                }

                // 如果不重试或已达到最大重试次数，则发送订单取消失败信号
                if (!shouldRetry)
                {
                    emit orderCancelled(false, QJsonObject(), response.errorMessage);

                    // 发送网络错误信号，以便主窗口检查订单状态
                    QJsonObject errorInfo;
                    errorInfo["errorType"] = "NetworkError";
                    errorInfo["errorCode"] = static_cast<int>(reply->error());
                    errorInfo["errorMessage"] = response.errorMessage;
                    errorInfo["requestType"] = "CANCEL_ORDER";
                    emit orderUpdateReceived(errorInfo);
                }
            }
            else if (response.requestType == RequestType::GET_ACCOUNT_INFO)
            {
                // 检查是否是验证请求
                bool isValidating = reply->property("isValidating").toBool();
                if (isValidating)
                {
                    // 验证API密钥失败
                    qDebug() << "验证API密钥失败:" << response.errorMessage;
                    qDebug() << "HTTP状态码:" << reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();

                    emit apiKeysValidated(false, "网络请求错误: " + response.errorMessage);
                }
            }
            else if (response.requestType == RequestType::GET_OPEN_ORDERS)
            {
                // 处理获取挂单列表错误
                QJsonObject errorInfo;
                errorInfo["errorType"] = "NetworkError";
                errorInfo["errorCode"] = static_cast<int>(reply->error());
                errorInfo["errorMessage"] = response.errorMessage;
                errorInfo["requestType"] = "GET_OPEN_ORDERS";
                emit orderUpdateReceived(errorInfo);
                emit openOrdersReceived(false, QJsonArray(), response.errorMessage);
            }

            qDebug() << "网络请求错误: " << response.errorMessage;
        }
    }
    else
    {
        // 从响应头中提取API权重信息
        if (reply->hasRawHeader("X-MBX-USED-WEIGHT-1M"))
        {
            m_currentWeight = reply->rawHeader("X-MBX-USED-WEIGHT-1M").toInt();
            // 只有当权重超过阈值时才输出
            if (m_currentWeight >= m_apiWeightOutputThreshold)
            {
                qDebug() << "当前1分钟API权重: " << m_currentWeight;
            }
        }

        // 检查5分钟权重
        if (reply->hasRawHeader("X-MBX-USED-WEIGHT-5M"))
        {
            m_currentWeight5m = reply->rawHeader("X-MBX-USED-WEIGHT-5M").toInt();
            // 只有当1分钟权重超过阈值时才输出其他权重信息
            if (m_currentWeight >= m_apiWeightOutputThreshold)
            {
                qDebug() << "当前5分钟API权重: " << m_currentWeight5m;
            }
        }

        // 检查1小时权重
        if (reply->hasRawHeader("X-MBX-USED-WEIGHT-1H"))
        {
            m_currentWeight1h = reply->rawHeader("X-MBX-USED-WEIGHT-1H").toInt();
            // 只有当1分钟权重超过阈值时才输出其他权重信息
            if (m_currentWeight >= m_apiWeightOutputThreshold)
            {
                qDebug() << "当前1小时API权重: " << m_currentWeight1h;
            }
        }

        // 检查1天权重
        if (reply->hasRawHeader("X-MBX-USED-WEIGHT-1D"))
        {
            m_currentWeight1d = reply->rawHeader("X-MBX-USED-WEIGHT-1D").toInt();
            // 只有当1分钟权重超过阈值时才输出其他权重信息
            if (m_currentWeight >= m_apiWeightOutputThreshold)
            {
                qDebug() << "当前1天API权重: " << m_currentWeight1d;
            }
        }

        // 只有当1分钟权重超过阈值时才输出权重相关头信息
        if (m_currentWeight >= m_apiWeightOutputThreshold)
        {
            QList<QByteArray> headers = reply->rawHeaderList();
            for (const QByteArray &header : headers)
            {
                if (header.contains("WEIGHT") || header.contains("LIMIT"))
                {
                    qDebug() << "发现权重相关头: " << header << ":" << reply->rawHeader(header);
                }
            }
        }

        if (reply->hasRawHeader("X-MBX-ORDER-COUNT-1M"))
        {
            m_orderCount = reply->rawHeader("X-MBX-ORDER-COUNT-1M").toInt();
            // 只有当1分钟权重超过阈值时才输出订单数量
            if (m_currentWeight >= m_apiWeightOutputThreshold)
            {
                qDebug() << "当前订单数量: " << m_orderCount;
            }
        }

        // 读取响应数据
        QByteArray responseData = reply->readAll();

        // 只在需要时打印响应数据
        if (response.requestType == RequestType::PLACE_ORDER)
        {
        }

        // 解析JSON响应
        QJsonParseError parseError;
        QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData, &parseError);

        if (parseError.error != QJsonParseError::NoError)
        {
            response.errorMessage = "JSON解析错误: " + parseError.errorString();
            qDebug() << response.errorMessage;

            // 特别处理下单错误
            if (response.requestType == RequestType::PLACE_ORDER)
            {
                emit orderPlaced(false, QJsonObject(), response.errorMessage);
            }
            else if (response.requestType == RequestType::CANCEL_ORDER)
            {
                emit orderCancelled(false, QJsonObject(), response.errorMessage);
            }
            else if (response.requestType == RequestType::GET_ACCOUNT_INFO)
            {
                // 检查是否是验证请求
                bool isValidating = reply->property("isValidating").toBool();
                if (isValidating)
                {
                    // 验证API密钥失败
                    emit apiKeysValidated(false, "JSON解析错误: " + parseError.errorString());
                }
            }
        }
        else
        {
            response.success = true;
            response.data = jsonDoc;

            // 根据请求类型处理响应
            switch (response.requestType)
            {
            case RequestType::CREATE_LISTEN_KEY:
            {
                // 处理创建listenKey响应
                if (jsonDoc.isObject() && jsonDoc.object().contains("listenKey"))
                {
                    m_listenKey = jsonDoc.object()["listenKey"].toString();
                    qDebug() << "创建listenKey成功:" << m_listenKey;
                    // if (userStream == nullptr) {
                    //     userStream = new BinanceUserStream(m_apiKey, m_listenKey);
                    //     userStream->setApiKeyListenKey(m_apiKey, m_listenKey);
                    // }
                    // 连接WebSocket
                    connectUserDataWebSocket();
                }
                else if (jsonDoc.isObject() && jsonDoc.object().contains("code"))
                {
                    // 处理错误响应
                    QJsonObject errorObj = jsonDoc.object();
                    int errorCode = errorObj["code"].toInt();
                    QString errorMsg = errorObj["msg"].toString();

                    qDebug() << "创建listenKey失败: " << errorCode << " - " << errorMsg;
                }
                break;
            }
            case RequestType::KEEP_ALIVE_LISTEN_KEY:
            {
                // 处理延长listenKey有效期响应
                // qDebug() << "延长listenKey有效期成功";
                break;
            }
            case RequestType::GET_ACCOUNT_INFO:
            {
                // 处理账户信息响应
                // qDebug() << "收到账户信息响应:" << responseData;

                if (jsonDoc.isObject())
                {
                    QJsonObject accountInfo = jsonDoc.object();

                    // 检查是否是验证请求
                    bool isValidating = reply->property("isValidating").toBool();
                    if (isValidating)
                    {
                        // 验证API密钥
                        qDebug() << "验证API密钥成功";
                        emit apiKeysValidated(true);
                    }
                    else
                    {
                        // 更新账户信息
                        emit accountInfoUpdated(accountInfo);
                    }
                }
                else if (jsonDoc.isObject() && jsonDoc.object().contains("code"))
                {
                    // 处理错误响应
                    QJsonObject errorObj = jsonDoc.object();
                    int errorCode = errorObj["code"].toInt();
                    QString errorMsg = errorObj["msg"].toString();

                    response.success = false;
                    response.errorCode = errorCode;
                    response.errorMessage = errorMsg;

                    // 检查是否是验证请求
                    bool isValidating = reply->property("isValidating").toBool();
                    if (isValidating)
                    {
                        // 验证API密钥失败
                        qDebug() << "验证API密钥失败: " << errorCode << " - " << errorMsg;
                        emit apiKeysValidated(false, errorMsg);
                    }
                    else
                    {
                        qDebug() << "获取账户信息失败: " << errorCode << " - " << errorMsg;
                        // 发送空的账户信息对象，表示获取失败
                        emit accountInfoUpdated(QJsonObject());
                    }
                }
                break;
            }
            case RequestType::GET_TICKER_PRICE:
            {
                // 处理最佳挂单价格响应
                if (jsonDoc.isObject())
                {
                    QJsonObject ticker = jsonDoc.object();
                    emit bestOrderBookTickerUpdated(ticker);
                }
                break;
            }
            case RequestType::GET_LATEST_PRICE:
            {
                // 处理最新成交价格响应
                if (jsonDoc.isArray() && !jsonDoc.array().isEmpty())
                {
                    // 获取最新的一笔交易
                    QJsonObject tradeInfo = jsonDoc.array().first().toObject();
                    emit latestPriceUpdated(tradeInfo);
                }
                break;
            }
            case RequestType::PLACE_ORDER:
            {
                // 处理下单响应
                if (jsonDoc.isObject())
                {
                    QJsonObject orderInfo = jsonDoc.object();

                    if (orderInfo.contains("code"))
                    {
                        // 下单失败
                        int errorCode = orderInfo["code"].toInt();
                        QString errorMsg = orderInfo["msg"].toString();

                        response.success = false;
                        response.errorCode = errorCode;
                        response.errorMessage = errorMsg;

                        emit orderPlaced(false, QJsonObject(), errorMsg);

                        qDebug() << "下单失败: " << errorCode << " - " << errorMsg;
                    }
                    else
                    {
                        // 下单成功
                        // qDebug() << "下单成功: " << orderInfo;

                        // 检查是否有关联的买单ID
                        if (m_requestContext.contains("relatedOrderId"))
                        {
                            QString relatedOrderId = m_requestContext["relatedOrderId"].toString();
                            if (!relatedOrderId.isEmpty())
                            {
                                // 将关联的买单ID添加到订单信息中
                                QJsonObject orderInfoWithRelatedId = orderInfo;
                                orderInfoWithRelatedId["relatedOrderId"] = relatedOrderId;

                                qDebug() << "下单成功，关联买单ID: " << relatedOrderId;
                                emit orderPlaced(true, orderInfoWithRelatedId);

                                // 清除请求上下文中的关联买单ID
                                m_requestContext.remove("relatedOrderId");
                                break;
                            }
                        }

                        // 如果没有关联的买单ID，正常发送信号
                        emit orderPlaced(true, orderInfo);
                    }
                }
                break;
            }
            case RequestType::CANCEL_ORDER:
            {
                // 处理取消订单响应
                if (jsonDoc.isObject())
                {
                    QJsonObject orderInfo = jsonDoc.object();

                    if (orderInfo.contains("code"))
                    {
                        // 取消订单失败
                        int errorCode = orderInfo["code"].toInt();
                        QString errorMsg = orderInfo["msg"].toString();

                        response.success = false;
                        response.errorCode = errorCode;
                        response.errorMessage = errorMsg;

                        emit orderCancelled(false, QJsonObject(), errorMsg);

                        qDebug() << "取消订单失败: " << errorCode << " - " << errorMsg;
                    }
                    else
                    {
                        // 取消订单成功
                        emit orderCancelled(true, orderInfo);
                    }
                }
                break;
            }
            case RequestType::GET_ORDER_STATUS:
            {
                // 处理获取订单状态响应
                if (jsonDoc.isObject())
                {
                    QJsonObject orderStatus = jsonDoc.object();

                    if (orderStatus.contains("code"))
                    {
                        // 获取订单状态失败
                        int errorCode = orderStatus["code"].toInt();
                        QString errorMsg = orderStatus["msg"].toString();

                        response.success = false;
                        response.errorCode = errorCode;
                        response.errorMessage = errorMsg;

                        qDebug() << "获取订单状态失败: " << errorCode << " - " << errorMsg;

                        // 发送错误信号，包含错误信息
                        QJsonObject errorInfo;
                        errorInfo["errorType"] = "GetOrderStatus";
                        errorInfo["errorCode"] = errorCode;
                        errorInfo["errorMessage"] = errorMsg;
                        errorInfo["requestType"] = "GET_ORDER_STATUS";
                        emit orderUpdateReceived(errorInfo);
                    }
                    else
                    {
                        // 检索订单信息
                        QString orderId = QString::number(orderStatus["orderId"].toInt());
                        QString symbol = orderStatus["symbol"].toString();
                        QString side = orderStatus["side"].toString();
                        QString status = orderStatus["status"].toString();
                        qint64 updateTime = orderStatus["updateTime"].toVariant().toLongLong();

                        // 获取订单状态成功
                        emit orderStatusUpdated(orderId, symbol, side, status, updateTime);
                    }
                }
                break;
            }
            case RequestType::GET_OPEN_ORDERS:
            {
                // 处理获取所有挂单响应
                if (jsonDoc.isArray())
                {
                    QJsonArray orders = jsonDoc.array();
                    emit openOrdersReceived(true, orders);
                }
                else
                {
                    emit openOrdersReceived(false, QJsonArray(), "响应格式错误");
                }
                break;
            }
            case RequestType::GET_ORDER_BOOK_DEPTH:
            {
                if (response.success)
                {
                    QJsonObject depthData = QJsonDocument::fromJson(responseData).object();
                    emit orderBookDepthUpdated(depthData);
                }
                else
                {
                    qDebug().noquote() << "获取订单簿深度数据失败：" << response.errorMessage;
                    // 发送空对象作为错误指示
                    emit orderBookDepthUpdated(QJsonObject());
                }
                break;
            }
            default:
                break;
            }
        }
    }

    // 发送请求完成信号
    emit requestFinished(response);

    // 释放reply对象
    reply->deleteLater();
}

// 启动WebSocket连接，接收实时订单更新
void ApiManager::startUserDataStream()
{
    qDebug() << "启动用户数据WebSocket流...";

    if (m_isWebSocketConnected)
    {
        qDebug() << "WebSocket已连接，无需重复启动";
        return;
    }

    // 如果已经有ListenKey，先延长其有效期
    if (!m_listenKey.isEmpty())
    {
        keepAliveListenKey();

        // 如果WebSocket未连接，则连接它
        if (m_webSocketClient->state() != QAbstractSocket::ConnectedState)
        {
            connectUserDataWebSocket();
        }
    }
    else
    {
        // 创建新的ListenKey
        createListenKey();
        // 在ListenKey创建成功的回调中会调用connectUserDataWebSocket
    }
}

// 停止WebSocket连接
void ApiManager::stopUserDataStream()
{
    qDebug().noquote() << "正在停止用户数据流...";

    // 清除listenKey，防止重连
    m_listenKey.clear();
    m_isWebSocketConnected = false;

    // 停止保活定时器
    if (m_keepAliveTimer && m_keepAliveTimer->isActive())
    {
        m_keepAliveTimer->stop();
        qDebug().noquote() << "保活定时器已停止";
    }

    // 关闭WebSocket连接
    if (m_webSocketClient)
    {
        // 禁用自动重连，然后关闭连接
        m_webSocketClient->setAutoReconnect(false);
        m_webSocketClient->close();
        qDebug().noquote() << "WebSocket连接已关闭";
    }

    qDebug().noquote() << "用户数据流已停止";
}

// 创建listenKey，用于WebSocket连接
void ApiManager::createListenKey()
{
    // 创建listenKey，用于WebSocket连接
    QUrl url(API_BASE_URL + "/api/v3/userDataStream");
    QUrlQuery query;

    // 发送POST请求，不需要签名，但需要API密钥
    sendRequest(url, query, RequestType::CREATE_LISTEN_KEY, QByteArray("POST"), QByteArray(), false);
}

// 延长listenKey有效期
void ApiManager::keepAliveListenKey()
{
    // 延长listenKey有效期
    if (m_listenKey.isEmpty())
    {
        qDebug() << "错误：listenKey为空，无法延长有效期";
        return;
    }

    QUrl url(API_BASE_URL + "/api/v3/userDataStream");
    QUrlQuery query;
    query.addQueryItem("listenKey", m_listenKey);

    // 发送PUT请求，不需要签名，但需要API密钥
    sendRequest(url, query, RequestType::KEEP_ALIVE_LISTEN_KEY, QByteArray("PUT"), QByteArray(), false);
}

// 处理WebSocket连接成功
void ApiManager::onWebSocketConnected()
{
    qDebug().noquote() << "WebSocket连接成功: \"" << m_webSocketClient->requestUrl().toString() << "\"";
    m_isWebSocketConnected = true;

    // 启动保活定时器，每30分钟延长一次listenKey的有效期
    if (!m_keepAliveTimer->isActive())
    {
        m_keepAliveTimer->start(30 * 60 * 1000); // 30分钟
    }
}

// 处理WebSocket断开连接
void ApiManager::onWebSocketDisconnected()
{
    qDebug().noquote() << "WebSocket断开连接";
    m_isWebSocketConnected = false;

    // 停止保活定时器
    if (m_keepAliveTimer->isActive())
    {
        m_keepAliveTimer->stop();
    }

    // 如果不是主动断开连接，尝试重新连接
    if (!m_listenKey.isEmpty())
    {
        qDebug().noquote() << "尝试重新连接WebSocket...";
        // 修正URL格式，确保在WS_BASE_URL和listenKey之间添加斜杠
        QString wsUrl = WS_BASE_URL + "/" + m_listenKey;
        m_webSocketClient->open(QUrl(wsUrl));
    }
}

// 处理WebSocket错误
void ApiManager::onWebSocketError(QAbstractSocket::SocketError error)
{
    qDebug().noquote() << "WebSocket错误: \"" << m_webSocketClient->errorString() << "\"";

    // 如果是因为listenKey过期导致的错误，重新创建listenKey
    if (error == QAbstractSocket::RemoteHostClosedError)
    {
        qDebug().noquote() << "远程主机关闭连接，可能是listenKey过期，重新创建listenKey";
        m_listenKey.clear();
        createListenKey();
    }

    // 发送网络错误信号，通知其他组件检查订单状态而不是简单提示错误
    QJsonObject errorInfo;
    errorInfo["errorType"] = "WebSocket";
    errorInfo["errorCode"] = static_cast<int>(error);
    errorInfo["errorMessage"] = m_webSocketClient->errorString();

    // 发送特殊的订单更新信号，主窗口会处理这个信号并检查订单状态
    emit orderUpdateReceived(errorInfo);
}

// 处理WebSocket接收到的文本消息
void ApiManager::onWebSocketTextMessageReceived(const QString &message)
{
    // qDebug().noquote() << "收到WebSocket消息: \"" << message << "\"";

    // 解析JSON消息
    QJsonDocument jsonDoc = QJsonDocument::fromJson(message.toUtf8());
    if (jsonDoc.isNull() || !jsonDoc.isObject())
    {
        qDebug().noquote() << "无效的JSON消息";
        return;
    }

    QJsonObject jsonObj = jsonDoc.object();
    QString eventType = jsonObj.value("e").toString();

    // 检查消息类型
    if (jsonObj.contains("e"))
    {
        QString eventType = jsonObj["e"].toString();

        // 处理订单更新事件
        if (eventType == "executionReport")
        {
            // 发出订单更新信号
            // emit orderUpdateReceived(jsonObj);
            qDebug() << "接到事件................";
            QString status = jsonObj.value("X").toString(); // 订单状态
            QString symbol = jsonObj.value("s").toString();
            QString side = jsonObj.value("S").toString();
            qint64 updateTime = jsonObj["T"].toVariant().toLongLong();
            QString clientOrderId = jsonObj["c"].toString();
            QString orderId = QString::number(jsonObj.value("i").toVariant().toLongLong());
            QString orderType = jsonObj["o"].toString();
            double price = jsonObj.value("p").toString().toDouble();    // 最新成交价格
            double quantity = jsonObj.value("q").toString().toDouble(); // 最新成交数量
            // qDebug() << status << symbol << orderId << price << quantity;
            emit orderStatusUpdated(orderId, symbol, side, status, updateTime);
            // 打印订单更新信息
            qDebug().noquote() << "订单更新事件:";
            qDebug().noquote() << "交易对: \"" << symbol << "\"";
            qDebug().noquote() << "订单ID: \"" << orderId << "\"";
            qDebug().noquote() << "客户端订单ID: \"" << clientOrderId << "\"";
            qDebug().noquote() << "订单方向: \"" << side << "\"";
            qDebug().noquote() << "订单类型: \"" << orderType << "\"";
            qDebug().noquote() << "订单状态: \"" << status << "\"";
            qDebug().noquote() << "成交数量: \"" << quantity << "\"";
            qDebug().noquote() << "成交金额: \"" << price << "\"";
        }
        // 处理账户更新事件
        else if (eventType == "outboundAccountPosition")
        {
            // 可以在这里处理账户余额更新
            QString updateTime = QDateTime::fromMSecsSinceEpoch(jsonObj["u"].toVariant().toLongLong()).toString("yyyy年MM月dd日hh:mm:ss");
            QJsonArray arr = jsonObj["B"].toArray();
            double usdc;
            double usdt;
            double usdc_locked;
            double usdt_locked;
            bool updateUsdc = false, updateUsdt = false;
            for (int i = 0; i < arr.size(); i++)
            {
                if (arr[i].toObject()["a"].toString() == "USDC")
                {
                    // 更新USDC
                    usdc = arr[i].toObject()["f"].toString().toDouble();
                    usdc_locked = arr[i].toObject()["l"].toString().toDouble();
                    updateUsdc = true;
                }
                if (arr[i].toObject()["a"].toString() == "USDT")
                {
                    // 更新USDT
                    usdt = arr[i].toObject()["f"].toString().toDouble();
                    usdt_locked = arr[i].toObject()["l"].toString().toDouble();
                    updateUsdt = true;
                }

                if (updateUsdc && updateUsdt)
                {
                    // 如果都更新完了就不需要遍历了
                    emit accountInfoUpdateForWebsocket(usdc, usdc_locked, usdt, usdt_locked, updateTime);
                    break;
                }
            }
        }
        // 处理余额更新事件
        else if (eventType == "balanceUpdate")
        {
            // 可以在这里处理余额更新
        }
    }
}

// 处理listenKey保活定时器超时
void ApiManager::onKeepAliveTimerTimeout()
{
    qDebug().noquote() << "保活定时器触发，延长listenKey有效期";

    // 延长listenKey有效期
    if (!m_listenKey.isEmpty())
    {
        keepAliveListenKey();
    }
    else
    {
        qDebug().noquote() << "listenKey为空，无法延长有效期";
        // 如果listenKey为空，尝试重新创建
        createListenKey();
    }
}

// 连接用户数据WebSocket
void ApiManager::connectUserDataWebSocket()
{
    if (m_listenKey.isEmpty())
    {
        qWarning() << "无法连接WebSocket，ListenKey为空";
        return;
    }

    if (!m_webSocketClient)
    {
        qWarning() << "无法连接WebSocket，WebSocketClient未初始化";
        return;
    }

    // 修正URL拼接，确保格式正确
    QString wsUrl = WS_BASE_URL + "/" + m_listenKey;
    qDebug() << "连接用户数据WebSocket: " << wsUrl;

    m_webSocketClient->open(QUrl(wsUrl));
    m_webSocketClient->connectUserStreamWebSocket(wsUrl);
}

// 实现新增的WebSocket相关槽函数
void ApiManager::onWebSocketReconnectAttempt(int attempt, int maxAttempts)
{
    // qDebug().noquote() << "WebSocket正在尝试重连 (" << attempt << "/" << maxAttempts << ")";

    // 每次重连尝试前，确保listenKey依然有效
    if (!m_listenKey.isEmpty())
    {
        keepAliveListenKey();
    }
    else
    {
        // 如果listenKey为空，需要创建新的
        createListenKey();
    }
}

void ApiManager::onWebSocketReconnected()
{
    qDebug().noquote() << "WebSocket重连成功";
    m_isWebSocketConnected = true;

    // 重连成功后，确保listenKey依然有效
    if (!m_listenKey.isEmpty())
    {
        keepAliveListenKey();
    }
}

void ApiManager::onWebSocketHeartbeatSent()
{
    // 发送心跳的同时，也延长listenKey的有效期
    if (!m_listenKey.isEmpty())
    {
        // 我们不需要每次都调用keepAliveListenKey，可以根据上次调用时间来决定
        // 这里简化处理，每5次心跳调用一次keepAliveListenKey
        static int heartbeatCount = 0;
        if (++heartbeatCount >= 5)
        {
            keepAliveListenKey();
            heartbeatCount = 0;
        }
    }
}

void ApiManager::onWebSocketHeartbeatReceived()
{
    // 记录最后一次接收到心跳响应的时间
    static QDateTime lastHeartbeatReceived = QDateTime::currentDateTime();
    QDateTime now = QDateTime::currentDateTime();

    // 计算距离上次接收心跳响应的时间
    qint64 msSinceLastHeartbeat = lastHeartbeatReceived.msecsTo(now);
    lastHeartbeatReceived = now;

    // 如果收到心跳响应，表明listenKey仍然有效，可以考虑延长listenKey
    if (!m_listenKey.isEmpty() && msSinceLastHeartbeat > 10 * 60 * 1000)
    { // 如果超过10分钟才收到下一次心跳
        keepAliveListenKey();
    }
}

// 添加订阅BookTicker流的方法
void ApiManager::subscribeBookTickerStream(const QString &symbol)
{
    // 检查WebSocket是否已连接
    if (m_webSocketClient->state() != QAbstractSocket::ConnectedState)
    {
        qDebug() << "WebSocket未连接，尝试连接后再订阅BookTicker流";
        // 确保WebSocket已打开
        QUrl url(WS_BASE_URL);
        m_webSocketClient->open(url);

        // 使用延时，确保连接成功后再订阅
        QTimer::singleShot(2000, this, [this, symbol]()
                           {
            if (m_webSocketClient->state() == QAbstractSocket::ConnectedState) {
                m_webSocketClient->subscribeBookTickerStream(symbol);
                qDebug() << "延时后成功订阅BookTicker流:" << symbol;
            } else {
                qDebug() << "WebSocket连接失败，无法订阅BookTicker流:" << symbol;
            } });
    }
    else
    {
        // WebSocket已连接，直接订阅
        m_webSocketClient->subscribeBookTickerStream(symbol);
        qDebug() << "直接订阅BookTicker流:" << symbol;
    }
}

// 添加取消订阅BookTicker流的方法
void ApiManager::unsubscribeBookTickerStream(const QString &symbol)
{
    // 直接取消订阅，无论WebSocket是否连接
    m_webSocketClient->unsubscribeBookTickerStream(symbol);
    qDebug() << "取消订阅BookTicker流:" << symbol;
}

// 添加处理WebSocket BookTicker数据的方法
void ApiManager::onWebSocketBookTickerReceived(const QJsonObject &bookTickerData)
{
    // 将接收到的BookTicker数据转发出去
    emit bestOrderBookTickerUpdated(bookTickerData);

    // 输出日志（可选，用于调试）
    // QString symbol = bookTickerData["symbol"].toString();
    // QString bidPrice = bookTickerData["bidPrice"].toString();
    // QString askPrice = bookTickerData["askPrice"].toString();
    // qDebug() << "通过WebSocket收到BookTicker数据: 交易对=" << symbol << ", 买一价=" << bidPrice << ", 卖一价=" << askPrice;
}

QJsonObject ApiManager::getApiWeightInfo() const
{
    QJsonObject weightInfo;
    weightInfo["currentWeight"] = m_currentWeight;
    weightInfo["weightLimit"] = m_weightLimit;
    weightInfo["orderCount"] = m_orderCount;
    weightInfo["weightPercentage"] = (m_weightLimit > 0) ? (static_cast<double>(m_currentWeight) / m_weightLimit) * 100.0 : 0.0;
    return weightInfo;
}

QJsonObject ApiManager::getDetailedApiWeightInfo() const
{
    QJsonObject weightInfo;

    // 1分钟权重信息
    weightInfo["currentWeight1m"] = m_currentWeight;
    weightInfo["weightLimit1m"] = m_weightLimit;
    weightInfo["weightPercentage1m"] = (m_weightLimit > 0) ? (static_cast<double>(m_currentWeight) / m_weightLimit) * 100.0 : 0.0;

    // 5分钟权重信息
    weightInfo["currentWeight5m"] = m_currentWeight5m;
    weightInfo["weightLimit5m"] = m_weightLimit5m;
    weightInfo["weightPercentage5m"] = (m_weightLimit5m > 0) ? (static_cast<double>(m_currentWeight5m) / m_weightLimit5m) * 100.0 : 0.0;

    // 1小时权重信息
    weightInfo["currentWeight1h"] = m_currentWeight1h;
    weightInfo["weightLimit1h"] = m_weightLimit1h;
    weightInfo["weightPercentage1h"] = (m_weightLimit1h > 0) ? (static_cast<double>(m_currentWeight1h) / m_weightLimit1h) * 100.0 : 0.0;

    // 1天权重信息
    weightInfo["currentWeight1d"] = m_currentWeight1d;
    weightInfo["weightLimit1d"] = m_weightLimit1d;
    weightInfo["weightPercentage1d"] = (m_weightLimit1d > 0) ? (static_cast<double>(m_currentWeight1d) / m_weightLimit1d) * 100.0 : 0.0;

    // 订单计数
    weightInfo["orderCount"] = m_orderCount;

    return weightInfo;
}
