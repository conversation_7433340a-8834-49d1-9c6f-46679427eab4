{"configurations": [{"name": "Qt MSVC2019 64bit", "includePath": ["${workspaceFolder}/**", "D:/StudyAndWork/Qt/6.5.3/msvc2019_64/include/**", "D:/StudyAndWork/Qt/6.5.3/msvc2019_64/include/QtCore/**", "D:/StudyAndWork/Qt/6.5.3/msvc2019_64/include/QtGui/**", "D:/StudyAndWork/Qt/6.5.3/msvc2019_64/include/QtWidgets/**", "D:/StudyAndWork/Qt/6.5.3/msvc2019_64/include/QtNetwork/**", "D:/StudyAndWork/Qt/6.5.3/msvc2019_64/include/QtWebSockets/**", "${workspaceFolder}/generated_files/moc", "${workspaceFolder}/generated_files/ui", "${workspaceFolder}/build/debug", "${workspaceFolder}/build/release"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "QT_CORE_LIB", "QT_GUI_LIB", "QT_WIDGETS_LIB", "QT_NETWORK_LIB", "QT_WEBSOCKETS_LIB"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "D:/StudyAndWork/VS/VC/Tools/MSVC/14.38.33130/bin/Hostx64/x64/cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64", "configurationProvider": "ms-vscode.cmake-tools"}], "version": 4}