cmake_minimum_required(VERSION 3.16)

project(Binance VERSION 0.1 LANGUAGES CXX)

# 强制使用Qt 6.5.3而不是Anaconda的Qt5
set(CMAKE_PREFIX_PATH "D:/StudyAndWork/Qt/6.5.3/msvc2019_64")

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Widgets Network WebSockets Concurrent Multimedia Sql)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Widgets Network WebSockets Concurrent Multimedia Sql)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/../Binance)
set(PROJECT_SOURCES
        main.cpp
        mainwindow.cpp
        mainwindow.h
        mainwindow.ui
        loginwindow.cpp
        loginwindow.h
        loginwindow.ui
        apimanager.cpp
        apimanager.h
        apiworker.cpp
        apiworker.h
        accountinfo.cpp
        accountinfo.h
        utils.cpp
        utils.h
        currentorder.cpp
        currentorder.h
        websocketclient.cpp
        websocketclient.h
        marketstrengthanalyzer.cpp
        marketstrengthanalyzer.h
        orderqueuepositiontracker.cpp
        orderqueuepositiontracker.h
        databasemanager.cpp
        databasemanager.h
)

# 设置编译选项以支持 UTF-8
if(MSVC)
  add_compile_options("/utf-8") # MSVC 编译器选项
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
  add_compile_options("-finput-charset=UTF-8" "-fexec-charset=UTF-8")
endif()

# 为 Windows 创建 .rc 文件
if(WIN32)
    file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/app.rc "IDI_ICON1 ICON DISCARDABLE \"${CMAKE_CURRENT_SOURCE_DIR}/USDTIcons-001.ico\"\n")
    list(APPEND PROJECT_SOURCES ${CMAKE_CURRENT_BINARY_DIR}/app.rc)
endif()

if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_add_executable(Binance
        MANUAL_FINALIZATION
        ${PROJECT_SOURCES}
        res.qrc

    )
    # 为 macOS 设置图标
    if(APPLE)
        set(MACOSX_BUNDLE_ICON_FILE USDTIcons-001.icns)
        set_source_files_properties(USDTIcons-001.icns PROPERTIES MACOSX_PACKAGE_LOCATION "Resources")
        target_sources(Binance PRIVATE USDTIcons-001.icns)
    endif()
else()
    if(ANDROID)
        add_library(Binance SHARED
            ${PROJECT_SOURCES}
        )
    else()
        add_executable(Binance
            ${PROJECT_SOURCES}
        )
        # 为 macOS 设置图标
        if(APPLE)
            set(MACOSX_BUNDLE_ICON_FILE USDTIcons-001.icns)
            set_source_files_properties(USDTIcons-001.icns PROPERTIES MACOSX_PACKAGE_LOCATION "Resources")
            target_sources(Binance PRIVATE USDTIcons-001.icns)
        endif()
    endif()
endif()

target_link_libraries(Binance PRIVATE
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Network
    Qt${QT_VERSION_MAJOR}::WebSockets
    Qt${QT_VERSION_MAJOR}::Concurrent
    Qt${QT_VERSION_MAJOR}::Multimedia
    Qt${QT_VERSION_MAJOR}::Sql
)

# Qt for iOS sets MACOSX_BUNDLE_GUI_IDENTIFIER automatically since Qt 6.1.
# If you are developing for iOS or macOS you should consider setting an
# explicit, fixed bundle identifier manually though.
if(${QT_VERSION} VERSION_LESS 6.1.0)
  set(BUNDLE_ID_OPTION MACOSX_BUNDLE_GUI_IDENTIFIER com.example.Binance)
endif()
set_target_properties(Binance PROPERTIES
    ${BUNDLE_ID_OPTION}
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
)

include(GNUInstallDirs)
install(TARGETS Binance
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if(QT_VERSION_MAJOR EQUAL 6)
    qt_finalize_executable(Binance)
endif()


