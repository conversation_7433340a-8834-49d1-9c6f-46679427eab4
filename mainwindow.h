#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QTableWidgetItem>
#include <QTimer>
#include <QDateTime>
#include <QElapsedTimer>
#include <QMessageBox>
#include <QDialog>
#include <QMediaPlayer>
#include <QAudioOutput>
#include <QProgressBar>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QCloseEvent>
#include <QSet>
#include <QFileDialog>
#include "utils.h"
#include "apiworker.h"
#include "accountinfo.h"
#include "currentorder.h"
#include "marketstrengthanalyzer.h"
#include "orderqueuepositiontracker.h"
#include "websocketclient.h"
#include "databasemanager.h"

QT_BEGIN_NAMESPACE
namespace Ui
{
    class MainWindow;
}
QT_END_NAMESPACE

// 前向声明
class LoginDialog;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

    // 设置API工作对象
    void setApiWorker(ApiWorker *apiWorker);

    // 重写关闭事件处理函数
    void closeEvent(QCloseEvent *event) override;

    // 订阅特定价格的交易数据
    bool subscribeToPriceTrade(const QString &symbol, double price);

    // 取消订阅
    bool unsubscribeFromPriceTrade();

    // 数据库相关方法
    void initDatabase();
    void setupDbTableWidget();
    QueryFilter buildQueryFilter();
    void updateDbTableWidget(const QList<QVariantMap> &results);
    void fillDbTableRow(int row, const QVariantMap &record);
    bool exportToExcel(const QString &fileName);
    void setupDbFilterConnections();

signals:
    // API相关信号
    void validateApiKeys(const QString &apiKey, const QString &secretKey);
    void requestAccountInfo();
    void requestBestOrderBookTicker(const QString &symbol);
    void subscribeBookTickerStream(const QString &symbol);
    void unsubscribeBookTickerStream(const QString &symbol);
    void subscribeTradeDataStream(const QString &symbol);
    void unsubscribeTradeDataStream(const QString &symbol);
    void requestLatestPrice(const QString &symbol);

    // 请求下单
    void requestPlaceOrder(const QString &symbol, const QString &side, const QString &type, const QString &quantity, const QString &price,
                           const QString &relatedOrderId = QString());

    // 请求取消订单
    void requestCancelOrder(const QString &symbol, const QString &orderId);

    // 请求获取所有挂单
    void requestOpenOrders(const QString &symbol);

    // 请求获取订单簿深度数据
    void requestOrderBookDepth(const QString &symbol, int limit = 20);

    // 订单成交信号
    void orderFilled(const QString &side, const QString &quantity, double price);

private slots:
    // 处理API密钥验证结果
    void handleApiKeysValidated(bool valid, const QString &errorMessage);

    // 处理账户信息更新
    void handleAccountInfoUpdated(const QJsonObject &accountInfo);
    void handleAccountInfoUpdatedForWebsocket(double usdc, double usdc_locked, double usdt, double usdt_locked, QString updateTime);

    // 处理WebSocket交易数据
    void handleTradeDataReceived(const QJsonObject &tradeData);

    // 处理USDT余额更新
    void handleUsdtBalanceUpdated(const QString &balance);

    // 处理USDC余额更新
    void handleUsdcBalanceUpdated(const QString &balance);

    // 处理USDT可用余额更新
    void handleUsdtFreeBalanceUpdated(const QString &balance);

    // 处理USDC可用余额更新
    void handleUsdcFreeBalanceUpdated(const QString &balance);

    // 处理USDT锁定余额更新
    void handleUsdtLockedBalanceUpdated(const QString &balance);

    // 处理USDC锁定余额更新
    void handleUsdcLockedBalanceUpdated(const QString &balance);

    // 处理最佳挂单价格更新
    void handleBestOrderBookTickerUpdated(const QJsonObject &ticker);

    // 处理最新成交价格更新
    void handleLatestPriceUpdated(const QJsonObject &priceInfo);

    // 处理下单结果
    void handleOrderPlaced(bool success, const QJsonObject &orderInfo, const QString &errorMessage);

    // 处理取消订单结果
    void handleOrderCancelled(bool success, const QJsonObject &orderInfo, const QString &errorMessage);

    // 处理订单状态更新
    void handleOrderStatusUpdated(QString orderId, QString symbol, QString side, QString status, qint64 updateTime);

    // 处理所有挂单信息
    void handleOpenOrdersReceived(bool success, const QJsonArray &orders, const QString &errorMessage);

    // 处理WebSocket接收到的实时订单更新
    void handleOrderUpdateReceived(const QJsonObject &orderUpdate);

    // 处理订单状态变化
    void handleOrderStatusChanged(CurrentOrderStatus status);

    // 处理买单成交
    void handleBuyOrderFilled(const QString &orderId, double price, const QString &quantity);

    // 处理订单取消 - 添加接收字符串参数的版本，用于处理来自CurrentOrder的信号
    void handleOrderCancelled(const QString &orderId);

    // 处理套利完成
    void handleArbitrageCompleted(const QString &buyOrderId, const QString &sellOrderId, double buyPrice, double sellPrice, double amount, double profit,
                                  bool isStopLoss = false);

    // 刷新账户信息
    void refreshAccountInfo();

    // 刷新价格信息
    void refreshPriceInfo();

    // 退出菜单项点击
    void on_actionExit_triggered();

    // 关于菜单项点击
    void on_actionAbout_triggered();

    // 下单按钮点击
    void on_btn_order_clicked();

    // 结束套利按钮点击（原撤单按钮）
    void on_btn_cancle_clicked();

    // 检查币安平台上的挂单
    void checkBinanceOpenOrders();

    // 继续下单流程
    void proceedWithOrder();

    // 更新UI显示方法
    void updateAccountInfo();
    void updateArbitrageStatus();

    // 处理自动登录
    void onAutoLogin();

    // 修改套利超时处理槽函数
    void onArbitrageTimeout();

    // 处理订单簿深度数据更新
    void handleOrderBookDepthUpdated(const QJsonObject &depthData);

    // 更新市场信息
    void updateMarketInfo();

    // 查询API权重信息并输出到控制台
    void checkApiWeightInfo();

    // 设置API权重输出阈值
    void setApiWeightOutputThreshold(int threshold) { m_apiWeightOutputThreshold = threshold; }

    // 获取API权重输出阈值
    int getApiWeightOutputThreshold() const { return m_apiWeightOutputThreshold; }

private slots:
    // 数据库相关槽函数
    void on_btn_querry_clicked();
    void on_btn_exportEXCEL_clicked();
    void onDbFilterCheckBoxToggled();

private:
    // UI初始化方法
    void initUI();
    void initMenu();
    void initSignalsAndSlots();
    void initHistoryTable();
    void updateMs(quint64 ms);
    // 止损相关方法
    void handleFirstPriceDrop();
    void handleSecondPriceDrop();
    void executeFirstDropStopLoss();
    void executeSecondDropStopLoss();
    void onStopLossTimeout();
    void resetStopLossState();
    // 订阅特定价格的交易数据
    void subscribeToTrades(const QString &symbol, double price);

    // 订单事件处理辅助方法
    bool isEventProcessed(const QString &eventKey);
    void cleanupProcessedEvents();

    // 更新UI显示方法
    void updateAccountInfoDisplay();
    void updatePriceInfoDisplay();
    void clearCurrentOrderDisplay();
    void updateCurrentOrderDisplay();

    // 断开所有连接的方法
    void disconnectAllConnections();

    // 添加历史记录
    void addHistoryRecord(const QString &orderId, const QDateTime &startTime, const QDateTime &arbitrageStartTime, const QDateTime &arbitrageEndTime,
                          const QString &status, double buyPrice, double sellPrice, double amount, double profit);

    // 显示订单信息
    void displayOrderInfo(const QJsonObject &orderInfo);

    // 设置初始UI
    void setupInitialUI();

    // 获取友好的错误信息
    QString getFriendlyErrorMessage(const QString &errorMessage);

    // 套利完成后重置软件状态，但保留历史记录
    void resetAfterArbitrage();

    // 音频播放相关
    QMediaPlayer *m_gettingMoneySoundPlayer = nullptr;
    QMediaPlayer *m_successSoundPlayer = nullptr;
    QAudioOutput *m_audioOutput_success = nullptr;
    QAudioOutput *m_audioOutput_gettingMoney = nullptr;

    // 初始化音频播放器
    void initSoundPlayers();

    Ui::MainWindow *ui;
    ApiWorker *m_apiWorker;
    AccountInfo *m_accountInfo;

    // 等待对话框
    QDialog *m_waitDialog;

    // 表格控件指针
    QTableWidget *m_historyTable; // 将指向UI文件中的historyTableWidget

    // 刷新定时器
    QTimer *m_refreshTimer;             // 账户信息刷新定时器 （只执行一次）
    QTimer *m_priceRefreshTimer;        // 价格信息刷新定时器（只执行一次）
    QTimer *m_arbitrageTimer;           // 套利超时定时器
    bool isFirstRefreshAccount = false; // 是否初始化账户信息（这个和m_refreshTimer关联，只用于初始化账户信息，之后采用websocket订阅）
    // 当前显示的订单ID
    QString m_currentOrderId;

    // 当前订单参数
    QString m_pendingOrderSymbol;
    QString m_pendingOrderSide;
    QString m_pendingOrderType;
    QString m_pendingOrderQuantity;
    QString m_pendingOrderPrice;
    QString m_lastOrderPrice;
    bool m_isPlacingOrder = false;

    // 价格信息
    QString m_bidPrice;    // 买一价
    QString m_askPrice;    // 卖一价
    QString m_latestPrice; // 最新成交价
    bool m_isBuyerMaker;   // 买方做市标志

    // 上次订单信息
    QJsonObject m_lastOrderInfo;
    QString m_lastOrderSymbol;
    QString m_lastOrderSide;
    QString m_lastOrderType;
    QString m_lastOrderQuantity;
    QString m_lastOrderStatus;
    QString m_lastOrderExecutedQty;
    QString m_lastOrderCummulativeQuoteQty;
    double m_lastOrderActualPrice = 0.0;
    QDateTime m_lastOrderTime;
    QDateTime m_lastOrderFillTime;

    // 止损相关
    QTimer *m_stopLossTimer = nullptr;   // 止损计时器
    QString m_lastAskPrice;              // 记录上一次的卖一价，用于检测下降
    bool m_firstStopLoss = false;        // 第一次价格下降标记
    bool m_secondStopLoss = false;       // 第二次价格下降标记
    double m_currentStopLossValue = 0.0; // 当前止损价格差值

    // 已处理过的订单事件集合
    QSet<QString> m_processedOrderEvents;

    // 记录订单状态查询失败的计数
    int m_orderStatusQueryFailCount = 0;
    // 取消订单失败次数
    int m_orderCancledFailCount = 0;
    // 最大允许的查询失败次数，超过此数才显示提示
    const int MAX_ORDER_STATUS_QUERY_FAIL_COUNT = 3;
    // 记录上一次显示错误消息的时间
    QDateTime m_lastErrorMessageTime;

    // 买一价上涨重新下单相关变量
    bool m_waitForNewOrder = false;     // 标记是否在等待撤单后重新下单
    double m_lastTotalInvestment = 0.0; // 记录原始订单的总投资金额

    // 订单重试相关变量
    int m_orderRetryCount = 0; // 记录网络错误时下单重试次数

    // 超时相关变量
    QDateTime m_timeoutTime; // 存储套利超时的具体时间点

    bool orderDialogShowFlag = true; // 是否需要显示下单对话框

    // 市场强度和订单队列位置UI控件
    QLabel *m_marketStrengthLabel;            // 市场强度标签
    QLabel *m_queuePositionLabel;             // 队列位置标签
    QProgressBar *m_queuePositionProgressBar; // 队列位置进度条

    // 是否使用WebSocket获取BookTicker数据（用于控制是否仍需要使用REST API）
    bool m_useWebSocketForBookTicker;

    // 是否使用WebSocket获取最新成交价格（用于控制是否仍需要使用REST API）
    bool m_useWebSocketForLatestPrice;

    // 卖单创建确认等待标志
    bool m_waitingForSellOrderConfirmation = false;

    // API权重检查定时器
    QTimer *m_apiWeightCheckTimer = nullptr;

    // API权重输出阈值（默认300）
    int m_apiWeightOutputThreshold = 300;

    // 数据库管理器
    DatabaseManager *m_databaseManager = nullptr;
};
#endif // MAINWINDOW_H
