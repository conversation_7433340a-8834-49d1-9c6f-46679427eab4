<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1513</width>
    <height>912</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>币安量化交易系统</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_5">
    <item row="0" column="0">
     <layout class="QGridLayout" name="gridLayout" rowstretch="0,1">
      <item row="0" column="0">
       <layout class="QGridLayout" name="gridLayout_3">
        <item row="2" column="0">
         <widget class="QGroupBox" name="priceGroupBox">
          <property name="title">
           <string>USDC/USDT 价格信息</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <item>
            <layout class="QHBoxLayout" name="bidLayout">
             <item>
              <widget class="QLabel" name="bidTitleLabel">
               <property name="text">
                <string>买一价：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="bidPriceLabel">
               <property name="font">
                <font>
                 <family>Microsoft YaHei</family>
                 <pointsize>12</pointsize>
                 <italic>false</italic>
                 <bold>false</bold>
                </font>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="askLayout">
             <item>
              <widget class="QLabel" name="askTitleLabel">
               <property name="text">
                <string>卖一价：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="askPriceLabel">
               <property name="font">
                <font>
                 <family>Microsoft YaHei</family>
                 <pointsize>12</pointsize>
                 <italic>false</italic>
                 <bold>false</bold>
                </font>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="latestLayout">
             <item>
              <widget class="QLabel" name="latestTitleLabel">
               <property name="text">
                <string>最新成交价：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="latestPriceLabel">
               <property name="font">
                <font>
                 <family>Microsoft YaHei</family>
                 <pointsize>12</pointsize>
                 <italic>false</italic>
                 <bold>false</bold>
                </font>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QGroupBox" name="accountGroupBox">
          <property name="title">
           <string>账户信息</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_6" columnstretch="0,1">
           <item row="0" column="1">
            <layout class="QGridLayout" name="gridLayout_7">
             <item row="0" column="0">
              <widget class="QLabel" name="usdcTitleLabel">
               <property name="text">
                <string>USDC余额：</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLabel" name="usdcBalanceLabel">
               <property name="font">
                <font>
                 <family>Microsoft YaHei</family>
                 <pointsize>12</pointsize>
                 <italic>false</italic>
                 <bold>false</bold>
                </font>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
             <item row="0" column="2">
              <widget class="QLabel" name="usdcFreeLabel">
               <property name="text">
                <string>USDC可用余额：</string>
               </property>
              </widget>
             </item>
             <item row="0" column="3">
              <widget class="QLabel" name="usdcFreeBalanceLabel">
               <property name="font">
                <font>
                 <family>Microsoft YaHei</family>
                 <pointsize>12</pointsize>
                 <italic>false</italic>
                 <bold>false</bold>
                </font>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
             <item row="0" column="4">
              <widget class="QLabel" name="usdcLockedLabel">
               <property name="text">
                <string>USDC锁定余额：</string>
               </property>
              </widget>
             </item>
             <item row="0" column="5">
              <widget class="QLabel" name="usdcLockedBalanceLabel">
               <property name="font">
                <font>
                 <family>Microsoft YaHei</family>
                 <pointsize>12</pointsize>
                 <italic>false</italic>
                 <bold>false</bold>
                </font>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="1" column="1">
            <layout class="QHBoxLayout" name="usdtLayout">
             <item>
              <widget class="QLabel" name="usdtTitleLabel">
               <property name="text">
                <string>USDT余额：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="usdtBalanceLabel">
               <property name="font">
                <font>
                 <family>Microsoft YaHei</family>
                 <pointsize>12</pointsize>
                 <italic>false</italic>
                 <bold>false</bold>
                </font>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="usdtFreeLabel">
               <property name="text">
                <string>USDT可用余额：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="usdtFreeBalanceLabel">
               <property name="font">
                <font>
                 <family>Microsoft YaHei</family>
                 <pointsize>12</pointsize>
                 <italic>false</italic>
                 <bold>false</bold>
                </font>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="usdtLockedLabel">
               <property name="text">
                <string>USDT锁定余额：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="usdtLockedBalanceLabel">
               <property name="font">
                <font>
                 <family>Microsoft YaHei</family>
                 <pointsize>12</pointsize>
                 <italic>false</italic>
                 <bold>false</bold>
                </font>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label">
             <property name="text">
              <string>更新时间</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_accountUpdateTime">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="3" column="0">
         <layout class="QGridLayout" name="gridLayout_2">
          <item row="0" column="5">
           <widget class="QPushButton" name="btn_order">
            <property name="text">
             <string>下单</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QSpinBox" name="spinBox_price">
            <property name="suffix">
             <string>USDC</string>
            </property>
            <property name="prefix">
             <string>数量</string>
            </property>
            <property name="minimum">
             <number>6</number>
            </property>
            <property name="maximum">
             <number>*********</number>
            </property>
            <property name="singleStep">
             <number>1</number>
            </property>
            <property name="value">
             <number>6</number>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="bidPriceInfoLabel">
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>12</pointsize>
              <italic>false</italic>
              <bold>false</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(64, 26, 255);</string>
            </property>
            <property name="text">
             <string>系统将使用最新买一价作为下单价格</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QCheckBox" name="checkBox_useTimeOut">
            <property name="text">
             <string>设置超时时间</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QDateTimeEdit" name="dateTimeEdit_timeOut">
            <property name="dateTime">
             <datetime>
              <hour>0</hour>
              <minute>0</minute>
              <second>0</second>
              <year>2025</year>
              <month>4</month>
              <day>7</day>
             </datetime>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QCheckBox" name="checkBox_automatic">
            <property name="text">
             <string>全自动套利</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item row="1" column="0">
       <layout class="QGridLayout" name="gridLayout_4">
        <item row="0" column="0">
         <widget class="QSplitter" name="splitter">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <widget class="QWidget" name="verticalLayoutWidget">
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_currentOrder">
              <item>
               <layout class="QVBoxLayout" name="verticalLayout_3">
                <item>
                 <widget class="QLabel" name="label_3">
                  <property name="text">
                   <string>订单号</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_orderId">
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QVBoxLayout" name="verticalLayout_4">
                <item>
                 <widget class="QLabel" name="label_4">
                  <property name="text">
                   <string>数量</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_orderCount">
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QVBoxLayout" name="verticalLayout_5">
                <item>
                 <widget class="QLabel" name="label_6">
                  <property name="text">
                   <string>单价</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_buyPrice">
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QVBoxLayout" name="verticalLayout_6">
                <item>
                 <widget class="QLabel" name="label_8">
                  <property name="text">
                   <string>总金额</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_totalCount">
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QVBoxLayout" name="verticalLayout_7">
                <item>
                 <widget class="QLabel" name="label_10">
                  <property name="text">
                   <string>下单时间</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_orderTime">
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QVBoxLayout" name="verticalLayout_8">
                <item>
                 <widget class="QLabel" name="label_12">
                  <property name="text">
                   <string>订单状态</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_orderStatus">
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QVBoxLayout" name="verticalLayout_9">
                <item>
                 <widget class="QPushButton" name="btn_cancle">
                  <property name="text">
                   <string>结束套利</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_newOrderMsg">
                  <property name="styleSheet">
                   <string notr="true">color: rgb(255, 0, 0);
font: 12pt &quot;Microsoft YaHei UI&quot;;</string>
                  </property>
                  <property name="text">
                   <string>价格波动，正在重新下单...</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QTabWidget" name="tabWidget_history_db">
              <property name="currentIndex">
               <number>0</number>
              </property>
              <widget class="QWidget" name="tab">
               <attribute name="title">
                <string>历史记录</string>
               </attribute>
               <layout class="QGridLayout" name="gridLayout_8">
                <item row="0" column="0">
                 <widget class="QLabel" name="historyTitleLabel">
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>12</pointsize>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>套利历史记录</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QTableWidget" name="historyTableWidget"/>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="tab_2">
               <attribute name="title">
                <string>数据库查询</string>
               </attribute>
               <layout class="QGridLayout" name="gridLayout_9">
                <item row="0" column="0">
                 <layout class="QGridLayout" name="gridLayout_11" columnstretch="0,1">
                  <item row="0" column="0">
                   <layout class="QGridLayout" name="gridLayout_10">
                    <item row="4" column="1">
                     <widget class="QCheckBox" name="checkBox_comboBox_orderStatus_db">
                      <property name="text">
                       <string>启用</string>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0">
                     <widget class="QDateTimeEdit" name="dateTimeEdit_orderStart_db">
                      <property name="dateTime">
                       <datetime>
                        <hour>0</hour>
                        <minute>0</minute>
                        <second>0</second>
                        <year>2025</year>
                        <month>4</month>
                        <day>7</day>
                       </datetime>
                      </property>
                      <property name="displayFormat">
                       <string>订单开始时间yyyy/M/d H:mm</string>
                      </property>
                     </widget>
                    </item>
                    <item row="2" column="0">
                     <widget class="QLineEdit" name="lineEdit_orderID_db">
                      <property name="placeholderText">
                       <string>订单号:</string>
                      </property>
                     </widget>
                    </item>
                    <item row="2" column="1">
                     <widget class="QCheckBox" name="checkBox_lineEdit_orderID_db">
                      <property name="text">
                       <string>启用</string>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <widget class="QDateTimeEdit" name="dateTimeEdit_orderEnd_db">
                      <property name="dateTime">
                       <datetime>
                        <hour>0</hour>
                        <minute>0</minute>
                        <second>0</second>
                        <year>2025</year>
                        <month>4</month>
                        <day>7</day>
                       </datetime>
                      </property>
                      <property name="displayFormat">
                       <string>套利结束时间yyyy/M/d H:mm</string>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="1">
                     <widget class="QCheckBox" name="checkBox_dateTimeEdit_orderStart_db">
                      <property name="text">
                       <string>启用</string>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="1">
                     <widget class="QCheckBox" name="checkBox_dateTimeEdit_orderEnd_db">
                      <property name="text">
                       <string>启用</string>
                      </property>
                     </widget>
                    </item>
                    <item row="3" column="0">
                     <widget class="QDoubleSpinBox" name="doubleSpinBox_price_db">
                      <property name="prefix">
                       <string>交易额</string>
                      </property>
                      <property name="decimals">
                       <number>4</number>
                      </property>
                      <property name="maximum">
                       <double>99999999999999.000000000000000</double>
                      </property>
                     </widget>
                    </item>
                    <item row="4" column="0">
                     <widget class="QComboBox" name="comboBox_orderStatus_db">
                      <property name="placeholderText">
                       <string/>
                      </property>
                      <item>
                       <property name="text">
                        <string>套利完成</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>止损完成</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                    <item row="3" column="1">
                     <widget class="QCheckBox" name="checkBox_doubleSpinBox_price_db">
                      <property name="text">
                       <string>启用</string>
                      </property>
                     </widget>
                    </item>
                    <item row="5" column="0">
                     <widget class="QPushButton" name="btn_exportEXCEL">
                      <property name="text">
                       <string>导出EXCEL</string>
                      </property>
                     </widget>
                    </item>
                    <item row="5" column="1">
                     <widget class="QPushButton" name="btn_querry">
                      <property name="text">
                       <string>查询</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item row="0" column="1">
                   <widget class="QTableWidget" name="dbTableWidget"/>
                  </item>
                 </layout>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout">
              <item>
               <widget class="QLabel" name="label_marketStrengthLabel">
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_queuePositionLabel">
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QProgressBar" name="progressBar_currentPrecent">
                <property name="value">
                 <number>0</number>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
                <property name="invertedAppearance">
                 <bool>false</bool>
                </property>
                <property name="format">
                 <string>%p%</string>
                </property>
               </widget>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_currentMs">
                <item>
                 <widget class="QLabel" name="label_2">
                  <property name="text">
                   <string>当前延迟:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_currentMs">
                  <property name="text">
                   <string>inf</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_ms">
                  <property name="text">
                   <string>ms</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1513</width>
     <height>32</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>文件</string>
    </property>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>帮助</string>
    </property>
    <addaction name="actionAbout"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionExit">
   <property name="text">
    <string>退出</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>关于</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
