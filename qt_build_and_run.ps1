# Qt Build and Run Script
param(
    [string]$BuildType = "debug"
)

Write-Host ""
Write-Host "========================================"
Write-Host "     Qt Build & Run Script"
Write-Host "========================================"
Write-Host ""

# Set Qt path
$QtPath = "Z:\StudyAndWork\Qt\6.5.3\msvc2019_64"

# Check if we should use Huawei computer path
if (Test-Path "computer_config.json") {
    $config = Get-Content "computer_config.json" | ConvertFrom-Json
    if ($config.current_config -eq "huawei_computer") {
        $QtPath = "D:\StudyAndWork\Qt\6.5.3\msvc2019_64"
        Write-Host "Using Huawei Computer configuration"
    } else {
        Write-Host "Using Current Computer configuration"
    }
}

Write-Host "Qt Path: $QtPath"

# Verify Qt installation
if (-not (Test-Path "$QtPath\bin\qmake.exe")) {
    Write-Host "ERROR: Qt installation not found at: $QtPath" -ForegroundColor Red
    Write-Host "Please check your Qt installation or run switch_computer.bat"
    Read-Host "Press Enter to continue"
    exit 1
}

# Set environment variables
$env:CMAKE_PREFIX_PATH = $QtPath
$env:Qt6_DIR = "$QtPath\lib\cmake\Qt6"
$env:PATH = "$QtPath\bin;$env:PATH"

# Create build directory if it doesn't exist
if (-not (Test-Path "build")) {
    New-Item -ItemType Directory -Path "build" | Out-Null
}

# Handle build type
if ($BuildType -eq "clean") {
    Write-Host "Cleaning build directory..."
    if (Test-Path "build") {
        Remove-Item -Recurse -Force "build"
        Write-Host "Clean completed."
    }
    Write-Host ""
    Read-Host "Press Enter to continue"
    exit 0
}

# Build the project
Write-Host "Building $BuildType version..."
Set-Location "build"

try {
    # Configure with CMake
    & cmake -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=$BuildType -DCMAKE_PREFIX_PATH=$QtPath -DQt6_DIR="$QtPath\lib\cmake\Qt6" ..
    
    if ($LASTEXITCODE -ne 0) {
        throw "CMake configuration failed"
    }
    
    # Build with CMake
    & cmake --build . --config $BuildType
    
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    
    Set-Location ".."
    
    # Determine executable path (check multiple possible locations)
    $PossiblePaths = @(
        "build\Binance\$BuildType\Binance.exe",
        "Binance\$BuildType\Binance.exe",
        "build\$BuildType\Binance.exe"
    )

    $ExePath = $null
    foreach ($path in $PossiblePaths) {
        if (Test-Path $path) {
            $ExePath = $path
            break
        }
    }
    
    # Check if build was successful
    if ($ExePath -and (Test-Path $ExePath)) {
        Write-Host ""
        Write-Host "✓ Build successful! Deploying Qt libraries..." -ForegroundColor Green
        Write-Host "✓ Executable found at: $ExePath" -ForegroundColor Green

        # Deploy Qt libraries using windeployqt
        $WinDeployQt = "$QtPath\bin\windeployqt.exe"
        if (Test-Path $WinDeployQt) {
            Write-Host "✓ Deploying Qt libraries for $BuildType version..." -ForegroundColor Green

            $deployArgs = @(
                "--$BuildType",
                "--compiler-runtime",
                "--force",
                $ExePath
            )

            try {
                & $WinDeployQt @deployArgs | Out-Host

                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✓ Qt libraries deployed successfully!" -ForegroundColor Green
                } else {
                    Write-Host "⚠ Qt deployment completed with warnings" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "⚠ Qt deployment failed: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "⚠ windeployqt.exe not found, skipping Qt deployment" -ForegroundColor Yellow
        }

        Write-Host ""
        Write-Host "✓ Starting $BuildType version..." -ForegroundColor Green

        # Launch the application
        Start-Process -FilePath $ExePath

        Write-Host "✓ Application launched successfully!" -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "✗ Build failed! Executable not found in any of these locations:" -ForegroundColor Red
        foreach ($path in $PossiblePaths) {
            Write-Host "  - $path" -ForegroundColor Red
        }
        Write-Host ""
    }
    
} catch {
    Set-Location ".."
    Write-Host ""
    Write-Host "✗ Build failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
}

Write-Host ""
Write-Host "========================================"
Write-Host "Build process completed!"
Write-Host "========================================"
Write-Host ""
Read-Host "Press Enter to continue"
