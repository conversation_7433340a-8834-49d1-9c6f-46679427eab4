#include "currentorder.h"
#include "apiworker.h"
#include "websocketclient.h"
#include <QDebug>
#include <QMetaObject>
#include <QTimer>

// 初始化单例实例指针
CurrentOrder *CurrentOrder::m_instance = nullptr;

// 初始化静态常量
const CurrentOrderStatus CurrentOrder::INITIAL;
const CurrentOrderStatus CurrentOrder::BUY_PENDING;
const CurrentOrderStatus CurrentOrder::ARBITRAGING;
const CurrentOrderStatus CurrentOrder::STOP_LOSS_FIRST_DROP;
const CurrentOrderStatus CurrentOrder::STOP_LOSS_SECOND_DROP;

CurrentOrder *CurrentOrder::getInstance()
{
    if (!m_instance)
    {
        m_instance = new CurrentOrder();
    }
    return m_instance;
}

void CurrentOrder::releaseInstance()
{
    if (m_instance)
    {
        delete m_instance;
        m_instance = nullptr;
    }
}

CurrentOrder::CurrentOrder(QObject *parent)
    : QObject(parent), m_apiWorker(nullptr), m_webSocketClient(nullptr), m_status(CurrentOrderStatus::INITIAL), m_buyPrice(0.0), m_sellPrice(0.0),
      m_originalSellPrice(0.0), m_orderAmount(0.0), m_subscribedSymbol(""), m_subscribedPrice(0.0), m_pricePrecision(0.00001), m_isSubscribed(false)
{
    qDebug() << "CurrentOrder单例对象已创建";
}

CurrentOrder::~CurrentOrder()
{
    qDebug() << "CurrentOrder单例对象已销毁";

    // 确保取消订阅
    if (m_isSubscribed && m_webSocketClient)
    {
        unsubscribe();
    }

    // 断开所有信号连接
    if (m_webSocketClient)
    {
        disconnect(m_webSocketClient, nullptr, this, nullptr);
        disconnect(this, nullptr, m_webSocketClient, nullptr);
    }
}

void CurrentOrder::setWebSocketClient(WebSocketClient *webSocketClient)
{
    // 先断开之前的连接
    if (m_webSocketClient)
    {
        disconnect(m_webSocketClient, nullptr, this, nullptr);
        disconnect(this, nullptr, m_webSocketClient, nullptr);
    }

    m_webSocketClient = webSocketClient;

    if (m_webSocketClient)
    {
        // 连接WebSocketClient的信号到CurrentOrder的槽
        connect(m_webSocketClient, &WebSocketClient::subscriptionChanged, this, &CurrentOrder::handleWebSocketSubscriptionChanged, Qt::QueuedConnection);

        // 连接CurrentOrder的信号到WebSocketClient的槽
        connect(this, &CurrentOrder::requestSubscribeTradeStream, m_webSocketClient, &WebSocketClient::subscribeTradeStream, Qt::QueuedConnection);
        connect(this, &CurrentOrder::requestUnsubscribeTradeStream, m_webSocketClient, &WebSocketClient::unsubscribeTradeStream, Qt::QueuedConnection);
        connect(this, &CurrentOrder::requestReconnectWebSocket, m_webSocketClient, &WebSocketClient::reconnectWebSocket, Qt::QueuedConnection);

        qDebug() << "WebSocketClient已设置到CurrentOrder并建立信号槽连接";
    }
}

QString CurrentOrder::getStatusText() const
{
    switch (m_status)
    {
    case CurrentOrderStatus::INITIAL:
        return "初始状态";
    case CurrentOrderStatus::BUY_PENDING:
        return "买入挂单中";
    case CurrentOrderStatus::ARBITRAGING:
        return "套利中";
    case CurrentOrderStatus::STOP_LOSS_FIRST_DROP:
        return "止损中 - 第一次价格下降";
    case CurrentOrderStatus::STOP_LOSS_SECOND_DROP:
        return "止损中 - 第二次价格下降";
    default:
        return "未知状态";
    }
}

QString CurrentOrder::getStatusColor() const
{
    switch (m_status)
    {
    case CurrentOrderStatus::INITIAL:
        return "color: #000000;"; // 黑色
    case CurrentOrderStatus::BUY_PENDING:
        return "color: #009900;"; // 绿色
    case CurrentOrderStatus::ARBITRAGING:
        return "color: #FF0000;"; // 红色
    case CurrentOrderStatus::STOP_LOSS_FIRST_DROP:
        return "color: #FF6600;"; // 橙色
    case CurrentOrderStatus::STOP_LOSS_SECOND_DROP:
        return "color: #FF0000;"; // 红色
    default:
        return "color: #000000;"; // 黑色
    }
}

void CurrentOrder::setStatus(CurrentOrderStatus status)
{
    // 如果状态没有改变，直接返回
    if (m_status == status)
    {
        return;
    }

    // 保存旧状态
    CurrentOrderStatus oldStatus = m_status;

    // 更新状态
    m_status = status;
    qDebug() << "订单状态已更新:" << getStatusText();

    // 发送状态改变信号
    emit statusChanged(status);
}

QString CurrentOrder::formatOrderId(const QVariant &orderIdVariant) const
{
    // 确保订单ID使用标准整数格式而非科学计数法
    bool ok;
    qlonglong id = orderIdVariant.toLongLong(&ok);
    if (ok)
    {
        return QString::number(id);
    }
    return orderIdVariant.toString();
}

bool CurrentOrder::isSameOrderId(const QString &id1, const QString &id2) const
{
    // 比较两个订单ID是否相同，考虑格式差异
    return id1 == id2 || id1.toLongLong() == id2.toLongLong();
}

void CurrentOrder::setBuyOrderInfo(const QJsonObject &orderInfo)
{
    // 检查当前状态，只有在初始状态或买入挂单状态才处理买单信息
    if (m_status == CurrentOrderStatus::ARBITRAGING)
    {
        qDebug() << "当前处于套利中状态，忽略买单信息更新";
        return;
    }

    // 解析订单信息
    m_buyOrderId = formatOrderId(QVariant(orderInfo["orderId"]));
    m_symbol = orderInfo["symbol"].toString();
    m_buyPrice = orderInfo["price"].toString().toDouble();
    m_buyQuantity = orderInfo["origQty"].toString();

    // 使用transactTime字段获取时间戳，如果不存在则尝试使用time字段
    if (orderInfo.contains("transactTime"))
    {
        m_buyOrderTime = QDateTime::fromMSecsSinceEpoch(orderInfo["transactTime"].toVariant().toLongLong());
    }
    else if (orderInfo.contains("time"))
    {
        m_buyOrderTime = QDateTime::fromMSecsSinceEpoch(orderInfo["time"].toVariant().toLongLong());
    }
    else
    {
        // 如果两个字段都不存在，使用当前时间
        m_buyOrderTime = QDateTime::currentDateTime();
        qDebug() << "警告：订单信息中缺少时间戳字段，使用当前时间作为买单时间";
    }

    m_orderAmount = m_buyPrice * m_buyQuantity.toDouble();

    qDebug() << "已设置买入订单信息：订单ID=" << m_buyOrderId << "交易对=" << m_symbol << "价格=" << m_buyPrice << "数量=" << m_buyQuantity;

    // 检查买单状态
    QString status = orderInfo["status"].toString();
    if (status == "FILLED")
    {
        // 如果买单已成交
        qDebug() << "买单已成交，更新订单状态";
        setStatus(CurrentOrderStatus::ARBITRAGING);

        // 发送买单成交信号
        emit buyOrderFilled(m_buyOrderId, m_buyPrice, m_buyQuantity);
    }
    else if (status == "NEW" || status == "PARTIALLY_FILLED")
    {
        // 如果买单是新建或部分成交状态
        setStatus(CurrentOrderStatus::BUY_PENDING);
    }
}

void CurrentOrder::setSellOrderInfo(const QJsonObject &orderInfo)
{
    // 解析订单信息
    m_sellOrderId = formatOrderId(QVariant(orderInfo["orderId"]));
    m_sellPrice = orderInfo["price"].toString().toDouble();
    m_sellQuantity = orderInfo["origQty"].toString();

    // 使用transactTime字段获取时间戳，如果不存在则尝试使用time字段
    if (orderInfo.contains("transactTime"))
    {
        m_sellOrderTime = QDateTime::fromMSecsSinceEpoch(orderInfo["transactTime"].toVariant().toLongLong());
    }
    else if (orderInfo.contains("time"))
    {
        m_sellOrderTime = QDateTime::fromMSecsSinceEpoch(orderInfo["time"].toVariant().toLongLong());
    }
    else
    {
        // 如果两个字段都不存在，使用当前时间
        m_sellOrderTime = QDateTime::currentDateTime();
        qDebug() << "警告：订单信息中缺少时间戳字段，使用当前时间作为卖单时间";
    }

    m_orderAmount = m_sellPrice * m_sellQuantity.toDouble();

    qDebug() << "已设置卖出订单信息：订单ID=" << m_sellOrderId << "交易对=" << m_symbol << "价格=" << m_sellPrice << "数量=" << m_sellQuantity;

    // 检查卖单状态
    QString status = orderInfo["status"].toString();
    if (status == "FILLED")
    {
        // 如果卖单已成交
        qDebug() << "卖单已成交，更新订单状态";

        // 计算利润
        double profit = calculateProfit();

        // 判断是否为止损完成
        bool isStopLoss = (m_status == CurrentOrderStatus::STOP_LOSS_FIRST_DROP || m_status == CurrentOrderStatus::STOP_LOSS_SECOND_DROP);
        if (isStopLoss)
        {
            qDebug() << "止损完成，状态：" << getStatusText();
        }

        // 完成套利，添加isStopLoss参数
        emit arbitrageCompleted(m_buyOrderId, m_sellOrderId, m_buyPrice, m_sellPrice, m_buyQuantity.toDouble(), profit, isStopLoss);

        // 不再在这里调用resetToInitial()，而是在MainWindow::handleArbitrageCompleted中调用
    }
    else if (status == "NEW" || status == "PARTIALLY_FILLED")
    {
        // 如果卖单是新建或部分成交状态
        // 只有当不处于止损状态时，才设置为套利中
        if (m_status != CurrentOrderStatus::STOP_LOSS_FIRST_DROP && m_status != CurrentOrderStatus::STOP_LOSS_SECOND_DROP)
        {
            setStatus(CurrentOrderStatus::ARBITRAGING);
        }
        else
        {
            qDebug() << "当前处于止损状态，保持状态不变：" << getStatusText();
        }
    }
}

double CurrentOrder::calculateProfit() const
{
    if (m_buyPrice <= 0 || m_sellPrice <= 0 || m_buyQuantity.isEmpty())
    {
        return 0.0;
    }

    double buyAmount = m_buyPrice * m_buyQuantity.toDouble();
    double sellAmount = m_sellPrice * m_buyQuantity.toDouble();
    return sellAmount - buyAmount;
}

bool CurrentOrder::cancelCurrentOrder()
{
    // 检查是否有API工作对象
    if (!m_apiWorker)
    {
        qDebug() << "错误：ApiWorker未设置，无法取消订单";
        return false;
    }

    // 根据当前状态取消相应的订单
    switch (m_status)
    {
    case CurrentOrderStatus::BUY_PENDING:
        // 如果处于买入挂单状态，取消买单
        if (!m_buyOrderId.isEmpty() && !m_symbol.isEmpty())
        {
            qDebug() << "正在取消买入订单：订单ID=" << m_buyOrderId << "交易对=" << m_symbol;
            // 使用QMetaObject::invokeMethod确保在正确的线程中执行
            QMetaObject::invokeMethod(m_apiWorker, "cancelOrder", Qt::QueuedConnection, Q_ARG(QString, m_symbol), Q_ARG(QString, m_buyOrderId));
            return true;
        }
        break;

    case CurrentOrderStatus::ARBITRAGING:
    case CurrentOrderStatus::STOP_LOSS_FIRST_DROP:
    case CurrentOrderStatus::STOP_LOSS_SECOND_DROP:
        // 如果处于套利中或止损状态，取消卖单
        if (!m_sellOrderId.isEmpty() && !m_symbol.isEmpty())
        {
            qDebug() << "正在取消卖出订单：订单ID=" << m_sellOrderId << "交易对=" << m_symbol;
            // 使用QMetaObject::invokeMethod确保在正确的线程中执行
            QMetaObject::invokeMethod(m_apiWorker, "cancelOrder", Qt::QueuedConnection, Q_ARG(QString, m_symbol), Q_ARG(QString, m_sellOrderId));
            return true;
        }
        break;

    default:
        qDebug() << "当前状态无需取消订单：" << getStatusText();
        break;
    }

    return false;
}

void CurrentOrder::resetToInitial()
{
    qDebug() << "恢复订单对象到初始状态";

    // 取消订阅
    if (m_isSubscribed)
    {
        unsubscribe();
    }

    // 清空订单信息
    m_buyOrderId.clear();
    m_sellOrderId.clear();
    m_symbol.clear();
    m_buyPrice = 0.0;
    m_sellPrice = 0.0;
    m_originalSellPrice = 0.0;
    m_buyQuantity.clear();
    m_sellQuantity.clear();
    m_buyOrderTime = QDateTime();
    m_sellOrderTime = QDateTime();
    m_sellOrderFillTime = QDateTime();
    m_orderAmount = 0.0;

    // 设置状态为初始状态
    setStatus(CurrentOrderStatus::INITIAL);

    // 清理已处理事件集合
    cleanupProcessedEvents();
}

void CurrentOrder::syncOrderStatusWithBinance(const QJsonArray &openOrders)
{
    // 根据开放订单列表同步订单状态
    bool foundBuyOrder = false;
    bool foundSellOrder = false;

    for (const QJsonValue &orderValue : openOrders)
    {
        QJsonObject order = orderValue.toObject();
        QString orderId = formatOrderId(QVariant(order["orderId"]));

        // 检查是否是买单
        if (!m_buyOrderId.isEmpty() && isSameOrderId(orderId, m_buyOrderId))
        {
            foundBuyOrder = true;
            qDebug() << "找到买单：" << orderId << "当前状态：" << order["status"].toString();
        }

        // 检查是否是卖单
        if (!m_sellOrderId.isEmpty() && isSameOrderId(orderId, m_sellOrderId))
        {
            foundSellOrder = true;
            qDebug() << "找到卖单：" << orderId << "当前状态：" << order["status"].toString();
        }
    }

    // 根据查找结果同步状态
    if (m_status == CurrentOrderStatus::BUY_PENDING && !foundBuyOrder && !m_buyOrderId.isEmpty())
    {
        // 买单不在开放订单列表中，说明可能已成交
        qDebug() << "买单不在开放订单列表中，可能已成交，尝试更新状态";

        // 请求查询订单状态
        if (m_apiWorker)
        {
            // 使用QMetaObject::invokeMethod确保在正确的线程中执行
            QMetaObject::invokeMethod(m_apiWorker, "getOrderStatus", Qt::QueuedConnection, Q_ARG(QString, m_symbol), Q_ARG(QString, m_buyOrderId));
        }
    }

    if (m_status == CurrentOrderStatus::ARBITRAGING && !foundSellOrder && !m_sellOrderId.isEmpty())
    {
        // 卖单不在开放订单列表中，说明可能已成交
        qDebug() << "卖单不在开放订单列表中，可能已成交，尝试更新状态";

        // 请求查询订单状态
        if (m_apiWorker)
        {
            // 使用QMetaObject::invokeMethod确保在正确的线程中执行
            QMetaObject::invokeMethod(m_apiWorker, "getOrderStatus", Qt::QueuedConnection, Q_ARG(QString, m_symbol), Q_ARG(QString, m_sellOrderId));
        }
    }
}

void CurrentOrder::handleBuyOrderFilled(bool success, QString orderStatus, QString orderId, const QString &errorMessage)
{
    if (!success)
    {
        qDebug() << "获取买单状态失败：" << errorMessage;
        return;
    }

    // 检查是否已处理过此订单的成交事件
    QString eventKey = orderId + "_buy_filled";
    if (isEventProcessed(eventKey))
    {
        qDebug() << "买单成交事件已处理过，忽略重复事件：" << orderId;
        return;
    }

    // 检查当前状态，只有在初始状态或买入挂单状态才处理买单成交事件
    if (m_status == CurrentOrderStatus::ARBITRAGING)
    {
        qDebug() << "当前处于套利中状态，忽略买单成交事件";
        return;
    }

    if (orderId == m_buyOrderId && orderStatus == "FILLED")
    {
        // 买单已成交
        qDebug() << "买单已成交，更新状态";
        setStatus(CurrentOrderStatus::ARBITRAGING);

        // 发送买单成交信号
        emit buyOrderFilled(m_buyOrderId, m_buyPrice, m_buyQuantity);
    }
}

void CurrentOrder::handleSellOrderFilled(bool success, QString orderStatus, QString orderId, const QString &errorMessage)
{
    if (!success)
    {
        qDebug() << "获取卖单状态失败：" << errorMessage;
        return;
    }

    // 检查是否已处理过此订单的成交事件
    QString eventKey = orderId + "_sell_filled";
    if (isEventProcessed(eventKey))
    {
        qDebug() << "卖单成交事件已处理过，忽略重复事件：" << orderId;
        return;
    }

    if (orderId == m_sellOrderId && orderStatus == "FILLED")
    {
        // 卖单已成交
        qDebug() << "卖单已成交，更新状态";

        // 计算利润
        double profit = calculateProfit();

        // 判断是否为止损完成
        bool isStopLoss = (m_status == CurrentOrderStatus::STOP_LOSS_FIRST_DROP || m_status == CurrentOrderStatus::STOP_LOSS_SECOND_DROP);
        if (isStopLoss)
        {
            qDebug() << "止损完成，状态：" << getStatusText();
        }

        // 完成套利，添加isStopLoss参数
        emit arbitrageCompleted(m_buyOrderId, m_sellOrderId, m_buyPrice, m_sellPrice, m_buyQuantity.toDouble(), profit, isStopLoss);

        // 不再在这里调用resetToInitial()，而是在MainWindow::handleArbitrageCompleted中调用
    }
}

void CurrentOrder::handleOrderCancelled(bool success, QString orderId, const QString &errorMessage)
{
    if (!success)
    {
        qDebug() << "取消订单失败：" << errorMessage;
        return;
    }

    qDebug() << "订单已取消：" << orderId;

    // 根据当前状态处理
    switch (m_status)
    {
    case CurrentOrderStatus::BUY_PENDING:
        // 如果是买单被取消
        if (orderId == m_buyOrderId)
        {
            // 发送订单取消信号
            emit orderCancelled(m_buyOrderId);

            // 恢复初始状态
            resetToInitial();
        }
        break;

    case CurrentOrderStatus::ARBITRAGING:
        // 如果是卖单被取消
        if (orderId == m_sellOrderId)
        {
            // 发送订单取消信号
            emit orderCancelled(m_sellOrderId);

            // 恢复初始状态
            resetToInitial();
        }
        break;

    case CurrentOrderStatus::STOP_LOSS_FIRST_DROP:
    case CurrentOrderStatus::STOP_LOSS_SECOND_DROP:
        // 如果是止损状态下取消卖单，不重置状态
        // 因为取消后会立即下新的卖单，状态应保持为止损状态
        if (orderId == m_sellOrderId)
        {
            // 只发送订单取消信号，不重置状态
            emit orderCancelled(m_sellOrderId);
        }
        break;

    default:
        // 初始状态或其他未知状态，不做处理
        break;
    }
}

void CurrentOrder::handleOpenOrdersReceived(bool success, const QJsonArray &orders, const QString &errorMessage)
{
    if (!success)
    {
        qDebug() << "获取开放订单失败：" << errorMessage;
        return;
    }

    // 使用开放订单列表同步状态
    syncOrderStatusWithBinance(orders);
}

void CurrentOrder::handleWebSocketSubscriptionChanged(const QString &symbol, bool isSubscribed)
{
    // 检查是否是当前订阅的交易对
    if (symbol.toLower() == m_subscribedSymbol.toLower())
    {
        qDebug() << "WebSocket交易对" << symbol << "的订阅状态变为" << (isSubscribed ? "已订阅" : "已取消订阅");

        // 更新订阅状态
        m_isSubscribed = isSubscribed;

        // 发送订阅状态变化信号
        emit subscriptionStatusChanged(isSubscribed);
    }
}

// 处理WebSocket连接恢复
void CurrentOrder::onWebSocketConnected()
{
    qDebug() << "WebSocket连接已恢复，重新订阅交易数据";

    // 设置延时，确保WebSocket连接稳定后再订阅
    QTimer::singleShot(1000, this, [this]()
                       {
        if (!m_subscribedSymbol.isEmpty() && m_isSubscribed) {
            qDebug() << "WebSocket连接稳定后重新发送订阅请求: " << m_subscribedSymbol;
            emit requestSubscribeTradeStream(m_subscribedSymbol);
        } });
}

// 订阅特定价格的交易数据
bool CurrentOrder::subscribe(const QString &symbol, double price)
{
    // 检查是否已经订阅
    if (m_isSubscribed)
    {
        qDebug() << "警告：已有订阅未取消，先取消之前的订阅";
        unsubscribe();
    }

    // 检查WebSocket客户端是否存在
    if (!m_webSocketClient)
    {
        qDebug() << "错误：WebSocketClient未设置，无法订阅交易数据";
        return false;
    }

    // 保存订阅信息，无论WebSocket是否连接
    m_subscribedSymbol = symbol;
    m_subscribedPrice = price;
    m_isSubscribed = true;

    // 检查WebSocket连接状态
    if (m_webSocketClient->state() != QAbstractSocket::ConnectedState)
    {
        qDebug() << "警告：WebSocket未连接，将在连接成功后订阅";

        // 断开之前的连接以避免重复
        disconnect(m_webSocketClient, &WebSocketClient::connected, this, &CurrentOrder::onWebSocketConnected);

        // 连接WebSocket的connected信号，在连接成功后重试订阅
        // 使用成员函数代替lambda来解决UniqueConnection问题
        connect(m_webSocketClient, &WebSocketClient::connected, this, &CurrentOrder::onWebSocketConnected, Qt::UniqueConnection);

        // 发出重连请求
        emit requestReconnectWebSocket();

        // 通知状态变更，但实际订阅会在连接恢复后完成
        emit subscriptionStatusChanged(true);

        // 创建健康检查定时器
        QTimer *healthCheckTimer = new QTimer(this);
        connect(healthCheckTimer, &QTimer::timeout, this, [this, healthCheckTimer]()
                {
            if (!m_isSubscribed) {
                // 如果已取消订阅，停止定时器
                healthCheckTimer->stop();
                healthCheckTimer->deleteLater();
                return;
            }

            qDebug() << "订阅健康检查: 交易对=" << m_subscribedSymbol << ", 订阅价格=" << m_subscribedPrice
                     << ", WebSocket状态=" << (m_webSocketClient ? m_webSocketClient->state() : -1);

            // 如果WebSocket断开，尝试重新连接
            if (m_webSocketClient && m_webSocketClient->state() != QAbstractSocket::ConnectedState) {
                qDebug() << "检测到WebSocket未连接，尝试重新连接...";
                // 使用信号请求重新连接，而不是直接调用
                emit requestReconnectWebSocket();
            } });

        // 每30秒检查一次
        healthCheckTimer->start(30000);

        return true; // 返回true表示订阅请求已接收，稍后会完成
    }

    // WebSocket已连接，可以直接发送订阅请求
    emit requestSubscribeTradeStream(symbol);

    qDebug() << "已订阅交易对" << symbol << "的价格" << price << "的交易数据，价格精度范围±" << m_pricePrecision;
    emit subscriptionStatusChanged(true);

    // 创建健康检查定时器
    QTimer *healthCheckTimer = new QTimer(this);
    connect(healthCheckTimer, &QTimer::timeout, this, [this, healthCheckTimer]()
            {
        if (!m_isSubscribed) {
            // 如果已取消订阅，停止定时器
            healthCheckTimer->stop();
            healthCheckTimer->deleteLater();
            return;
        }

        qDebug() << "订阅健康检查: 交易对=" << m_subscribedSymbol << ", 订阅价格=" << m_subscribedPrice
                 << ", WebSocket状态=" << (m_webSocketClient ? m_webSocketClient->state() : -1);

        // 如果WebSocket断开，尝试重新连接
        if (m_webSocketClient && m_webSocketClient->state() != QAbstractSocket::ConnectedState) {
            qDebug() << "检测到WebSocket未连接，尝试重新连接...";
            // 使用信号请求重新连接，而不是直接调用
            emit requestReconnectWebSocket();
        } });

    // 每30秒检查一次
    healthCheckTimer->start(30000);

    return true;
}

// 取消订阅
bool CurrentOrder::unsubscribe()
{
    if (!m_isSubscribed)
    {
        qDebug() << "当前没有活跃的订阅";
        return false;
    }

    // 先记录当前订阅信息，用于日志
    QString oldSymbol = m_subscribedSymbol;
    double oldPrice = m_subscribedPrice;

    // 清除订阅状态
    m_isSubscribed = false;
    m_subscribedSymbol = "";
    m_subscribedPrice = 0.0;

    if (!m_webSocketClient)
    {
        qDebug() << "错误：WebSocketClient未设置，无法取消订阅";
        emit subscriptionStatusChanged(false);
        return false;
    }

    // 断开所有信号连接（只断开connect相关的连接，保留信号槽连接）
    disconnect(m_webSocketClient, &WebSocketClient::connected, this, nullptr);

    // 通过信号发送取消订阅请求，确保线程安全
    emit requestUnsubscribeTradeStream(oldSymbol);

    qDebug() << "已取消交易对" << oldSymbol << "的价格" << oldPrice << "的交易数据订阅";
    emit subscriptionStatusChanged(false);

    return true;
}

// 检查事件是否已处理过
bool CurrentOrder::isEventProcessed(const QString &eventKey)
{
    if (m_processedEvents.contains(eventKey))
    {
        qDebug() << "事件已处理过，忽略重复事件：" << eventKey;
        return true;
    }
    m_processedEvents.insert(eventKey);
    return false;
}

// 清理已处理事件集合
void CurrentOrder::cleanupProcessedEvents()
{
    if (m_processedEvents.size() > 1000)
    {
        qDebug() << "清理已处理事件集合，当前大小：" << m_processedEvents.size();
        m_processedEvents.clear();
    }
}
