@echo off
echo 正在部署Qt依赖文件...

REM 创建必要的目录
if not exist "Debug\sqldrivers" mkdir "Debug\sqldrivers"
if not exist "Debug\platforms" mkdir "Debug\platforms"

REM 复制SQLite驱动
echo 复制SQLite驱动...
copy "D:\StudyAndWork\Qt\6.5.3\msvc2019_64\plugins\sqldrivers\qsqlite.dll" "Debug\sqldrivers\" >nul
if %errorlevel% neq 0 (
    echo 错误：无法复制qsqlite.dll
    pause
    exit /b 1
)

REM 复制Qt6Sql.dll
echo 复制Qt6Sql.dll...
copy "D:\StudyAndWork\Qt\6.5.3\msvc2019_64\bin\Qt6Sql.dll" "Debug\" >nul
if %errorlevel% neq 0 (
    echo 错误：无法复制Qt6Sql.dll
    pause
    exit /b 1
)

REM 复制平台插件（可选，但建议）
echo 复制平台插件...
copy "D:\StudyAndWork\Qt\6.5.3\msvc2019_64\plugins\platforms\qwindows.dll" "Debug\platforms\" >nul

REM 复制其他必要的Qt DLL
echo 复制Qt核心库...
copy "D:\StudyAndWork\Qt\6.5.3\msvc2019_64\bin\Qt6Core.dll" "Debug\" >nul
copy "D:\StudyAndWork\Qt\6.5.3\msvc2019_64\bin\Qt6Gui.dll" "Debug\" >nul
copy "D:\StudyAndWork\Qt\6.5.3\msvc2019_64\bin\Qt6Widgets.dll" "Debug\" >nul
copy "D:\StudyAndWork\Qt\6.5.3\msvc2019_64\bin\Qt6Network.dll" "Debug\" >nul
copy "D:\StudyAndWork\Qt\6.5.3\msvc2019_64\bin\Qt6WebSockets.dll" "Debug\" >nul

echo Qt依赖文件部署完成！
echo.
echo 现在可以运行程序了。
pause
