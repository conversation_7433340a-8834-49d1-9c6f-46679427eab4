#ifndef UTILS_H
#define UTILS_H

#include <QString>
#include <QByteArray>
#include <QCryptographicHash>
#include <QDateTime>
#include <QUrlQuery>
#include <QSettings>

#define MAX_RETRY_COUNT 3
#define SELL_PRICE_UP   0.0001
const QString m_symbol = "USDCUSDT";
// const QString m_symbol = "BTCUSDT";
// const QString m_symbol = "ETHUSDT";
class Utils
{
public:
    // 生成HMAC SHA256签名
    static QByteArray generateHmacSha256(const QByteArray &key, const QByteArray &data);

    // 获取当前时间戳（毫秒）
    static QString getTimestamp();

    // 保存API密钥到配置文件
    static void saveApiKeys(const QString &apiKey, const QString &secretKey, bool remember);

    // 从配置文件加载API密钥
    static bool loadApiKeys(QString &apiKey, QString &secretKey);

    // 清除保存的API密钥
    static void clearApiKeys();

    // 检查是否已保存API密钥
    static bool hasApiKeys();
};

#endif // UTILS_H
