#ifndef APIWORKER_H
#define APIWORKER_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QString>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QWebSocket>
#include <QTimer>

// 前向声明ApiManager类
class ApiManager;
class WebSocketClient;

// API工作线程类
class ApiWorker : public QObject
{
    Q_OBJECT

public:
    explicit ApiWorker(QObject *parent = nullptr);
    ~ApiWorker();

    // 设置API密钥
    void setApiKeys(const QString &apiKey, const QString &secretKey);

    // 设置API权重输出阈值
    void setApiWeightOutputThreshold(int threshold);

    // 获取WebSocketClient实例
    WebSocketClient *getWebSocketClient() const;

    // 获取API权重信息
    QJsonObject getApiWeightInfo() const;

    // 获取详细API权重信息
    QJsonObject getDetailedApiWeightInfo() const;

    // 获取当前权重
    int getCurrentWeight() const;

    // 获取权重限制
    int getWeightLimit() const;

    // 获取订单数量
    int getOrderCount() const;

    // 获取5分钟权重
    int getCurrentWeight5m() const;

    // 获取1小时权重
    int getCurrentWeight1h() const;

    // 获取1天权重
    int getCurrentWeight1d() const;

public slots:
    // 在工作线程中初始化ApiManager
    void initApiManager();

    // 验证API密钥
    void validateApiKeys();

    // 获取账户信息
    void getAccountInfo();

    // 获取最佳挂单价格 - 通过REST API
    void getBestOrderBookTicker(const QString &symbol);

    // 订阅BookTicker流 - 通过WebSocket
    void subscribeBookTickerStream(const QString &symbol);

    // 取消订阅BookTicker流 - 通过WebSocket
    void unsubscribeBookTickerStream(const QString &symbol);

    // 订阅Trade流 - 通过WebSocket
    void subscribeTradeStream(const QString &symbol);

    // 取消订阅Trade流 - 通过WebSocket
    void unsubscribeTradeStream(const QString &symbol);

    // 获取最新成交价格
    void getLatestPrice(const QString &symbol);

    // 下单
    void placeOrder(const QString &symbol, const QString &side, const QString &type, const QString &quantity, const QString &price,
                    const QString &relatedOrderId = QString());

    // 取消订单
    void cancelOrder(const QString &symbol, const QString &orderId);

    // 获取订单状态
    void getOrderStatus(const QString &symbol, const QString &orderId);

    // 获取所有挂单
    void getOpenOrders(const QString &symbol);

    // 启动WebSocket连接，接收实时订单更新
    void startUserDataStream();

    // 停止WebSocket连接
    void stopUserDataStream();

    // 获取订单簿深度数据
    void getOrderBookDepth(const QString &symbol, int limit = 20);

signals:
    // 转发ApiManager的信号
    void apiKeysValidated(bool valid, const QString &errorMessage);
    void accountInfoUpdated(const QJsonObject &accountInfo);
    void accountInfoUpdateForWebsocket(double usdc, double usdc_locked, double usdt, double usdt_locked, QString updateTime);
    void bestOrderBookTickerUpdated(const QJsonObject &ticker);
    void latestPriceUpdated(const QJsonObject &priceInfo);
    void orderPlaced(bool success, const QJsonObject &orderInfo, const QString &errorMessage);
    void orderCancelled(bool success, const QJsonObject &orderInfo, const QString &errorMessage);
    void currentOrderStatusUpdated(QString orderId, QString symbol, QString side, QString status, qint64 updateTime);
    void openOrdersReceived(bool success, const QJsonArray &orders, const QString &errorMessage);
    void orderUpdateReceived(const QJsonObject &orderUpdate);
    void orderBookDepthUpdated(const QJsonObject &depthData);
    void sg_sendMs(quint64 ms);

private:
    // ApiManager实例
    ApiManager *m_apiManager;

    // API密钥
    QString m_apiKey;
    QString m_secretKey;
};

#endif // APIWORKER_H
