{"version": "2.0.0", "tasks": [{"label": "Qt: Build & Run Debug", "detail": "⭐ 构建并运行 Debug 版本 - 日常开发首选", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/qt_build_and_run.ps1", "debug"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Qt: Build & Run Release", "detail": "⭐ 构建并运行 Release 版本 - 发布测试", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/qt_build_and_run.ps1", "release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Qt: Clean All", "detail": "🧹 清理所有构建文件", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/qt_build_and_run.ps1", "clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Qt: <PERSON>g", "detail": "🚀 运行 Debug 版本应用程序", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/run_only.ps1", "debug"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Qt: Run Release", "detail": "🚀 运行 Release 版本应用程序", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/run_only.ps1", "release"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Qt: De<PERSON>loy Debug", "detail": "📦 部署 Debug 版本的 Qt 库文件", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/deploy_qt.ps1", "debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Qt: Deploy Release", "detail": "📦 部署 Release 版本的 Qt 库文件", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/deploy_qt.ps1", "release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}