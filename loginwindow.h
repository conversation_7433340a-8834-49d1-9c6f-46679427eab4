#ifndef LOGINWINDOW_H
#define LOGINWINDOW_H

#include <QMainWindow>
#include <QThread>
#include "apiworker.h"
#include "mainwindow.h"

namespace Ui {
class LoginWindow;
}

class LoginWindow : public QMainWindow
{
    Q_OBJECT
    
public:
    explicit LoginWindow(QWidget *parent = nullptr);
    ~LoginWindow();
    
private slots:
    // 登录按钮点击事件
    void on_loginButton_clicked();
    
    // 重置按钮点击事件
    void on_resetButton_clicked();
    
    // 退出按钮点击事件
    void on_exitButton_clicked();
    
    // API密钥验证结果处理
    void handleApiKeysValidated(bool valid, const QString &errorMessage);
    
private:
    Ui::LoginWindow *ui;
    
    // API工作线程
    QThread *m_apiThread;
    
    // API工作对象
    ApiWorker *m_apiWorker;
    
    // 主窗口
    MainWindow *m_mainWindow;
    
    // 加载保存的API密钥
    void loadSavedApiKeys();
    
    // 保存API密钥
    void saveApiKeys(const QString &apiKey, const QString &secretKey, bool remember);

protected:
    // 关闭事件处理
    void closeEvent(QCloseEvent *event) override;
};

#endif // LOGINWINDOW_H 