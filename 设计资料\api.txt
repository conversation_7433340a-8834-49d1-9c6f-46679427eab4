# 币安API文档
文档地址：https://developers.binance.com/docs/binance-spot-api-docs

## 1. 账户API
- 系统状态 (System Status): GET /sapi/v1/system/status
  - 获取系统状态信息

- 账户信息 (Account Information): GET /api/v3/account
  - 获取账户信息，包括余额、权限等
  - 需要签名

- 账户余额 (Account Balance): GET /api/v3/balance
  - 获取账户余额信息
  - 需要签名

## 2. 市场API
- 交易对信息 (Exchange Information): GET /api/v3/exchangeInfo
  - 获取交易规则和交易对信息

- 深度信息 (Order Book): GET /api/v3/depth
  - 获取订单簿深度信息
  - 参数：symbol (必须)，limit (可选)

- 最新价格 (Latest Price): GET /api/v3/ticker/price
  - 获取交易对最新价格
  - 参数：symbol (可选)

- 最佳挂单 (Best Order Book Ticker): GET /api/v3/ticker/bookTicker
  - 获取最优挂单价格
  - 参数：symbol (可选)

## 3. 交易API
- 下单 (New Order): POST /api/v3/order
  - 创建新订单
  - 需要签名
  - 主要参数：symbol, side, type, quantity, price等

- 查询订单 (Query Order): GET /api/v3/order
  - 查询订单状态
  - 需要签名
  - 参数：symbol (必须), orderId或origClientOrderId (必须其一)

- 取消订单 (Cancel Order): DELETE /api/v3/order
  - 取消订单
  - 需要签名
  - 参数：symbol (必须), orderId或origClientOrderId (必须其一)

- 查询所有订单 (All Orders): GET /api/v3/allOrders
  - 获取所有订单信息
  - 需要签名
  - 参数：symbol (必须)，其他可选参数

## 4. 其他API
- Websocket市场数据流：wss://stream.binance.com:9443/ws
  - 订阅市场数据，如深度信息、K线数据等
  - 可以用于实时监控价格变化

- 用户数据流 (User Data Stream): 
  - 创建数据流：POST /api/v3/userDataStream
  - 保持数据流有效：PUT /api/v3/userDataStream
  - 关闭数据流：DELETE /api/v3/userDataStream
  - 用于接收账户和订单更新的实时信息 