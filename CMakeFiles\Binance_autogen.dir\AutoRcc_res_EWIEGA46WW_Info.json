{"BUILD_DIR": "D:/StudyAndWork/QtProjects/Binance/Binance_autogen", "CMAKE_BINARY_DIR": "D:/StudyAndWork/QtProjects/Binance", "CMAKE_CURRENT_BINARY_DIR": "D:/StudyAndWork/QtProjects/Binance", "CMAKE_CURRENT_SOURCE_DIR": "D:/StudyAndWork/QtProjects/Binance", "CMAKE_SOURCE_DIR": "D:/StudyAndWork/QtProjects/Binance", "CROSS_CONFIG": false, "GENERATOR": "Visual Studio 17 2022", "INCLUDE_DIR": "D:/StudyAndWork/QtProjects/Binance/Binance_autogen/include", "INCLUDE_DIR_Debug": "D:/StudyAndWork/QtProjects/Binance/Binance_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "D:/StudyAndWork/QtProjects/Binance/Binance_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "D:/StudyAndWork/QtProjects/Binance/Binance_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "D:/StudyAndWork/QtProjects/Binance/Binance_autogen/include_Release", "INPUTS": ["D:/StudyAndWork/QtProjects/Binance/USDTIcons-001.ico", "D:/StudyAndWork/QtProjects/Binance/资源/wait.mp3", "D:/StudyAndWork/QtProjects/Binance/资源/success.mp3", "D:/StudyAndWork/QtProjects/Binance/资源/gettingMoney.mp3"], "LOCK_FILE": "D:/StudyAndWork/QtProjects/Binance/CMakeFiles/Binance_autogen.dir/AutoRcc_res_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": true, "OPTIONS": ["--no-zstd", "-name", "res"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_res.cpp", "RCC_EXECUTABLE": "D:/StudyAndWork/Qt/6.5.3/msvc2019_64/./bin/rcc.exe", "RCC_LIST_OPTIONS": [], "SETTINGS_FILE": "D:/StudyAndWork/QtProjects/Binance/CMakeFiles/Binance_autogen.dir/AutoRcc_res_EWIEGA46WW_Used.txt", "SETTINGS_FILE_Debug": "D:/StudyAndWork/QtProjects/Binance/CMakeFiles/Binance_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_Debug.txt", "SETTINGS_FILE_MinSizeRel": "D:/StudyAndWork/QtProjects/Binance/CMakeFiles/Binance_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "D:/StudyAndWork/QtProjects/Binance/CMakeFiles/Binance_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "D:/StudyAndWork/QtProjects/Binance/CMakeFiles/Binance_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_Release.txt", "SOURCE": "D:/StudyAndWork/QtProjects/Binance/res.qrc", "USE_BETTER_GRAPH": false, "VERBOSITY": 0}