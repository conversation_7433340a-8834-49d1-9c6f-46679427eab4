# Qt Application Runner (Run Only)
param(
    [string]$BuildType = "debug"
)

Write-Host ""
Write-Host "========================================"
Write-Host "        Qt Application Runner"
Write-Host "========================================"
Write-Host ""

# Determine executable path (check multiple possible locations)
$PossiblePaths = @(
    "Binance\$BuildType\Binance.exe",
    "build\Binance\$BuildType\Binance.exe",
    "build\$BuildType\Binance.exe"
)

$ExePath = $null
foreach ($path in $PossiblePaths) {
    if (Test-Path $path) {
        $ExePath = $path
        break
    }
}

# Check if executable exists and run it
if ($ExePath -and (Test-Path $ExePath)) {
    Write-Host "OK Found executable: $ExePath" -ForegroundColor Green
    Write-Host "OK Starting $BuildType version..." -ForegroundColor Green
    Write-Host ""

    # Launch the application
    Start-Process -FilePath $ExePath

    Write-Host "OK Application launched successfully!" -ForegroundColor Green
} else {
    Write-Host "X Executable not found in any of these locations:" -ForegroundColor Red
    foreach ($path in $PossiblePaths) {
        $exists = if (Test-Path $path) { "OK" } else { "X" }
        Write-Host "  $exists $path" -ForegroundColor $(if (Test-Path $path) { "Green" } else { "Red" })
    }
    Write-Host ""
    Write-Host "Please build the application first using:" -ForegroundColor Yellow
    Write-Host "  - Ctrl+R (Build & Run Debug)" -ForegroundColor Yellow
    Write-Host "  - Or run: powershell -File qt_build_and_run.ps1 $BuildType" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================"
Write-Host ""
