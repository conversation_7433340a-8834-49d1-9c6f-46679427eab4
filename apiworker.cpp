#include "apiworker.h"
#include "apimanager.h"
#include "websocketclient.h"
#include <QThread>
#include <QDebug>
#include <QMetaObject>

// ApiWorker类实现
ApiWorker::ApiWorker(QObject *parent) : QObject(parent), m_apiManager(nullptr)
{
    // ApiManager对象将在工作线程中创建
    qDebug() << "ApiWorker已创建，ApiManager将在工作线程中创建";
}

ApiWorker::~ApiWorker()
{
    // 断开所有信号连接，避免后续操作触发任何回调
    disconnect(this, nullptr, nullptr, nullptr);

    // 停止WebSocket连接 - 但不直接访问ApiManager的方法
    // 安全地处理ApiManager对象 - 不直接删除对象
    if (m_apiManager)
    {
        // 断开所有与ApiManager的连接
        m_apiManager->disconnect();

        // 让ApiManager自行处理资源释放
        if (m_apiManager->thread() == thread())
        {
            // 如果在同一线程中，可以安排延迟删除
            m_apiManager->deleteLater();
        }
        else
        {
            // 否则，将删除操作投递到ApiManager所属的线程
            QMetaObject::invokeMethod(m_apiManager, "deleteLater", Qt::QueuedConnection);
        }

        // 立即将指针置空，避免后续访问
        m_apiManager = nullptr;
        qDebug().noquote() << "ApiManager对象已安排删除";
    }

    qDebug().noquote() << "ApiWorker析构函数执行完毕";
}

void ApiWorker::setApiKeys(const QString &apiKey, const QString &secretKey)
{
    // 设置API密钥
    m_apiKey = apiKey;
    m_secretKey = secretKey;

    if (m_apiManager)
    {
        m_apiManager->setApiKeys(apiKey, secretKey);
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法设置API密钥";
    }
}

void ApiWorker::setApiWeightOutputThreshold(int threshold)
{
    if (m_apiManager)
    {
        m_apiManager->setApiWeightOutputThreshold(threshold);
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法设置API权重输出阈值";
    }
}

// 在工作线程中初始化ApiManager
void ApiWorker::initApiManager()
{
    // 确保在工作线程中创建ApiManager
    qDebug() << "正在工作线程" << QThread::currentThread() << "中初始化ApiManager";

    // 创建ApiManager对象
    m_apiManager = new ApiManager();

    // 如果已经设置了API密钥，则传递给ApiManager
    if (!m_apiKey.isEmpty() && !m_secretKey.isEmpty())
    {
        m_apiManager->setApiKeys(m_apiKey, m_secretKey);
    }

    // 连接ApiManager的信号到ApiWorker的信号
    connect(m_apiManager, &ApiManager::sg_sendMs, this, &ApiWorker::sg_sendMs);
    connect(m_apiManager, &ApiManager::apiKeysValidated, this, &ApiWorker::apiKeysValidated);
    connect(m_apiManager, &ApiManager::accountInfoUpdated, this, &ApiWorker::accountInfoUpdated);
    connect(m_apiManager, &ApiManager::accountInfoUpdateForWebsocket, this, &ApiWorker::accountInfoUpdateForWebsocket);
    connect(m_apiManager, &ApiManager::bestOrderBookTickerUpdated, this, &ApiWorker::bestOrderBookTickerUpdated);
    connect(m_apiManager, &ApiManager::latestPriceUpdated, this, &ApiWorker::latestPriceUpdated);
    connect(m_apiManager, &ApiManager::orderPlaced, this, &ApiWorker::orderPlaced);
    connect(m_apiManager, &ApiManager::orderCancelled, this, &ApiWorker::orderCancelled);
    connect(m_apiManager, &ApiManager::orderStatusUpdated, this, &ApiWorker::currentOrderStatusUpdated);
    connect(m_apiManager, &ApiManager::orderUpdateReceived, this, &ApiWorker::orderUpdateReceived);
    connect(m_apiManager, &ApiManager::openOrdersReceived, this, &ApiWorker::openOrdersReceived);
    connect(m_apiManager, &ApiManager::orderBookDepthUpdated, this, &ApiWorker::orderBookDepthUpdated);

    qDebug() << "ApiManager已在工作线程" << QThread::currentThread() << "中初始化完成";
}

void ApiWorker::validateApiKeys()
{
    // 验证API密钥
    if (m_apiManager)
    {
        m_apiManager->validateApiKeys();
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法验证API密钥";
        // 发出验证失败信号
        emit apiKeysValidated(false, "ApiManager未初始化");
    }
}

void ApiWorker::getAccountInfo()
{
    // 获取账户信息
    if (m_apiManager)
    {
        m_apiManager->getAccountInfo();
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法获取账户信息";
    }
}

void ApiWorker::getBestOrderBookTicker(const QString &symbol)
{
    // 获取最佳挂单价格
    if (m_apiManager)
    {
        m_apiManager->getBestOrderBookTicker(symbol);
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法获取最佳挂单价格";
    }
}

void ApiWorker::getLatestPrice(const QString &symbol)
{
    // 获取最新成交价格
    if (m_apiManager)
    {
        m_apiManager->getLatestPrice(symbol);
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法获取最新成交价格";
    }
}

void ApiWorker::placeOrder(const QString &symbol, const QString &side, const QString &type, const QString &quantity, const QString &price,
                           const QString &relatedOrderId)
{
    // 下单
    if (m_apiManager)
    {
        m_apiManager->placeOrder(symbol, side, type, quantity, price, relatedOrderId);
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法下单";
        // 发出下单失败信号
        emit orderPlaced(false, QJsonObject(), "ApiManager未初始化");
    }
}

void ApiWorker::cancelOrder(const QString &symbol, const QString &orderId)
{
    // 取消订单
    if (m_apiManager)
    {
        m_apiManager->cancelOrder(symbol, orderId);
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法取消订单";
        // 发出取消订单失败信号
        emit orderCancelled(false, QJsonObject(), "ApiManager未初始化");
    }
}

void ApiWorker::getOrderStatus(const QString &symbol, const QString &orderId)
{
    // 获取订单状态
    if (m_apiManager)
    {
        m_apiManager->getOrderStatus(symbol, orderId);
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法获取订单状态";
    }
}

void ApiWorker::getOpenOrders(const QString &symbol)
{
    // 获取所有挂单
    if (m_apiManager)
    {
        m_apiManager->getOpenOrders(symbol);
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法获取所有挂单";
    }
}

void ApiWorker::startUserDataStream()
{
    // 确保ApiManager已初始化
    if (m_apiManager)
    {
        // 调用ApiManager的相应方法
        QMetaObject::invokeMethod(m_apiManager, "startUserDataStream", Qt::QueuedConnection);
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法启动WebSocket连接";
    }
}

void ApiWorker::stopUserDataStream()
{
    // 确保ApiManager已初始化
    if (m_apiManager)
    {
        // 调用ApiManager的相应方法
        QMetaObject::invokeMethod(m_apiManager, "stopUserDataStream", Qt::QueuedConnection);
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法停止WebSocket连接";
    }
}

void ApiWorker::getOrderBookDepth(const QString &symbol, int limit)
{
    // 确保ApiManager已初始化
    if (m_apiManager)
    {
        // 调用ApiManager的相应方法
        QMetaObject::invokeMethod(m_apiManager, "getOrderBookDepth", Qt::QueuedConnection, Q_ARG(QString, symbol), Q_ARG(int, limit));
    }
    else
    {
        qDebug() << "错误：ApiManager未初始化，无法获取订单簿深度数据";
    }
}

// 获取WebSocketClient实例
WebSocketClient *ApiWorker::getWebSocketClient() const
{
    if (m_apiManager)
    {
        return m_apiManager->getWebSocketClient();
    }
    return nullptr;
}

// 订阅BookTicker流 - 通过WebSocket
void ApiWorker::subscribeBookTickerStream(const QString &symbol)
{
    // 直接调用ApiManager对应的方法
    m_apiManager->subscribeBookTickerStream(symbol);
}

// 取消订阅BookTicker流 - 通过WebSocket
void ApiWorker::unsubscribeBookTickerStream(const QString &symbol)
{
    // 直接调用ApiManager对应的方法
    m_apiManager->unsubscribeBookTickerStream(symbol);
}

// 订阅Trade流 - 通过WebSocket
void ApiWorker::subscribeTradeStream(const QString &symbol)
{
    // 获取WebSocketClient实例
    WebSocketClient *webSocketClient = getWebSocketClient();
    if (webSocketClient)
    {
        // 直接调用WebSocketClient的订阅方法
        webSocketClient->subscribeTradeStream(symbol);
    }
    else
    {
        qDebug() << "错误：WebSocketClient未初始化，无法订阅Trade流";
    }
}

// 取消订阅Trade流 - 通过WebSocket
void ApiWorker::unsubscribeTradeStream(const QString &symbol)
{
    // 获取WebSocketClient实例
    WebSocketClient *webSocketClient = getWebSocketClient();
    if (webSocketClient)
    {
        // 直接调用WebSocketClient的取消订阅方法
        webSocketClient->unsubscribeTradeStream(symbol);
    }
    else
    {
        qDebug() << "错误：WebSocketClient未初始化，无法取消订阅Trade流";
    }
}

// 添加API权重信息相关方法的实现
QJsonObject ApiWorker::getApiWeightInfo() const
{
    if (m_apiManager)
    {
        return m_apiManager->getApiWeightInfo();
    }
    return QJsonObject(); // 如果ApiManager未初始化，返回空对象
}

int ApiWorker::getCurrentWeight() const
{
    if (m_apiManager)
    {
        return m_apiManager->getCurrentWeight();
    }
    return 0; // 如果ApiManager未初始化，返回0
}

int ApiWorker::getWeightLimit() const
{
    if (m_apiManager)
    {
        return m_apiManager->getWeightLimit();
    }
    return 1200; // 如果ApiManager未初始化，返回默认值1200
}

int ApiWorker::getOrderCount() const
{
    if (m_apiManager)
    {
        return m_apiManager->getOrderCount();
    }
    return 0; // 如果ApiManager未初始化，返回0
}

// 实现获取详细API权重信息的方法
QJsonObject ApiWorker::getDetailedApiWeightInfo() const
{
    if (m_apiManager)
    {
        return m_apiManager->getDetailedApiWeightInfo();
    }
    // 如果ApiManager未初始化，返回带有基本信息的对象
    QJsonObject emptyInfo;
    emptyInfo["currentWeight1m"] = 0;
    emptyInfo["weightLimit1m"] = 1200;
    emptyInfo["weightPercentage1m"] = 0.0;
    emptyInfo["currentWeight5m"] = 0;
    emptyInfo["weightLimit5m"] = 6000;
    emptyInfo["weightPercentage5m"] = 0.0;
    emptyInfo["currentWeight1h"] = 0;
    emptyInfo["weightLimit1h"] = 60000;
    emptyInfo["weightPercentage1h"] = 0.0;
    emptyInfo["currentWeight1d"] = 0;
    emptyInfo["weightLimit1d"] = 1000000;
    emptyInfo["weightPercentage1d"] = 0.0;
    emptyInfo["orderCount"] = 0;
    return emptyInfo;
}

// 实现获取5分钟权重的方法
int ApiWorker::getCurrentWeight5m() const
{
    if (m_apiManager)
    {
        return m_apiManager->getCurrentWeight5m();
    }
    return 0; // 如果ApiManager未初始化，返回0
}

// 实现获取1小时权重的方法
int ApiWorker::getCurrentWeight1h() const
{
    if (m_apiManager)
    {
        return m_apiManager->getCurrentWeight1h();
    }
    return 0; // 如果ApiManager未初始化，返回0
}

// 实现获取1天权重的方法
int ApiWorker::getCurrentWeight1d() const
{
    if (m_apiManager)
    {
        return m_apiManager->getCurrentWeight1d();
    }
    return 0; // 如果ApiManager未初始化，返回0
}
