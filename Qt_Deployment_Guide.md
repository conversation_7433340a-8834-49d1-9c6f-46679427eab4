# Qt应用程序部署指南

## 🤔 为什么需要Qt部署？

### 问题现象
- ✅ Debug版本可以运行
- ❌ Release版本无法运行或立即退出
- ❌ 在没有Qt开发环境的电脑上无法运行

### 根本原因
Qt应用程序需要依赖Qt库文件（DLL）才能运行：
- **开发环境**: 系统PATH中包含Qt库路径，可以找到DLL
- **Release版本**: 需要将Qt库文件复制到应用程序目录
- **其他电脑**: 没有Qt开发环境，必须包含所有依赖库

## 🛠️ 解决方案：使用windeployqt

### 什么是windeployqt？
- Qt官方提供的部署工具
- 自动检测应用程序的Qt依赖
- 复制必要的Qt库文件到应用程序目录
- 包含插件、翻译文件等

### 自动部署（推荐）
现在的构建脚本已经集成了自动部署功能：

```bash
# 构建并自动部署
powershell -ExecutionPolicy Bypass -File qt_build_and_run.ps1 release
```

**Ctrl+R** 也会自动部署Qt库！

### 手动部署
如果需要单独部署：

```bash
# 部署Debug版本
powershell -ExecutionPolicy Bypass -File deploy_qt.ps1 debug

# 部署Release版本
powershell -ExecutionPolicy Bypass -File deploy_qt.ps1 release
```

### VSCode任务部署
1. **Ctrl+Shift+P** → "Tasks: Run Task"
2. 选择：
   - `Qt: Deploy Debug` - 部署Debug版本
   - `Qt: Deploy Release` - 部署Release版本

## 📁 部署后的目录结构

### 部署前
```
Binance\Release\
├── Binance.exe          # 只有可执行文件
```

### 部署后
```
Binance\Release\
├── Binance.exe          # 应用程序
├── Qt6Core.dll          # Qt核心库
├── Qt6Gui.dll           # Qt GUI库
├── Qt6Widgets.dll       # Qt控件库
├── Qt6Network.dll       # Qt网络库
├── Qt6WebSockets.dll    # Qt WebSocket库
├── opengl32sw.dll       # OpenGL软件渲染
├── D3Dcompiler_47.dll   # DirectX编译器
├── platforms\           # 平台插件
│   └── qwindows.dll
├── imageformats\        # 图像格式插件
│   ├── qjpeg.dll
│   ├── qpng.dll
│   └── ...
├── styles\              # 样式插件
├── translations\        # 翻译文件
└── ...                  # 其他插件和库
```

## ✅ 部署验证

### 检查部署是否成功
1. **查看文件**: Release目录应包含多个Qt DLL文件
2. **运行测试**: 应用程序能正常启动和显示UI
3. **功能测试**: 所有功能正常工作

### 常见问题排查

#### 问题1：应用程序仍然无法运行
**可能原因**：
- 缺少Visual C++运行时库
- 缺少其他系统依赖

**解决方案**：
```bash
# 使用--compiler-runtime参数（已默认包含）
windeployqt --release --compiler-runtime Binance.exe
```

#### 问题2：部署文件过大
**说明**：这是正常的，Release版本包含所有依赖库
- Debug版本：~100MB+
- Release版本：~50MB+

#### 问题3：在其他电脑上仍无法运行
**可能需要**：
- Visual C++ Redistributable
- Windows更新
- 特定的系统库

## 🚀 最佳实践

### 开发阶段
- 使用 **Ctrl+R** 构建并运行Debug版本
- 自动部署功能确保应用程序能正常运行

### 测试阶段
- 构建Release版本并部署
- 在干净的环境中测试

### 发布阶段
- 使用Release版本
- 包含所有部署文件
- 提供安装程序或压缩包

## 📋 部署检查清单

- [ ] 构建Release版本成功
- [ ] 运行windeployqt部署
- [ ] 检查Qt DLL文件存在
- [ ] 应用程序能正常启动
- [ ] 所有功能正常工作
- [ ] 在其他电脑上测试（可选）

## 🔧 技术细节

### windeployqt参数说明
- `--release`: 部署Release版本库
- `--debug`: 部署Debug版本库
- `--compiler-runtime`: 包含编译器运行时
- `--force`: 强制覆盖现有文件

### 自动检测的依赖
- Qt模块（Core, Gui, Widgets等）
- 平台插件（Windows平台支持）
- 图像格式插件（JPEG, PNG等）
- 样式插件（Windows Vista样式等）
- 翻译文件（多语言支持）

---

**现在您的Qt应用程序已经正确部署，Release版本可以正常运行了！** 🎉
